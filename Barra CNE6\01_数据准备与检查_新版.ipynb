{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 01 数据准备与检查（新版）\n", "\n", "本notebook完成以下任务：\n", "1. 加载各种数据源（包括原始因子数据）\n", "2. 数据格式统一\n", "3. 筛选有效股票样本\n", "4. 数据质量检查\n", "5. 保存处理后的数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据准备与检查模块开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from datetime import datetime\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"数据准备与检查模块开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.1 加载原始数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载原始因子数据...\n", "原始因子数据形状: (9342609, 4)\n", "原始因子数据列: ['trade_date', 'ts_code', 'pb', 'total_mv']\n", "\n", "原始因子数据前5行:\n", "  trade_date    ts_code       pb      total_mv\n", "0 2015-12-31  000001.SZ   1.0918  1.715610e+07\n", "1 2015-12-31  000004.SZ  46.5911  3.854530e+05\n", "2 2015-12-31  000005.SZ   9.1784  1.062771e+06\n", "3 2015-12-31  000006.SZ   3.6543  1.553844e+06\n", "4 2015-12-31  000008.SZ  10.0755  2.838312e+06\n", "\n", "PB因子统计:\n", "count    9.283838e+06\n", "mean     4.925590e+00\n", "std      8.188557e+01\n", "min      2.260000e-02\n", "25%      1.672900e+00\n", "50%      2.620800e+00\n", "75%      4.254400e+00\n", "max      4.572357e+04\n", "Name: pb, dtype: float64\n", "PB因子缺失值: 58771\n", "PB因子负值数量: 0\n", "\n", "市值统计:\n", "count    9.342609e+06\n", "mean     1.808843e+06\n", "std      7.681744e+06\n", "min      2.543354e+03\n", "25%      3.237789e+05\n", "50%      5.665244e+05\n", "75%      1.224777e+06\n", "max      3.267370e+08\n", "Name: total_mv, dtype: float64\n", "市值缺失值: 0\n", "市值负值数量: 0\n"]}], "source": ["# 1. 加载原始因子数据（PB和市值）\n", "print(\"加载原始因子数据...\")\n", "factor_data = pd.read_hdf('data/factor.h5', key='data')\n", "factor_data = factor_data.reset_index()\n", "print(f\"原始因子数据形状: {factor_data.shape}\")\n", "print(f\"原始因子数据列: {factor_data.columns.tolist()}\")\n", "print(\"\\n原始因子数据前5行:\")\n", "print(factor_data.head())\n", "\n", "# 检查原始因子数据质量\n", "print(f\"\\nPB因子统计:\")\n", "print(factor_data['pb'].describe())\n", "print(f\"PB因子缺失值: {factor_data['pb'].isnull().sum()}\")\n", "print(f\"PB因子负值数量: {(factor_data['pb'] < 0).sum()}\")\n", "\n", "print(f\"\\n市值统计:\")\n", "print(factor_data['total_mv'].describe())\n", "print(f\"市值缺失值: {factor_data['total_mv'].isnull().sum()}\")\n", "print(f\"市值负值数量: {(factor_data['total_mv'] < 0).sum()}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "加载股票收益数据...\n", "股票数据形状: (9118319, 11)\n", "股票数据列: ['trade_date', 'ts_code', 'close', 'simple_return', 'log_return', 'adj_factor', 'adj_close', 'return', 'return_t1', 'pb', 'total_mv']\n", "\n", "股票数据前5行:\n", "  trade_date    ts_code  close  simple_return  log_return  adj_factor  \\\n", "0 2015-12-31  000001.SZ  11.99      -0.009091   -0.009132      85.994   \n", "1 2016-01-04  000001.SZ  11.33      -0.055046   -0.056619      85.994   \n", "2 2016-01-05  000001.SZ  11.40       0.006178    0.006159      85.994   \n", "3 2016-01-06  000001.SZ  11.53       0.011404    0.011339      85.994   \n", "4 2016-01-07  000001.SZ  10.94      -0.051171   -0.052527      85.994   \n", "\n", "    adj_close    return  return_t1        pb  total_mv  \n", "0  1031.06806 -0.009091  -0.055046 -1.019131  1.898023  \n", "1   974.31202 -0.055046   0.006178 -1.047173  1.898023  \n", "2   980.33160  0.006178   0.011404 -1.044187  1.898023  \n", "3   991.51082  0.011404  -0.051171 -1.038682  1.898023  \n", "4   940.77436 -0.051171   0.016453 -1.063737  1.898023  \n"]}], "source": ["# 2. 加载股票收益数据\n", "print(\"\\n加载股票收益数据...\")\n", "stock_data = pd.read_hdf('data/merged_data_871.h5', key='data')\n", "print(f\"股票数据形状: {stock_data.shape}\")\n", "print(f\"股票数据列: {stock_data.columns.tolist()}\")\n", "print(\"\\n股票数据前5行:\")\n", "print(stock_data.head())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "加载行业分类数据...\n", "行业分类数据形状: (7827, 10)\n", "行业分类数据列: ['ts_code', 'name', 'in_date', 'out_date', 'l1_code', 'l1_name', 'l2_code', 'l2_name', 'l3_code', 'l3_name']\n", "\n", "行业分类数据前5行:\n", "     ts_code       name   in_date    out_date    l1_code l1_name    l2_code  \\\n", "0  000001.SZ       平安银行  19910403         NaN  801780.SI      银行  801783.SI   \n", "1  000002.SZ        万科A  19910129         NaN  801180.SI     房地产  801181.SI   \n", "2  000003.SZ  PT金田A(退市)  19910415         NaN  801230.SI      综合  801231.SI   \n", "3  000004.SZ      *ST国华  19891223  20081230.0  801180.SI     房地产  801181.SI   \n", "4  000004.SZ      *ST国华  20081231  20210610.0  801150.SI    医药生物  801151.SI   \n", "\n", "  l2_name    l3_code l3_name  \n", "0  股份制银行Ⅱ  857831.SI  股份制银行Ⅲ  \n", "1   房地产开发  851811.SI    住宅开发  \n", "2     综合Ⅱ  852311.SI     综合Ⅲ  \n", "3   房地产开发  851811.SI    住宅开发  \n", "4    化学制药  851512.SI    化学制剂  \n"]}], "source": ["# 3. 加载行业分类数据\n", "print(\"\\n加载行业分类数据...\")\n", "swind_data = pd.read_excel('data/swind.xlsx')\n", "print(f\"行业分类数据形状: {swind_data.shape}\")\n", "print(f\"行业分类数据列: {swind_data.columns.tolist()}\")\n", "print(\"\\n行业分类数据前5行:\")\n", "print(swind_data.head())"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "加载上市退市日期数据...\n", "上市退市数据形状: (5653, 3)\n", "上市退市数据列: ['ts_code', 'list_date', 'delist_date']\n", "\n", "上市退市数据前5行:\n", "     ts_code   list_date delist_date\n", "0  000001.SZ    1991/4/3           0\n", "1  000002.SZ   1991/1/29           0\n", "2  000004.SZ   1990/12/1           0\n", "3  000005.SZ  1990/12/10   2024/4/26\n", "4  000006.SZ   1992/4/27           0\n"]}], "source": ["# 4. 加载上市退市日期数据\n", "print(\"\\n加载上市退市日期数据...\")\n", "ipo_data = pd.read_csv('data/ipodate.csv')\n", "print(f\"上市退市数据形状: {ipo_data.shape}\")\n", "print(f\"上市退市数据列: {ipo_data.columns.tolist()}\")\n", "print(\"\\n上市退市数据前5行:\")\n", "print(ipo_data.head())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "加载沪深300指数数据...\n", "指数数据形状: (5574, 2)\n", "指数数据列: ['date', 'close']\n", "\n", "指数数据前5行:\n", "        date     close\n", "0   2000/1/4  1406.371\n", "1   2000/1/5  1409.682\n", "2   2000/1/6  1463.942\n", "3   2000/1/7  1516.604\n", "4  2000/1/10  1545.112\n"]}], "source": ["# 5. 加载沪深300指数数据\n", "print(\"\\n加载沪深300指数数据...\")\n", "index_data = pd.read_csv('data/index.csv')\n", "print(f\"指数数据形状: {index_data.shape}\")\n", "print(f\"指数数据列: {index_data.columns.tolist()}\")\n", "print(\"\\n指数数据前5行:\")\n", "print(index_data.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.2 数据格式统一"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "统一日期格式...\n", "日期格式统一完成\n"]}], "source": ["# 统一日期格式\n", "print(\"\\n统一日期格式...\")\n", "\n", "# 处理因子数据\n", "factor_data['trade_date'] = pd.to_datetime(factor_data['trade_date'])\n", "factor_data['stock_code'] = factor_data['ts_code']\n", "\n", "# 处理股票收益数据\n", "stock_data['trade_date'] = pd.to_datetime(stock_data['trade_date'])\n", "stock_data['stock_code'] = stock_data['ts_code']\n", "\n", "# 处理行业分类数据\n", "swind_data['stock_code'] = swind_data['ts_code']\n", "swind_data = swind_data.rename(columns={'l1_code': 'sw_ind_code'})\n", "\n", "# 处理上市退市日期数据\n", "ipo_data = ipo_data.rename(columns={'list_date': 'in_date', 'delist_date': 'out_date'})\n", "ipo_data['in_date'] = pd.to_datetime(ipo_data['in_date'], format='%Y/%m/%d', errors='coerce')\n", "ipo_data['out_date'] = pd.to_datetime(ipo_data['out_date'], format='%Y/%m/%d', errors='coerce')\n", "ipo_data.loc[ipo_data['out_date'].dt.year == 1970, 'out_date'] = pd.NaT\n", "ipo_data['stock_code'] = ipo_data['ts_code']\n", "\n", "# 处理指数数据\n", "index_data = index_data.rename(columns={'date': 'trade_date'})\n", "index_data['trade_date'] = pd.to_datetime(index_data['trade_date'], format='%Y/%m/%d')\n", "index_data = index_data.sort_values('trade_date')\n", "index_data['hs300_return'] = index_data['close'].pct_change()\n", "index_data = index_data.dropna()  # 删除第一行的NaN收益率\n", "\n", "print(\"日期格式统一完成\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.3 合并数据"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "合并所有数据...\n", "合并因子数据和收益率数据...\n", "合并后数据形状: (9118319, 6)\n", "合并沪深300指数收益率...\n", "合并指数后数据形状: (9118319, 7)\n", "合并行业分类数据...\n", "合并行业后数据形状: (13133567, 8)\n", "合并上市退市日期...\n", "最终合并数据形状: (13133567, 10)\n", "最终数据列: ['trade_date', 'ts_code', 'pb', 'total_mv', 'stock_code', 'stock_return', 'hs300_return', 'sw_ind_code', 'in_date', 'out_date']\n"]}], "source": ["# 合并所有数据\n", "print(\"\\n合并所有数据...\")\n", "\n", "# 1. 合并因子数据和收益率数据\n", "print(\"合并因子数据和收益率数据...\")\n", "merged_data = factor_data.merge(\n", "    stock_data[['stock_code', 'trade_date', 'return']], \n", "    on=['stock_code', 'trade_date'], \n", "    how='inner'\n", ")\n", "merged_data = merged_data.rename(columns={'return': 'stock_return'})\n", "print(f\"合并后数据形状: {merged_data.shape}\")\n", "\n", "# 2. 合并沪深300指数收益率\n", "print(\"合并沪深300指数收益率...\")\n", "merged_data = merged_data.merge(\n", "    index_data[['trade_date', 'hs300_return']], \n", "    on='trade_date', \n", "    how='left'\n", ")\n", "print(f\"合并指数后数据形状: {merged_data.shape}\")\n", "\n", "# 3. 合并行业分类\n", "print(\"合并行业分类数据...\")\n", "merged_data = merged_data.merge(\n", "    swind_data[['stock_code', 'sw_ind_code']], \n", "    on='stock_code', \n", "    how='left'\n", ")\n", "print(f\"合并行业后数据形状: {merged_data.shape}\")\n", "\n", "# 4. 合并上市退市日期\n", "print(\"合并上市退市日期...\")\n", "merged_data = merged_data.merge(\n", "    ipo_data[['stock_code', 'in_date', 'out_date']], \n", "    on='stock_code', \n", "    how='left'\n", ")\n", "print(f\"最终合并数据形状: {merged_data.shape}\")\n", "print(f\"最终数据列: {merged_data.columns.tolist()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.4 筛选有效股票样本"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "筛选有效股票样本...\n", "初始数据量: 13133567\n", "剔除收益率异常值...\n", "剔除收益率异常值后: 13129861 (剔除 3706)\n", "删除关键字段缺失值...\n", "删除缺失值后: 9106575 (剔除 4023286)\n", "剔除上市不满6个月的股票...\n", "剔除上市不满6个月后: 8801464 (剔除 305111)\n", "计算交易日数并筛选...\n", "剔除交易日不足后: 7728286 (剔除 1073178)\n", "\n", "最终有效数据量: 7728286\n", "数据保留率: 58.84%\n", "有效股票数量: 3213\n", "交易日数量: 1704\n"]}], "source": ["# 筛选有效股票样本\n", "print(\"\\n筛选有效股票样本...\")\n", "\n", "initial_count = len(merged_data)\n", "print(f\"初始数据量: {initial_count}\")\n", "\n", "# 1. 剔除收益率异常值\n", "print(\"剔除收益率异常值...\")\n", "merged_data = merged_data[abs(merged_data['stock_return']) <= 0.23]\n", "print(f\"剔除收益率异常值后: {len(merged_data)} (剔除 {initial_count - len(merged_data)})\")\n", "\n", "# 2. 删除关键字段的缺失值\n", "print(\"删除关键字段缺失值...\")\n", "before_dropna = len(merged_data)\n", "merged_data = merged_data.dropna(subset=['pb', 'total_mv', 'stock_return', 'hs300_return', 'sw_ind_code'])\n", "print(f\"删除缺失值后: {len(merged_data)} (剔除 {before_dropna - len(merged_data)})\")\n", "\n", "# 3. 剔除上市不满6个月的股票\n", "print(\"剔除上市不满6个月的股票...\")\n", "merged_data['days_since_ipo'] = (merged_data['trade_date'] - merged_data['in_date']).dt.days\n", "before_ipo_filter = len(merged_data)\n", "merged_data = merged_data[merged_data['days_since_ipo'] >= 180]\n", "print(f\"剔除上市不满6个月后: {len(merged_data)} (剔除 {before_ipo_filter - len(merged_data)})\")\n", "\n", "# 4. 计算每只股票的交易日数并筛选\n", "print(\"计算交易日数并筛选...\")\n", "stock_trading_days = merged_data.groupby('stock_code')['trade_date'].nunique()\n", "total_trading_days = merged_data['trade_date'].nunique()\n", "valid_stocks = stock_trading_days[stock_trading_days >= total_trading_days * 0.7].index\n", "before_trading_filter = len(merged_data)\n", "merged_data = merged_data[merged_data['stock_code'].isin(valid_stocks)]\n", "print(f\"剔除交易日不足后: {len(merged_data)} (剔除 {before_trading_filter - len(merged_data)})\")\n", "\n", "print(f\"\\n最终有效数据量: {len(merged_data)}\")\n", "print(f\"数据保留率: {len(merged_data) / initial_count * 100:.2f}%\")\n", "print(f\"有效股票数量: {merged_data['stock_code'].nunique()}\")\n", "print(f\"交易日数量: {merged_data['trade_date'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.5 数据质量检查"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据质量检查...\n", "缺失值统计:\n", "trade_date              0\n", "ts_code                 0\n", "pb                      0\n", "total_mv                0\n", "stock_code              0\n", "stock_return            0\n", "hs300_return            0\n", "sw_ind_code             0\n", "in_date                 0\n", "out_date          7358868\n", "days_since_ipo          0\n", "dtype: int64\n", "\n", "数据范围检查:\n", "日期范围: 2015-12-31 00:00:00 到 2022-12-30 00:00:00\n", "股票收益率范围: -0.2271 到 0.2220\n", "PB因子范围: 0.0226 到 45723.5682\n", "市值范围: 9611 到 326737048\n", "\n", "行业分布:\n", "行业数量: 31\n", "前10个行业:\n", "sw_ind_code\n", "801030.SI    812323\n", "801890.SI    655715\n", "801150.SI    600619\n", "801080.SI    510041\n", "801730.SI    394144\n", "801750.SI    358317\n", "801180.SI    358226\n", "801050.SI    300294\n", "801880.SI    287167\n", "801200.SI    258530\n", "Name: count, dtype: int64\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABdEAAAPdCAYAAABlRyFLAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQABAABJREFUeJzs3XlcVGXfx/HvyDJIKouYlKCSS5qFpqJkalZ2m2talmuuZVqmVlJZmZkFLmmZabklaqaV3pp73pXeZtmdkhviUgaKa5LAgMDIMs8fvpynCQ6iAiPj5/16ndfznDPXdeY3c5+Ga75ecx2TzWazCQAAAAAAAAAA5FPO2QUAAAAAAAAAAHC9IkQHAAAAAAAAAMAAIToAAAAAAAAAAAYI0QEAAAAAAAAAMECIDgAAAAAAAACAAUJ0AAAAAAAAAAAMEKIDAAAAAAAAAGCAEB0AAAAAAAAAAAOE6AAAAAAAAAAAGCBEBwAoMzNTBw8eVF5env3YoUOHFB8ff0Xnyc7OznfswoUL11wfAAAAUFIyMjKUkpJyRVtGRobDOfLy8pSZmekwnr4WJ06ccDj30qVLdeDAgSs+z/Tp09WtWzclJSUVS11/Z7FY9L///e+q+7dv315vv/12vuNZWVmSpPT0dL333ntKSkrShQsX7N81Dh8+rH379hV67u+//77A7yYAcLUI0QHAiXJzc5WTk+OwGT2Wm5trfywvL09ZWVmy2WxX9Fznz58v8LG9e/eqfv36Ds8/ZcoU1a9fv8gD7lmzZqlhw4ayWq0Ox0eMGKFmzZrZB8NFkZmZqfHjx+uXX34pcp9LkpOTHb7UbNu2TRs3brzi8+zYsUP33HPPNX0xAAAAwPVvwIAB8vPzu6Jt4MCBDuc4fPiwvL295ebmJpPJdNntcuPTRx55RP369ZMklStXTlOnTtU777xzxa9t6dKlSkpKUkBAwBX3Lcy5c+f04IMPavbs2dd0jpSUFIdjeXl5atWqlT777DOdP39eERERSkpK0qJFi/TQQw9JkpYsWaJ//etfDt+P/s5ms2ns2LHq3r07E3oAFBtCdABworvuukseHh4O2+nTpyVJlStXdjgeGhpq73f48GGVL19e5cqVK9Ig3WQyyd3dXRUqVHAIyi8pX768JMnT01PSxcHrhg0b1KdPnyIPuNu1a6fffvtNc+fOtR+zWq364osv1LZtW3l5eRXY7/z58/lmiZQvX16LFi3SnDlzHI7bbDZZrdZ8M3/+7p133lFoaKj9nKtWrdLQoUMLfN2FWbFihfbv3686depcUT8AAACUnoSEBIcxb8WKFdW6dWvt2LFDklSzZk37Y56enqpfv77mzZvncA4PDw/1799fNpvNYVu/fr1WrlyZ73j//v3l4eHhcI7atWvr5MmT+uuvv5ScnJxvO3DggO6//***********************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 数据质量检查\n", "print(\"\\n数据质量检查...\")\n", "\n", "# 检查缺失值\n", "print(\"缺失值统计:\")\n", "print(merged_data.isnull().sum())\n", "\n", "# 检查数据范围\n", "print(f\"\\n数据范围检查:\")\n", "print(f\"日期范围: {merged_data['trade_date'].min()} 到 {merged_data['trade_date'].max()}\")\n", "print(f\"股票收益率范围: {merged_data['stock_return'].min():.4f} 到 {merged_data['stock_return'].max():.4f}\")\n", "print(f\"PB因子范围: {merged_data['pb'].min():.4f} 到 {merged_data['pb'].max():.4f}\")\n", "print(f\"市值范围: {merged_data['total_mv'].min():.0f} 到 {merged_data['total_mv'].max():.0f}\")\n", "\n", "# 行业分布\n", "print(f\"\\n行业分布:\")\n", "industry_counts = merged_data['sw_ind_code'].value_counts()\n", "print(f\"行业数量: {len(industry_counts)}\")\n", "print(\"前10个行业:\")\n", "print(industry_counts.head(10))\n", "\n", "# 可视化数据分布\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "\n", "# 股票收益率分布\n", "axes[0, 0].hist(merged_data['stock_return'], bins=50, alpha=0.7)\n", "axes[0, 0].set_title('股票收益率分布')\n", "axes[0, 0].set_xlabel('收益率')\n", "axes[0, 0].set_ylabel('频数')\n", "\n", "# PB因子分布（取对数）\n", "axes[0, 1].hist(np.log(merged_data['pb']), bins=50, alpha=0.7)\n", "axes[0, 1].set_title('PB因子分布（对数）')\n", "axes[0, 1].set_xlabel('log(PB)')\n", "axes[0, 1].set_ylabel('频数')\n", "\n", "# 市值分布（取对数）\n", "axes[1, 0].hist(np.log(merged_data['total_mv']), bins=50, alpha=0.7)\n", "axes[1, 0].set_title('市值分布（对数）')\n", "axes[1, 0].set_xlabel('log(市值)')\n", "axes[1, 0].set_ylabel('频数')\n", "\n", "# 行业分布\n", "top_industries = industry_counts.head(10)\n", "axes[1, 1].bar(range(len(top_industries)), top_industries.values)\n", "axes[1, 1].set_title('前10个行业分布')\n", "axes[1, 1].set_xlabel('行业')\n", "axes[1, 1].set_ylabel('股票数量')\n", "axes[1, 1].set_xticks(range(len(top_industries)))\n", "axes[1, 1].set_xticklabels(top_industries.index, rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1.6 保存处理后的数据"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "保存处理后的数据...\n", "数据已保存到:\n", "- data/processed_data_step1.csv\n", "- data/processed_data_step1.h5\n", "\n", "第1步完成！\n", "处理后数据形状: (7728286, 11)\n", "包含字段: ['trade_date', 'ts_code', 'pb', 'total_mv', 'stock_code', 'stock_return', 'hs300_return', 'sw_ind_code', 'in_date', 'out_date', 'days_since_ipo']\n", "\n", "关键统计:\n", "- 股票数量: 3213\n", "- 交易日数量: 1704\n", "- 行业数量: 31\n", "- 数据完整性: 91.34%\n", "\n", "可以继续运行第2步：因子数据处理\n"]}], "source": ["# 保存处理后的数据\n", "print(\"\\n保存处理后的数据...\")\n", "\n", "# 保存为CSV和HDF5格式\n", "merged_data.to_csv('data/processed_data_step1.csv', index=False)\n", "merged_data.to_hdf('data/processed_data_step1.h5', key='data', mode='w')\n", "\n", "print(f\"数据已保存到:\")\n", "print(f\"- data/processed_data_step1.csv\")\n", "print(f\"- data/processed_data_step1.h5\")\n", "\n", "print(f\"\\n第1步完成！\")\n", "print(f\"处理后数据形状: {merged_data.shape}\")\n", "print(f\"包含字段: {merged_data.columns.tolist()}\")\n", "print(f\"\\n关键统计:\")\n", "print(f\"- 股票数量: {merged_data['stock_code'].nunique()}\")\n", "print(f\"- 交易日数量: {merged_data['trade_date'].nunique()}\")\n", "print(f\"- 行业数量: {merged_data['sw_ind_code'].nunique()}\")\n", "print(f\"- 数据完整性: {(1 - merged_data.isnull().sum().sum() / merged_data.size) * 100:.2f}%\")\n", "\n", "print(\"\\n可以继续运行第2步：因子数据处理\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}