import pandas as pd
import numpy as np
import h5py
import warnings
from datetime import datetime, timedelta
import os

warnings.filterwarnings('ignore')

# 设置数据路径
DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data'
MARKET_DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data'
OUTPUT_PATH = r'C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\processed_data'

# 创建输出目录
os.makedirs(OUTPUT_PATH, exist_ok=True)

print("Barra CNE6 数据预处理开始...")
print(f"数据路径: {DATA_PATH}")
print(f"输出路径: {OUTPUT_PATH}")

def load_industry_data():
    """加载行业分类数据"""
    print("加载行业分类数据...")
    
    industry_path = os.path.join(DATA_PATH, 'swind.xlsx')
    industry_data = pd.read_excel(industry_path)
    
    print(f"行业数据形状: {industry_data.shape}")
    print(f"列名: {list(industry_data.columns)}")
    print(f"行业数量: {industry_data['l1_name'].nunique() if 'l1_name' in industry_data.columns else 'N/A'}")
    
    return industry_data

def load_ipo_data():
    """加载IPO日期数据"""
    print("\n加载IPO日期数据...")
    
    ipo_path = os.path.join(DATA_PATH, 'ipodate.csv')
    ipo_data = pd.read_csv(ipo_path)
    
    print(f"IPO数据形状: {ipo_data.shape}")
    print(f"列名: {list(ipo_data.columns)}")
    
    return ipo_data

# 加载基础数据
industry_data = load_industry_data()
ipo_data = load_ipo_data()

# 显示样本数据
print("\n=== 行业数据样本 ===")
print(industry_data.head())

print("\n=== IPO数据样本 ===")
print(ipo_data.head())

def load_factor_data(max_records=500000):
    """加载因子数据"""
    print("\n加载因子数据...")
    
    factor_path = os.path.join(DATA_PATH, 'factor.h5')
    
    try:
        with h5py.File(factor_path, 'r') as f:
            print(f"HDF5文件结构: {list(f.keys())}")
            
            if 'data' in f:
                data_group = f['data']
                print(f"data组结构: {list(data_group.keys())}")
                
                # 读取因子名称
                if 'block0_items' in data_group:
                    factor_names = [name.decode('utf-8') if isinstance(name, bytes) else str(name) 
                                  for name in data_group['block0_items'][:]]
                    print(f"因子名称: {factor_names}")
                
                # 读取部分数据进行分析
                factor_values = data_group['block0_values'][:max_records]
                dates_idx = data_group['axis1_label0'][:max_records]
                stocks_idx = data_group['axis1_label1'][:max_records]
                
                # 读取索引映射
                date_levels = data_group['axis1_level0'][:]
                stock_levels = data_group['axis1_level1'][:]
                
                print(f"数据形状: {factor_values.shape}")
                print(f"日期数量: {len(date_levels)}")
                print(f"股票数量: {len(stock_levels)}")
                
                return {
                    'factor_names': factor_names,
                    'factor_values': factor_values,
                    'dates_idx': dates_idx,
                    'stocks_idx': stocks_idx,
                    'date_levels': date_levels,
                    'stock_levels': stock_levels
                }
            else:
                print("未找到data组")
                return None
                
    except Exception as e:
        print(f"加载因子数据失败: {e}")
        return None

# 加载因子数据
factor_raw = load_factor_data()

def explore_h5_structure(file_path, file_name):
    """探索HDF5文件结构"""
    print(f"\n=== 探索{file_name}结构 ===")
    
    try:
        with h5py.File(file_path, 'r') as f:
            print(f"根级别键: {list(f.keys())}")
            
            def print_structure(name, obj, level=0):
                indent = "  " * level
                if isinstance(obj, h5py.Group):
                    print(f"{indent}{name}/ (组)")
                    if level < 2:  # 限制深度
                        for key in obj.keys():
                            print_structure(key, obj[key], level + 1)
                elif isinstance(obj, h5py.Dataset):
                    print(f"{indent}{name} (数据集): 形状={obj.shape}, 类型={obj.dtype}")
            
            for key in f.keys():
                print_structure(key, f[key])
                
    except Exception as e:
        print(f"探索{file_name}失败: {e}")

# 探索各个数据文件结构
daily_path = os.path.join(DATA_PATH, 'daily0925.h5')
market_path = os.path.join(MARKET_DATA_PATH, 'ind.h5')
adjfactor_path = os.path.join(DATA_PATH, 'adjfactor.hd5')

explore_h5_structure(daily_path, "股价数据(daily0925.h5)")
explore_h5_structure(market_path, "市值数据(ind.h5)")
explore_h5_structure(adjfactor_path, "复权因子(adjfactor.hd5)")

def check_data_quality():
    """检查数据质量"""
    print("\n=== 数据质量检查 ===")
    
    # 检查行业数据
    print("\n1. 行业数据质量:")
    print(f"   总股票数: {len(industry_data)}")
    print(f"   缺失行业的股票: {industry_data['l1_name'].isna().sum() if 'l1_name' in industry_data.columns else 'N/A'}")
    
    if 'l1_name' in industry_data.columns:
        print("   行业分布:")
        industry_counts = industry_data['l1_name'].value_counts()
        print(industry_counts.head(10))
    
    # 检查IPO数据
    print("\n2. IPO数据质量:")
    print(f"   总股票数: {len(ipo_data)}")
    
    if 'list_date' in ipo_data.columns:
        ipo_data['list_date'] = pd.to_datetime(ipo_data['list_date'])
        print(f"   最早上市日期: {ipo_data['list_date'].min()}")
        print(f"   最晚上市日期: {ipo_data['list_date'].max()}")
    
    # 检查因子数据
    if factor_raw:
        print("\n3. 因子数据质量:")
        print(f"   因子数量: {len(factor_raw['factor_names'])}")
        print(f"   数据记录数: {len(factor_raw['factor_values'])}")
        print(f"   缺失值比例: {np.isnan(factor_raw['factor_values']).mean():.2%}")

check_data_quality()

def build_unified_dataset():
    """构建统一的数据集"""
    print("\n=== 构建统一数据集 ===")
    
    if not factor_raw:
        print("因子数据加载失败，无法构建数据集")
        return None
    
    # 解码股票代码
    stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) 
                   for code in factor_raw['stock_levels']]
    
    print(f"开始处理{len(factor_raw['factor_values'])}条记录...")
    
    # 构建数据列表
    data_list = []
    
    for i in range(min(100000, len(factor_raw['factor_values']))):  # 限制处理数量
        if i % 10000 == 0:
            print(f"处理进度: {i}/{min(100000, len(factor_raw['factor_values']))}")
        
        try:
            date_idx = factor_raw['dates_idx'][i]
            stock_idx = factor_raw['stocks_idx'][i]
            
            if date_idx < len(factor_raw['date_levels']) and stock_idx < len(stock_codes):
                # 处理日期
                date_raw = factor_raw['date_levels'][date_idx]
                
                try:
                    if date_raw > 1e15:
                        date = pd.to_datetime(date_raw, unit='ns')
                    elif date_raw > 1e12:
                        date = pd.to_datetime(date_raw, unit='ms')
                    elif date_raw > 1e9:
                        date = pd.to_datetime(date_raw, unit='s')
                    else:
                        date = pd.to_datetime(str(int(date_raw)), format='%Y%m%d')
                except:
                    continue
                
                stock = stock_codes[stock_idx]
                
                # 获取因子值
                factor_values = factor_raw['factor_values'][i]
                
                if not np.any(np.isnan(factor_values)):
                    record = {
                        'date': date,
                        'ts_code': stock
                    }
                    
                    # 添加因子值
                    for j, factor_name in enumerate(factor_raw['factor_names']):
                        record[factor_name] = factor_values[j]
                    
                    data_list.append(record)
        
        except Exception as e:
            continue
    
    if data_list:
        df = pd.DataFrame(data_list)
        df = df.sort_values(['date', 'ts_code'])
        
        print(f"\n构建完成！")
        print(f"数据形状: {df.shape}")
        print(f"日期范围: {df['date'].min()} 到 {df['date'].max()}")
        print(f"股票数量: {df['ts_code'].nunique()}")
        print(f"因子列: {[col for col in df.columns if col not in ['date', 'ts_code']]}")
        
        return df
    else:
        print("构建数据集失败")
        return None

# 构建统一数据集
factor_data = build_unified_dataset()

if factor_data is not None:
    print("\n=== 因子数据样本 ===")
    print(factor_data.head())
    print("\n=== 因子数据描述性统计 ===")
    print(factor_data.describe())

def filter_by_listing_days(data, ipo_data, min_days=60):
    """剔除上市不足指定天数的股票"""
    print(f"\n=== 剔除上市不足{min_days}个交易日的股票 ===")
    
    if data is None:
        print("数据为空，跳过过滤")
        return None
    
    # 处理IPO数据
    ipo_clean = ipo_data.copy()
    if 'list_date' in ipo_clean.columns:
        ipo_clean['list_date'] = pd.to_datetime(ipo_clean['list_date'])
    elif 'ipo_date' in ipo_clean.columns:
        ipo_clean['list_date'] = pd.to_datetime(ipo_clean['ipo_date'])
    else:
        print("未找到上市日期列，跳过过滤")
        return data
    
    print(f"原始数据: {len(data)}条记录, {data['ts_code'].nunique()}只股票")
    
    # 合并IPO信息
    data_with_ipo = data.merge(ipo_clean[['ts_code', 'list_date']], on='ts_code', how='left')
    
    # 计算上市天数
    data_with_ipo['days_since_ipo'] = (data_with_ipo['date'] - data_with_ipo['list_date']).dt.days
    
    # 过滤条件
    valid_mask = (
        data_with_ipo['days_since_ipo'].notna() & 
        (data_with_ipo['days_since_ipo'] >= min_days)
    )
    
    filtered_data = data_with_ipo[valid_mask].drop(['list_date', 'days_since_ipo'], axis=1)
    
    print(f"过滤后数据: {len(filtered_data)}条记录, {filtered_data['ts_code'].nunique()}只股票")
    print(f"剔除比例: {(1 - len(filtered_data)/len(data)):.2%}")
    
    return filtered_data

# 应用过滤
if factor_data is not None:
    factor_data_filtered = filter_by_listing_days(factor_data, ipo_data, min_days=60)
else:
    factor_data_filtered = None

def merge_industry_info(data, industry_data):
    """合并行业信息"""
    print("\n=== 合并行业信息 ===")
    
    if data is None:
        print("数据为空，跳过合并")
        return None
    
    # 取每只股票最新的行业分类
    industry_latest = industry_data.copy()
    if 'in_date' in industry_latest.columns:
        industry_latest = industry_latest.sort_values('in_date').groupby('ts_code').last().reset_index()
    
    # 合并行业信息
    data_with_industry = data.merge(
        industry_latest[['ts_code', 'l1_name']], 
        on='ts_code', 
        how='left'
    )
    
    print(f"合并前: {len(data)}条记录")
    print(f"合并后: {len(data_with_industry)}条记录")
    print(f"有行业信息的记录: {data_with_industry['l1_name'].notna().sum()}条")
    print(f"行业覆盖率: {data_with_industry['l1_name'].notna().mean():.2%}")
    
    if 'l1_name' in data_with_industry.columns:
        print("\n行业分布:")
        industry_counts = data_with_industry['l1_name'].value_counts()
        print(industry_counts.head(10))
    
    return data_with_industry

# 合并行业信息
if factor_data_filtered is not None:
    final_data = merge_industry_info(factor_data_filtered, industry_data)
else:
    final_data = None

def save_processed_data(data, output_path):
    """保存预处理后的数据"""
    print("\n=== 保存预处理数据 ===")
    
    if data is None:
        print("数据为空，无法保存")
        return False
    
    try:
        # 保存为多种格式
        
        # 1. 保存为HDF5格式（推荐用于大数据）
        h5_path = os.path.join(output_path, 'factor_data_processed.h5')
        data.to_hdf(h5_path, key='data', mode='w', format='table')
        print(f"已保存HDF5格式: {h5_path}")
        
        # 2. 保存为CSV格式（便于查看）
        csv_path = os.path.join(output_path, 'factor_data_processed.csv')
        data.to_csv(csv_path, index=False)
        print(f"已保存CSV格式: {csv_path}")
        
        # 3. 保存数据摘要
        summary_path = os.path.join(output_path, 'data_summary.txt')
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("Barra CNE6 数据预处理摘要\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"数据形状: {data.shape}\n")
            f.write(f"日期范围: {data['date'].min()} 到 {data['date'].max()}\n")
            f.write(f"股票数量: {data['ts_code'].nunique()}\n")
            f.write(f"因子列: {[col for col in data.columns if col not in ['date', 'ts_code', 'l1_name']]}\n")
            f.write(f"行业数量: {data['l1_name'].nunique() if 'l1_name' in data.columns else 'N/A'}\n")
            f.write(f"数据完整性: {(1 - data.isnull().sum().sum() / (data.shape[0] * data.shape[1])):.2%}\n")
        
        print(f"已保存数据摘要: {summary_path}")
        
        return True
        
    except Exception as e:
        print(f"保存数据失败: {e}")
        return False

# 保存数据
if final_data is not None:
    save_success = save_processed_data(final_data, OUTPUT_PATH)
    
    if save_success:
        print("\n=== 数据预处理完成 ===")
        print(f"最终数据形状: {final_data.shape}")
        print(f"数据保存路径: {OUTPUT_PATH}")
        print("\n可用于后续步骤的数据文件:")
        print("- factor_data_processed.h5 (推荐)")
        print("- factor_data_processed.csv")
        print("- data_summary.txt")
else:
    print("\n数据预处理失败")

def analyze_missing_data():
    """分析缺失的数据类型"""
    print("\n=== 缺失数据分析 ===")
    
    missing_data_types = []
    
    # 检查是否需要额外的风格因子
    if factor_raw and len(factor_raw['factor_names']) < 6:
        missing_data_types.append("风格因子不足 - Barra CNE6需要6个风格因子")
        current_factors = factor_raw['factor_names']
        required_factors = ['规模', '价值', '盈利', '成长', '杠杆', '流动性']
        print(f"当前因子: {current_factors}")
        print(f"标准CNE6因子: {required_factors}")
    
    # 检查是否有收益率数据
    print("\n需要补充的数据:")
    print("1. 股票收益率数据 - 用于因子收益率计算")
    print("2. 市值数据 - 用于加权回归")
    print("3. 交易量数据 - 用于流动性因子")
    print("4. 财务数据 - 用于构建完整的风格因子")
    print("5. 基准指数数据 - 用于风险模型验证")
    
    return missing_data_types

missing_types = analyze_missing_data()

print("\n=== 下一步计划 ===")
print("1. 02_风格因子构建.ipynb - 构建完整的6个风格因子")
print("2. 03_行业因子构建.ipynb - 构建行业因子")
print("3. 04_因子收益率计算.ipynb - 计算因子收益率")
print("4. 05_协方差矩阵估计.ipynb - 估计因子协方差矩阵")
print("5. 06_特质风险模型.ipynb - 构建特质风险模型")
print("6. 07_风险模型验证.ipynb - 模型验证和回测")