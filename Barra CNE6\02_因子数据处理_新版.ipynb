{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 02 因子数据处理（新版）\n", "\n", "本notebook完成以下任务：\n", "1. 加载第1步处理后的数据\n", "2. 处理原始因子数据\n", "3. 计算因子值\n", "4. 因子标准化\n", "5. 计算权重\n", "6. 保存处理后的数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["因子数据处理模块开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"因子数据处理模块开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.1 加载数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载第1步处理后的数据...\n", "数据形状: (7728286, 11)\n", "数据列: ['trade_date', 'ts_code', 'pb', 'total_mv', 'stock_code', 'stock_return', 'hs300_return', 'sw_ind_code', 'in_date', 'out_date', 'days_since_ipo']\n", "\n", "数据质量检查:\n", "缺失值统计:\n", "trade_date              0\n", "ts_code                 0\n", "pb                      0\n", "total_mv                0\n", "stock_code              0\n", "stock_return            0\n", "hs300_return            0\n", "sw_ind_code             0\n", "in_date                 0\n", "out_date          7358868\n", "days_since_ipo          0\n", "dtype: int64\n", "\n", "关键因子统计:\n", "PB因子: 均值=5.9242, 标准差=109.6903\n", "市值: 均值=1692478, 标准差=6638723\n"]}], "source": ["# 加载第1步处理后的数据\n", "print(\"加载第1步处理后的数据...\")\n", "merged_data = pd.read_hdf('data/processed_data_step1.h5', key='data')\n", "print(f\"数据形状: {merged_data.shape}\")\n", "print(f\"数据列: {merged_data.columns.tolist()}\")\n", "\n", "# 检查数据质量\n", "print(f\"\\n数据质量检查:\")\n", "print(f\"缺失值统计:\")\n", "print(merged_data.isnull().sum())\n", "print(f\"\\n关键因子统计:\")\n", "print(f\"PB因子: 均值={merged_data['pb'].mean():.4f}, 标准差={merged_data['pb'].std():.4f}\")\n", "print(f\"市值: 均值={merged_data['total_mv'].mean():.0f}, 标准差={merged_data['total_mv'].std():.0f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.2 处理原始因子数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理PB因子缺失值...\n", "PB因子缺失值数量: 0\n", "处理后PB因子缺失值: 0\n", "\n", "检查市值数据...\n", "市值缺失值: 0\n", "市值非正值: 0\n"]}], "source": ["# 处理PB因子缺失值\n", "print(\"\\n处理PB因子缺失值...\")\n", "pb_missing_before = merged_data['pb'].isnull().sum()\n", "print(f\"PB因子缺失值数量: {pb_missing_before}\")\n", "\n", "if pb_missing_before > 0:\n", "    # 用整体均值填充（简化处理）\n", "    pb_mean = merged_data['pb'].mean()\n", "    merged_data['pb'] = merged_data['pb'].fillna(pb_mean)\n", "    print(f\"用均值 {pb_mean:.4f} 填充PB因子缺失值\")\n", "\n", "pb_missing_after = merged_data['pb'].isnull().sum()\n", "print(f\"处理后PB因子缺失值: {pb_missing_after}\")\n", "\n", "# 检查市值数据\n", "print(f\"\\n检查市值数据...\")\n", "mv_missing = merged_data['total_mv'].isnull().sum()\n", "mv_negative = (merged_data['total_mv'] <= 0).sum()\n", "print(f\"市值缺失值: {mv_missing}\")\n", "print(f\"市值非正值: {mv_negative}\")\n", "\n", "if mv_negative > 0:\n", "    print(\"警告：发现市值非正值，需要处理\")\n", "    # 删除市值非正值的记录\n", "    before_mv_filter = len(merged_data)\n", "    merged_data = merged_data[merged_data['total_mv'] > 0]\n", "    print(f\"删除市值非正值记录: {before_mv_filter - len(merged_data)} 条\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.3 计算因子值"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "计算因子值...\n", "PB因子计算完成\n", "PB因子统计: 均值=0.453351, 标准差=0.327351\n", "市值因子计算完成\n", "市值因子统计: 均值=13.504800, 标准差=1.027068\n", "超额收益率计算完成\n", "超额收益率统计: 均值=0.000120, 标准差=0.025735\n", "\n", "超额收益率极端值处理:\n", "极端值数量: 1799 (0.02%)\n", "极端值已处理（截断到±0.2）\n", "处理后超额收益率统计: 均值=0.000118, 标准差=0.025716\n"]}], "source": ["# 计算因子值\n", "print(\"\\n计算因子值...\")\n", "\n", "# 1. 计算PB因子（价值因子）- 使用PB的倒数\n", "merged_data['pb_factor'] = 1.0 / merged_data['pb']\n", "print(f\"PB因子计算完成\")\n", "print(f\"PB因子统计: 均值={merged_data['pb_factor'].mean():.6f}, 标准差={merged_data['pb_factor'].std():.6f}\")\n", "\n", "# 2. 计算市值因子（规模因子）- 使用市值的对数\n", "merged_data['size_factor'] = np.log(merged_data['total_mv'])\n", "print(f\"市值因子计算完成\")\n", "print(f\"市值因子统计: 均值={merged_data['size_factor'].mean():.6f}, 标准差={merged_data['size_factor'].std():.6f}\")\n", "\n", "# 3. 计算超额收益率\n", "merged_data['excess_return'] = merged_data['stock_return'] - merged_data['hs300_return']\n", "print(f\"超额收益率计算完成\")\n", "print(f\"超额收益率统计: 均值={merged_data['excess_return'].mean():.6f}, 标准差={merged_data['excess_return'].std():.6f}\")\n", "\n", "# 4. 处理超额收益率极端值\n", "excess_threshold = 0.20\n", "extreme_mask = abs(merged_data['excess_return']) > excess_threshold\n", "extreme_count = extreme_mask.sum()\n", "print(f\"\\n超额收益率极端值处理:\")\n", "print(f\"极端值数量: {extreme_count} ({extreme_count/len(merged_data)*100:.2f}%)\")\n", "\n", "if extreme_count > 0:\n", "    merged_data.loc[extreme_mask, 'excess_return'] = np.sign(merged_data.loc[extreme_mask, 'excess_return']) * excess_threshold\n", "    print(f\"极端值已处理（截断到±{excess_threshold}）\")\n", "\n", "print(f\"处理后超额收益率统计: 均值={merged_data['excess_return'].mean():.6f}, 标准差={merged_data['excess_return'].std():.6f}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.4 因子标准化"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "按交易日期标准化因子...\n", "标准化PB因子...\n", "标准化市值因子...\n", "\n", "标准化结果检查:\n", "pb_std: 均值=0.000000, 标准差=0.999890\n", "size_std: 均值=-0.000000, 标准差=0.999890\n", "\n", "因子相关性检查:\n", "            pb_std  size_std\n", "pb_std    1.000000  0.105168\n", "size_std  0.105168  1.000000\n", "\n", "PB因子与市值因子相关系数: 0.1052\n", "因子相关性在可接受范围内\n"]}], "source": ["# 按交易日期标准化因子\n", "print(\"\\n按交易日期标准化因子...\")\n", "\n", "def standardize_factor_by_date(df, factor_col, output_col):\n", "    \"\"\"按交易日期标准化因子\"\"\"\n", "    df_copy = df.copy()\n", "    \n", "    for date in df_copy['trade_date'].unique():\n", "        date_mask = df_copy['trade_date'] == date\n", "        date_data = df_copy[date_mask]\n", "        \n", "        if len(date_data) > 1 and date_data[factor_col].std() > 0:\n", "            # 标准化：(x - mean) / std\n", "            mean_val = date_data[factor_col].mean()\n", "            std_val = date_data[factor_col].std()\n", "            df_copy.loc[date_mask, output_col] = (date_data[factor_col] - mean_val) / std_val\n", "        else:\n", "            # 如果标准差为0或只有一个观测值，设为0\n", "            df_copy.loc[date_mask, output_col] = 0\n", "    \n", "    return df_copy\n", "\n", "# 标准化PB因子\n", "print(\"标准化PB因子...\")\n", "merged_data = standardize_factor_by_date(merged_data, 'pb_factor', 'pb_std')\n", "\n", "# 标准化市值因子\n", "print(\"标准化市值因子...\")\n", "merged_data = standardize_factor_by_date(merged_data, 'size_factor', 'size_std')\n", "\n", "# 检查标准化结果\n", "print(f\"\\n标准化结果检查:\")\n", "print(f\"pb_std: 均值={merged_data['pb_std'].mean():.6f}, 标准差={merged_data['pb_std'].std():.6f}\")\n", "print(f\"size_std: 均值={merged_data['size_std'].mean():.6f}, 标准差={merged_data['size_std'].std():.6f}\")\n", "\n", "# 检查因子相关性\n", "print(f\"\\n因子相关性检查:\")\n", "factor_corr = merged_data[['pb_std', 'size_std']].corr()\n", "print(factor_corr)\n", "\n", "pb_size_corr = factor_corr.loc['pb_std', 'size_std']\n", "print(f\"\\nPB因子与市值因子相关系数: {pb_size_corr:.4f}\")\n", "\n", "if abs(pb_size_corr) > 0.4:\n", "    print(f\"警告：因子相关性较高 (|{pb_size_corr:.4f}| > 0.4)，可能需要正交化处理\")\n", "else:\n", "    print(f\"因子相关性在可接受范围内\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.5 计算权重"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "使用原始市值计算权重...\n", "验证权重计算...\n", "权重缺失数量: 0\n", "每日权重和统计:\n", "count    1.704000e+03\n", "mean     1.000000e+00\n", "std      9.598804e-17\n", "min      1.000000e+00\n", "25%      1.000000e+00\n", "50%      1.000000e+00\n", "75%      1.000000e+00\n", "max      1.000000e+00\n", "Name: weight, dtype: float64\n", "✓ 权重计算正确，每日权重和为1\n", "\n", "权重统计:\n", "count    7.728286e+06\n", "mean     2.204887e-04\n", "std      1.789235e-04\n", "min      1.977506e-05\n", "25%      1.297232e-04\n", "50%      1.732372e-04\n", "75%      2.497010e-04\n", "max      3.899076e-03\n", "Name: weight, dtype: float64\n"]}], "source": ["# 使用原始市值计算权重\n", "print(\"\\n使用原始市值计算权重...\")\n", "\n", "def calculate_weights_by_date(df):\n", "    \"\"\"按交易日期计算市值平方根权重\"\"\"\n", "    df_copy = df.copy()\n", "    \n", "    for date in df_copy['trade_date'].unique():\n", "        date_mask = df_copy['trade_date'] == date\n", "        date_data = df_copy[date_mask]\n", "        \n", "        # 使用原始市值计算权重\n", "        sqrt_caps = np.sqrt(date_data['total_mv'])\n", "        total_sqrt_cap = sqrt_caps.sum()\n", "        \n", "        if total_sqrt_cap > 0:\n", "            weights = sqrt_caps / total_sqrt_cap\n", "        else:\n", "            # 如果总市值为0，使用等权重\n", "            weights = pd.Series(1.0 / len(date_data), index=date_data.index)\n", "        \n", "        df_copy.loc[date_mask, 'weight'] = weights\n", "    \n", "    return df_copy\n", "\n", "# 计算权重\n", "merged_data = calculate_weights_by_date(merged_data)\n", "\n", "# 验证权重计算\n", "print(\"验证权重计算...\")\n", "weight_missing = merged_data['weight'].isnull().sum()\n", "print(f\"权重缺失数量: {weight_missing}\")\n", "\n", "if weight_missing == 0:\n", "    # 检查每日权重和\n", "    daily_weight_sums = merged_data.groupby('trade_date')['weight'].sum()\n", "    print(f\"每日权重和统计:\")\n", "    print(daily_weight_sums.describe())\n", "    \n", "    # 检查权重和是否接近1\n", "    weight_sum_check = abs(daily_weight_sums - 1.0) < 1e-10\n", "    if weight_sum_check.all():\n", "        print(\"✓ 权重计算正确，每日权重和为1\")\n", "    else:\n", "        print(f\"✗ 权重计算有误，{(~weight_sum_check).sum()}个日期的权重和不为1\")\n", "        print(\"权重和异常的日期:\")\n", "        print(daily_weight_sums[~weight_sum_check].head())\n", "    \n", "    # 权重统计\n", "    print(f\"\\n权重统计:\")\n", "    print(merged_data['weight'].describe())\n", "    \n", "else:\n", "    print(f\"❌ 权重计算失败，有 {weight_missing} 个缺失值\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.6 可视化分析"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "可视化因子分布...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化因子分布\n", "print(\"\\n可视化因子分布...\")\n", "\n", "fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "\n", "# 原始PB因子分布\n", "axes[0, 0].hist(merged_data['pb_factor'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[0, 0].set_title('原始PB因子分布')\n", "axes[0, 0].set_xlabel('PB因子值')\n", "axes[0, 0].set_ylabel('频数')\n", "\n", "# 标准化PB因子分布\n", "axes[0, 1].hist(merged_data['pb_std'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[0, 1].set_title('标准化PB因子分布')\n", "axes[0, 1].set_xlabel('标准化PB因子值')\n", "axes[0, 1].set_ylabel('频数')\n", "\n", "# 原始市值因子分布\n", "axes[0, 2].hist(merged_data['size_factor'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[0, 2].set_title('原始市值因子分布')\n", "axes[0, 2].set_xlabel('市值因子值')\n", "axes[0, 2].set_ylabel('频数')\n", "\n", "# 标准化市值因子分布\n", "axes[1, 0].hist(merged_data['size_std'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[1, 0].set_title('标准化市值因子分布')\n", "axes[1, 0].set_xlabel('标准化市值因子值')\n", "axes[1, 0].set_ylabel('频数')\n", "\n", "# 超额收益率分布\n", "axes[1, 1].hist(merged_data['excess_return'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[1, 1].set_title('超额收益率分布')\n", "axes[1, 1].set_xlabel('超额收益率')\n", "axes[1, 1].set_ylabel('频数')\n", "\n", "# 权重分布\n", "axes[1, 2].hist(merged_data['weight'], bins=50, alpha=0.7, edgecolor='black')\n", "axes[1, 2].set_title('权重分布')\n", "axes[1, 2].set_xlabel('权重')\n", "axes[1, 2].set_ylabel('频数')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 因子相关性热力图\n", "plt.figure(figsize=(8, 6))\n", "factor_corr_matrix = merged_data[['pb_std', 'size_std', 'excess_return', 'weight']].corr()\n", "sns.heatmap(factor_corr_matrix, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5)\n", "plt.title('因子相关性矩阵')\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2.7 保存处理后的数据"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "保存处理后的数据...\n", "数据已保存到:\n", "- data/processed_data_step2.csv\n", "- data/processed_data_step2.h5\n", "\n", "第2步完成！\n", "处理摘要:\n", "          指标            数值\n", "0      数据记录数  7.728286e+06\n", "1       股票数量  3.213000e+03\n", "2      交易日数量  1.704000e+03\n", "3       行业数量  3.100000e+01\n", "4     PB因子均值  3.504773e-18\n", "5    PB因子标准差  9.998898e-01\n", "6     市值因子均值 -3.103912e-18\n", "7    市值因子标准差  9.998898e-01\n", "8    超额收益率均值  1.184370e-04\n", "9   超额收益率标准差  2.571638e-02\n", "10    权重缺失数量  0.000000e+00\n", "11    因子相关系数  1.051684e-01\n", "\n", "关键成果:\n", "- ✅ 原始因子数据处理完成\n", "- ✅ 因子标准化完成（均值≈0，标准差≈1）\n", "- ✅ 权重计算成功（无缺失值）\n", "- ✅ 超额收益率计算完成\n", "- ✅ 数据质量良好，可进行下一步\n", "\n", "可以继续运行第3步：构建因子暴露矩阵\n"]}], "source": ["# 保存处理后的数据\n", "print(\"\\n保存处理后的数据...\")\n", "\n", "# 保存完整数据\n", "merged_data.to_csv('data/processed_data_step2.csv', index=False)\n", "merged_data.to_hdf('data/processed_data_step2.h5', key='data', mode='w')\n", "\n", "print(f\"数据已保存到:\")\n", "print(f\"- data/processed_data_step2.csv\")\n", "print(f\"- data/processed_data_step2.h5\")\n", "\n", "# 生成处理摘要\n", "summary_stats = {\n", "    '指标': [\n", "        '数据记录数', '股票数量', '交易日数量', '行业数量',\n", "        'PB因子均值', 'PB因子标准差', '市值因子均值', '市值因子标准差',\n", "        '超额收益率均值', '超额收益率标准差', '权重缺失数量', '因子相关系数'\n", "    ],\n", "    '数值': [\n", "        len(merged_data),\n", "        merged_data['stock_code'].nunique(),\n", "        merged_data['trade_date'].nunique(),\n", "        merged_data['sw_ind_code'].nunique(),\n", "        merged_data['pb_std'].mean(),\n", "        merged_data['pb_std'].std(),\n", "        merged_data['size_std'].mean(),\n", "        merged_data['size_std'].std(),\n", "        merged_data['excess_return'].mean(),\n", "        merged_data['excess_return'].std(),\n", "        merged_data['weight'].isnull().sum(),\n", "        merged_data[['pb_std', 'size_std']].corr().iloc[0, 1]\n", "    ]\n", "}\n", "\n", "summary_df = pd.DataFrame(summary_stats)\n", "summary_df.to_csv('data/step2_summary.csv', index=False)\n", "\n", "print(f\"\\n第2步完成！\")\n", "print(f\"处理摘要:\")\n", "print(summary_df)\n", "\n", "print(f\"\\n关键成果:\")\n", "print(f\"- ✅ 原始因子数据处理完成\")\n", "print(f\"- ✅ 因子标准化完成（均值≈0，标准差≈1）\")\n", "print(f\"- ✅ 权重计算成功（无缺失值）\")\n", "print(f\"- ✅ 超额收益率计算完成\")\n", "print(f\"- ✅ 数据质量良好，可进行下一步\")\n", "\n", "print(\"\\n可以继续运行第3步：构建因子暴露矩阵\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}