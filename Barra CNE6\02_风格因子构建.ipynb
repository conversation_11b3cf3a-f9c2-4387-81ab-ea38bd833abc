{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Barra CNE6 风险模型复现 - 第二步：风格因子构建\n", "\n", "## 目标\n", "构建Barra CNE6模型的6个风格因子：\n", "1. **规模因子 (<PERSON><PERSON>)**: 市值相关指标\n", "2. **价值因子 (Value)**: 估值相关指标\n", "3. **盈利因子 (Profitability)**: 盈利能力指标\n", "4. **成长因子 (Growth)**: 成长性指标\n", "5. **杠杆因子 (Leverage)**: 财务杠杆指标\n", "6. **流动性因子 (Liquidity)**: 交易活跃度指标\n", "\n", "## <PERSON><PERSON>因子构建方法\n", "- 每个风格因子由多个原始指标合成\n", "- 使用标准化和去极值处理\n", "- 行业中性化处理\n", "- 因子正交化"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Barra CNE6 风格因子构建开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import h5py\n", "import warnings\n", "from datetime import datetime\n", "import os\n", "from scipy import stats\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 路径设置\n", "DATA_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\单因子\\data'\n", "MARKET_DATA_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\一致预期\\data'\n", "PROCESSED_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\processed_data'\n", "OUTPUT_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors'\n", "\n", "# 创建输出目录\n", "os.makedirs(OUTPUT_PATH, exist_ok=True)\n", "\n", "print(\"Barra CNE6 风格因子构建开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载预处理数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载预处理数据...\n", "从HDF5加载数据: (98527, 5)\n", "日期范围: 2015-12-31 00:00:00 到 2016-03-03 00:00:00\n", "股票数量: 2681\n", "现有因子: ['pb', 'total_mv']\n"]}], "source": ["def load_processed_data():\n", "    \"\"\"加载预处理后的数据\"\"\"\n", "    print(\"加载预处理数据...\")\n", "    \n", "    try:\n", "        # 优先加载HDF5格式\n", "        h5_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.h5')\n", "        if os.path.exists(h5_path):\n", "            data = pd.read_hdf(h5_path, key='data')\n", "            print(f\"从HDF5加载数据: {data.shape}\")\n", "        else:\n", "            # 备选CSV格式\n", "            csv_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.csv')\n", "            data = pd.read_csv(csv_path)\n", "            data['date'] = pd.to_datetime(data['date'])\n", "            print(f\"从CSV加载数据: {data.shape}\")\n", "        \n", "        print(f\"日期范围: {data['date'].min()} 到 {data['date'].max()}\")\n", "        print(f\"股票数量: {data['ts_code'].nunique()}\")\n", "        print(f\"现有因子: {[col for col in data.columns if col not in ['date', 'ts_code', 'l1_name']]}\")\n", "        \n", "        return data\n", "        \n", "    except Exception as e:\n", "        print(f\"加载预处理数据失败: {e}\")\n", "        return None\n", "\n", "# 加载数据\n", "base_data = load_processed_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 加载额外的市值和财务数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "加载市值数据...\n", "市值数据文件结构: ['data']\n", "data: 组，包含 ['axis0', 'axis1', 'block0_items', 'block0_values', 'block1_items', 'block1_values', 'block2_items', 'block2_values']\n", "\n", "加载股价数据...\n", "股价数据文件结构: ['data']\n", "data: 组，包含 ['axis0', 'axis1_label0', 'axis1_label1', 'axis1_level0', 'axis1_level1', 'block0_items', 'block0_values']\n"]}], "source": ["def load_market_value_data():\n", "    \"\"\"加载市值数据\"\"\"\n", "    print(\"\\n加载市值数据...\")\n", "    \n", "    try:\n", "        market_path = os.path.join(MARKET_DATA_PATH, 'ind.h5')\n", "        \n", "        with h5py.File(market_path, 'r') as f:\n", "            print(f\"市值数据文件结构: {list(f.keys())}\")\n", "            \n", "            # 探索数据结构\n", "            for key in f.keys():\n", "                obj = f[key]\n", "                if isinstance(obj, h5py.Dataset):\n", "                    print(f\"{key}: 形状={obj.shape}, 类型={obj.dtype}\")\n", "                elif isinstance(obj, h5py.Group):\n", "                    print(f\"{key}: 组，包含 {list(obj.keys())}\")\n", "        \n", "        # 这里需要根据实际数据结构来读取\n", "        # 暂时返回None，等确认数据结构后再实现\n", "        return None\n", "        \n", "    except Exception as e:\n", "        print(f\"加载市值数据失败: {e}\")\n", "        return None\n", "\n", "def load_stock_price_data():\n", "    \"\"\"加载股价数据\"\"\"\n", "    print(\"\\n加载股价数据...\")\n", "    \n", "    try:\n", "        price_path = os.path.join(DATA_PATH, 'daily0925.h5')\n", "        \n", "        with h5py.File(price_path, 'r') as f:\n", "            print(f\"股价数据文件结构: {list(f.keys())}\")\n", "            \n", "            # 探索数据结构\n", "            for key in f.keys():\n", "                obj = f[key]\n", "                if isinstance(obj, h5py.Dataset):\n", "                    print(f\"{key}: 形状={obj.shape}, 类型={obj.dtype}\")\n", "                elif isinstance(obj, h5py.Group):\n", "                    print(f\"{key}: 组，包含 {list(obj.keys())}\")\n", "        \n", "        return None\n", "        \n", "    except Exception as e:\n", "        print(f\"加载股价数据失败: {e}\")\n", "        return None\n", "\n", "# 加载额外数据\n", "market_data = load_market_value_data()\n", "price_data = load_stock_price_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 基于现有数据构建风格因子"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 基于现有数据构建风格因子 ===\n", "现有因子: ['pb', 'total_mv']\n", "✓ 构建规模因子 (基于total_mv)\n", "✓ 构建价值因子 (基于pb)\n", "✗ 缺少盈利数据，无法构建盈利因子\n", "✗ 缺少成长数据，无法构建成长因子\n", "✗ 缺少财务数据，无法构建杠杆因子\n", "✗ 缺少交易量数据，无法构建流动性因子\n"]}], "source": ["def build_style_factors_from_existing(data):\n", "    \"\"\"基于现有因子数据构建风格因子\"\"\"\n", "    print(\"\\n=== 基于现有数据构建风格因子 ===\")\n", "    \n", "    if data is None:\n", "        print(\"数据为空\")\n", "        return None\n", "    \n", "    # 获取现有的因子列\n", "    factor_cols = [col for col in data.columns if col not in ['date', 'ts_code', 'l1_name']]\n", "    print(f\"现有因子: {factor_cols}\")\n", "    \n", "    style_factors = data.copy()\n", "    \n", "    # 1. 规模因子 (<PERSON><PERSON>)\n", "    if 'total_mv' in factor_cols:\n", "        # 使用对数市值作为规模因子\n", "        style_factors['Size'] = np.log(style_factors['total_mv'])\n", "        print(\"✓ 构建规模因子 (基于total_mv)\")\n", "    else:\n", "        print(\"✗ 缺少市值数据，无法构建规模因子\")\n", "    \n", "    # 2. 价值因子 (Value)\n", "    if 'pb' in factor_cols:\n", "        # 使用PB的倒数作为价值因子 (Book-to-Market)\n", "        style_factors['Value'] = 1 / style_factors['pb']\n", "        # 处理极值\n", "        style_factors['Value'] = style_factors['Value'].clip(\n", "            style_factors['Value'].quantile(0.01),\n", "            style_factors['Value'].quantile(0.99)\n", "        )\n", "        print(\"✓ 构建价值因子 (基于pb)\")\n", "    else:\n", "        print(\"✗ 缺少PB数据，无法构建价值因子\")\n", "    \n", "    # 3. 盈利因子 (Profitability) - 需要财务数据\n", "    print(\"✗ 缺少盈利数据，无法构建盈利因子\")\n", "    \n", "    # 4. 成长因子 (Growth) - 需要历史财务数据\n", "    print(\"✗ 缺少成长数据，无法构建成长因子\")\n", "    \n", "    # 5. 杠杆因子 (Leverage) - 需要财务数据\n", "    print(\"✗ 缺少财务数据，无法构建杠杆因子\")\n", "    \n", "    # 6. 流动性因子 (Liquidity) - 需要交易量数据\n", "    print(\"✗ 缺少交易量数据，无法构建流动性因子\")\n", "    \n", "    return style_factors\n", "\n", "# 构建风格因子\n", "if base_data is not None:\n", "    style_data = build_style_factors_from_existing(base_data)\n", "else:\n", "    style_data = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 因子标准化和去极值处理"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 因子标准化处理 ===\n", "标准化因子: Size\n", "标准化因子: Value\n", "\n", "已标准化的因子: ['Size_std', 'Value_std']\n"]}], "source": ["def standardize_factors(data, factor_cols):\n", "    \"\"\"因子标准化处理\"\"\"\n", "    print(\"\\n=== 因子标准化处理 ===\")\n", "    \n", "    if data is None:\n", "        return None\n", "    \n", "    standardized_data = data.copy()\n", "    \n", "    for factor in factor_cols:\n", "        if factor in data.columns:\n", "            print(f\"标准化因子: {factor}\")\n", "            \n", "            # 按日期分组进行标准化\n", "            def standardize_cross_section(group):\n", "                values = group[factor]\n", "                \n", "                # 去极值 (3倍标准差)\n", "                mean_val = values.mean()\n", "                std_val = values.std()\n", "                \n", "                if std_val > 0:\n", "                    # 去极值\n", "                    values_winsorized = values.clip(\n", "                        mean_val - 3 * std_val,\n", "                        mean_val + 3 * std_val\n", "                    )\n", "                    \n", "                    # 标准化\n", "                    values_standardized = (values_winsorized - values_winsorized.mean()) / values_winsorized.std()\n", "                    \n", "                    group[f'{factor}_std'] = values_standardized\n", "                else:\n", "                    group[f'{factor}_std'] = 0\n", "                \n", "                return group\n", "            \n", "            standardized_data = standardized_data.groupby('date').apply(standardize_cross_section)\n", "            standardized_data = standardized_data.reset_index(drop=True)\n", "    \n", "    return standardized_data\n", "\n", "# 标准化处理\n", "if style_data is not None:\n", "    style_factors = ['Size', 'Value']  # 目前只有这两个因子\n", "    available_factors = [f for f in style_factors if f in style_data.columns]\n", "    \n", "    if available_factors:\n", "        standardized_data = standardize_factors(style_data, available_factors)\n", "        print(f\"\\n已标准化的因子: {[f + '_std' for f in available_factors]}\")\n", "    else:\n", "        standardized_data = style_data\n", "        print(\"\\n没有可标准化的因子\")\n", "else:\n", "    standardized_data = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 行业中性化处理"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 行业中性化处理 ===\n", "行业中性化因子: Size_std\n", "行业中性化因子: Value_std\n", "\n", "已中性化的因子: ['Size_neutral', 'Value_neutral']\n"]}], "source": ["def industry_neutralize_factors(data, factor_cols):\n", "    \"\"\"行业中性化处理\"\"\"\n", "    print(\"\\n=== 行业中性化处理 ===\")\n", "    \n", "    if data is None or 'l1_name' not in data.columns:\n", "        print(\"缺少行业信息，跳过中性化\")\n", "        return data\n", "    \n", "    neutralized_data = data.copy()\n", "    \n", "    for factor in factor_cols:\n", "        if factor in data.columns:\n", "            print(f\"行业中性化因子: {factor}\")\n", "            \n", "            def neutralize_cross_section(group):\n", "                # 过滤有效数据\n", "                valid_data = group.dropna(subset=[factor, 'l1_name'])\n", "                \n", "                if len(valid_data) < 10:\n", "                    group[f'{factor}_neutral'] = group[factor]\n", "                    return group\n", "                \n", "                # 创建行业虚拟变量\n", "                industry_dummies = pd.get_dummies(valid_data['l1_name'], prefix='industry')\n", "                \n", "                try:\n", "                    # 线性回归去除行业影响\n", "                    X = industry_dummies.values\n", "                    y = valid_data[factor].values\n", "                    \n", "                    # 添加截距项\n", "                    X_with_intercept = np.column_stack([np.ones(len(X)), X])\n", "                    \n", "                    # 最小二乘法\n", "                    beta = np.linalg.lstsq(X_with_intercept, y, rcond=None)[0]\n", "                    y_pred = X_with_intercept @ beta\n", "                    residuals = y - y_pred\n", "                    \n", "                    # 将残差赋值回原数据\n", "                    group.loc[valid_data.index, f'{factor}_neutral'] = residuals\n", "                    \n", "                except:\n", "                    # 回归失败时保持原值\n", "                    group[f'{factor}_neutral'] = group[factor]\n", "                \n", "                return group\n", "            \n", "            neutralized_data = neutralized_data.groupby('date').apply(neutralize_cross_section)\n", "            neutralized_data = neutralized_data.reset_index(drop=True)\n", "    \n", "    return neutralized_data\n", "\n", "# 行业中性化\n", "if standardized_data is not None:\n", "    std_factor_cols = [col for col in standardized_data.columns if col.endswith('_std')]\n", "    \n", "    if std_factor_cols:\n", "        final_factors = industry_neutralize_factors(standardized_data, std_factor_cols)\n", "        print(f\"\\n已中性化的因子: {[col.replace('_std', '_neutral') for col in std_factor_cols]}\")\n", "    else:\n", "        final_factors = standardized_data\n", "        print(\"\\n没有可中性化的因子\")\n", "else:\n", "    final_factors = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 因子质量分析"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 因子质量分析 ===\n", "分析因子: ['Size_std_neutral', 'Value_std_neutral']\n", "\n", "1. 因子覆盖率:\n", "   Size_std_neutral: 100.00%\n", "   Value_std_neutral: 100.00%\n", "\n", "2. 因子分布统计:\n", "       Size_std_neutral  Value_std_neutral\n", "count      9.852700e+04       9.852700e+04\n", "mean      -3.657751e-16       1.858299e-15\n", "std        9.210417e-01       8.448760e-01\n", "min       -2.526361e+00      -2.877979e+00\n", "25%       -6.489717e-01      -5.503352e-01\n", "50%       -1.499854e-01      -1.218908e-01\n", "75%        5.010138e-01       4.180432e-01\n", "max        3.582398e+00       3.427073e+00\n", "\n", "3. 因子相关性:\n", "                   Size_std_neutral  Value_std_neutral\n", "Size_std_neutral            1.00000            0.19302\n", "Value_std_neutral           0.19302            1.00000\n", "\n", "4. 时间序列稳定性:\n", "   Size_std_neutral: 均值稳定性=0.0000, 标准差稳定性=0.0012\n", "   Value_std_neutral: 均值稳定性=0.0000, 标准差稳定性=0.0131\n"]}], "source": ["def analyze_factor_quality(data):\n", "    \"\"\"分析因子质量\"\"\"\n", "    print(\"\\n=== 因子质量分析 ===\")\n", "    \n", "    if data is None:\n", "        return\n", "    \n", "    # 获取最终因子列\n", "    factor_cols = [col for col in data.columns if col.endswith('_neutral')]\n", "    \n", "    if not factor_cols:\n", "        print(\"没有找到处理后的因子\")\n", "        return\n", "    \n", "    print(f\"分析因子: {factor_cols}\")\n", "    \n", "    # 1. 因子覆盖率\n", "    print(\"\\n1. 因子覆盖率:\")\n", "    for factor in factor_cols:\n", "        coverage = data[factor].notna().mean()\n", "        print(f\"   {factor}: {coverage:.2%}\")\n", "    \n", "    # 2. 因子分布统计\n", "    print(\"\\n2. 因子分布统计:\")\n", "    factor_stats = data[factor_cols].describe()\n", "    print(factor_stats)\n", "    \n", "    # 3. 因子相关性\n", "    if len(factor_cols) > 1:\n", "        print(\"\\n3. 因子相关性:\")\n", "        correlation_matrix = data[factor_cols].corr()\n", "        print(correlation_matrix)\n", "    \n", "    # 4. 时间序列稳定性\n", "    print(\"\\n4. 时间序列稳定性:\")\n", "    monthly_stats = data.groupby(data['date'].dt.to_period('M'))[factor_cols].agg(['mean', 'std'])\n", "    \n", "    for factor in factor_cols:\n", "        mean_stability = monthly_stats[(factor, 'mean')].std()\n", "        std_stability = monthly_stats[(factor, 'std')].std()\n", "        print(f\"   {factor}: 均值稳定性={mean_stability:.4f}, 标准差稳定性={std_stability:.4f}\")\n", "\n", "# 分析因子质量\n", "if final_factors is not None:\n", "    analyze_factor_quality(final_factors)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 因子可视化"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 因子可视化 ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def visualize_factors(data):\n", "    \"\"\"因子可视化\"\"\"\n", "    print(\"\\n=== 因子可视化 ===\")\n", "    \n", "    if data is None:\n", "        return\n", "    \n", "    factor_cols = [col for col in data.columns if col.endswith('_neutral')]\n", "    \n", "    if not factor_cols:\n", "        print(\"没有可视化的因子\")\n", "        return\n", "    \n", "    # 1. 因子分布图\n", "    fig, axes = plt.subplots(len(factor_cols), 2, figsize=(12, 4*len(factor_cols)))\n", "    \n", "    if len(factor_cols) == 1:\n", "        axes = axes.reshape(1, -1)\n", "    \n", "    for i, factor in enumerate(factor_cols):\n", "        # 分布直方图\n", "        axes[i, 0].hist(data[factor].dropna(), bins=50, alpha=0.7, edgecolor='black')\n", "        axes[i, 0].set_title(f'{factor} 分布')\n", "        axes[i, 0].set_xlabel('因子值')\n", "        axes[i, 0].set_ylabel('频数')\n", "        \n", "        # 时间序列图\n", "        monthly_mean = data.groupby(data['date'].dt.to_period('M'))[factor].mean()\n", "        axes[i, 1].plot(monthly_mean.index.astype(str), monthly_mean.values)\n", "        axes[i, 1].set_title(f'{factor} 时间序列')\n", "        axes[i, 1].set_xlabel('月份')\n", "        axes[i, 1].set_ylabel('因子均值')\n", "        axes[i, 1].tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 2. 因子相关性热力图\n", "    if len(factor_cols) > 1:\n", "        plt.figure(figsize=(8, 6))\n", "        correlation_matrix = data[factor_cols].corr()\n", "        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,\n", "                   square=True, linewidths=0.5)\n", "        plt.title('因子相关性热力图')\n", "        plt.show()\n", "\n", "# 可视化因子\n", "if final_factors is not None:\n", "    visualize_factors(final_factors)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 保存风格因子数据"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 保存风格因子数据 ===\n", "已保存完整数据: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors\\style_factors_complete.h5\n", "已保存因子数据: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors\\style_factors_only.h5\n", "已保存CSV格式: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors\\style_factors.csv\n", "已保存因子摘要: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors\\style_factors_summary.txt\n", "\n", "=== 风格因子构建完成 ===\n", "成功构建的风格因子: ['Size_std_neutral', 'Value_std_neutral']\n", "数据保存路径: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors\n", "\n", "可用文件:\n", "- style_factors_complete.h5 (完整数据)\n", "- style_factors_only.h5 (仅因子数据)\n", "- style_factors.csv (CSV格式)\n", "- style_factors_summary.txt (摘要)\n"]}], "source": ["def save_style_factors(data, output_path):\n", "    \"\"\"保存风格因子数据\"\"\"\n", "    print(\"\\n=== 保存风格因子数据 ===\")\n", "    \n", "    if data is None:\n", "        print(\"数据为空，无法保存\")\n", "        return False\n", "    \n", "    try:\n", "        # 1. 保存完整数据\n", "        h5_path = os.path.join(output_path, 'style_factors_complete.h5')\n", "        data.to_hdf(h5_path, key='data', mode='w', format='table')\n", "        print(f\"已保存完整数据: {h5_path}\")\n", "        \n", "        # 2. 保存仅因子数据\n", "        factor_cols = ['date', 'ts_code'] + [col for col in data.columns if col.endswith('_neutral')]\n", "        factor_only = data[factor_cols].copy()\n", "        \n", "        factor_h5_path = os.path.join(output_path, 'style_factors_only.h5')\n", "        factor_only.to_hdf(factor_h5_path, key='data', mode='w', format='table')\n", "        print(f\"已保存因子数据: {factor_h5_path}\")\n", "        \n", "        # 3. 保存CSV格式\n", "        csv_path = os.path.join(output_path, 'style_factors.csv')\n", "        factor_only.to_csv(csv_path, index=False)\n", "        print(f\"已保存CSV格式: {csv_path}\")\n", "        \n", "        # 4. 保存因子摘要\n", "        summary_path = os.path.join(output_path, 'style_factors_summary.txt')\n", "        with open(summary_path, 'w', encoding='utf-8') as f:\n", "            f.write(\"Barra CNE6 风格因子摘要\\n\")\n", "            f.write(\"=\" * 50 + \"\\n\\n\")\n", "            f.write(f\"数据形状: {data.shape}\\n\")\n", "            f.write(f\"日期范围: {data['date'].min()} 到 {data['date'].max()}\\n\")\n", "            f.write(f\"股票数量: {data['ts_code'].nunique()}\\n\")\n", "            \n", "            neutral_factors = [col for col in data.columns if col.endswith('_neutral')]\n", "            f.write(f\"风格因子: {neutral_factors}\\n\\n\")\n", "            \n", "            if neutral_factors:\n", "                f.write(\"因子统计:\\n\")\n", "                factor_stats = data[neutral_factors].describe()\n", "                f.write(factor_stats.to_string())\n", "                f.write(\"\\n\\n\")\n", "                \n", "                f.write(\"因子覆盖率:\\n\")\n", "                for factor in neutral_factors:\n", "                    coverage = data[factor].notna().mean()\n", "                    f.write(f\"{factor}: {coverage:.2%}\\n\")\n", "        \n", "        print(f\"已保存因子摘要: {summary_path}\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"保存风格因子失败: {e}\")\n", "        return False\n", "\n", "# 保存数据\n", "if final_factors is not None:\n", "    save_success = save_style_factors(final_factors, OUTPUT_PATH)\n", "    \n", "    if save_success:\n", "        print(\"\\n=== 风格因子构建完成 ===\")\n", "        \n", "        neutral_factors = [col for col in final_factors.columns if col.endswith('_neutral')]\n", "        print(f\"成功构建的风格因子: {neutral_factors}\")\n", "        print(f\"数据保存路径: {OUTPUT_PATH}\")\n", "        \n", "        print(\"\\n可用文件:\")\n", "        print(\"- style_factors_complete.h5 (完整数据)\")\n", "        print(\"- style_factors_only.h5 (仅因子数据)\")\n", "        print(\"- style_factors.csv (CSV格式)\")\n", "        print(\"- style_factors_summary.txt (摘要)\")\n", "else:\n", "    print(\"\\n风格因子构建失败\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 缺失因子分析和建议"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 缺失因子分析 ===\n", "已构建因子: ['Size_std', 'Value_std']\n", "\n", "缺失因子及所需数据:\n", "✗ Size: 规模因子 - 需要市值数据\n", "✗ Value: 价值因子 - 需要PB、PE、PS等估值数据\n", "✗ Profitability: 盈利因子 - 需要ROE、ROA、毛利率等财务数据\n", "✗ Growth: 成长因子 - 需要营收增长率、利润增长率等数据\n", "✗ Leverage: 杠杆因子 - 需要资产负债率、债务股本比等数据\n", "✗ Liquidity: 流动性因子 - 需要交易量、换手率等数据\n", "\n", "数据补充建议:\n", "1. 财务数据: 资产负债表、利润表、现金流量表\n", "2. 交易数据: 成交量、成交额、换手率\n", "3. 估值数据: PE、PS、PCF等多个估值指标\n", "4. 分析师数据: 盈利预测、评级等\n", "5. 宏观数据: 利率、汇率等市场环境数据\n", "\n", "下一步计划:\n", "1. 03_行业因子构建.ipynb - 构建行业因子矩阵\n", "2. 04_因子收益率计算.ipynb - 计算因子收益率\n", "3. 05_协方差矩阵估计.ipynb - 估计因子协方差矩阵\n", "4. 06_特质风险模型.ipynb - 构建特质风险模型\n", "5. 07_风险模型验证.ipynb - 模型验证和回测\n"]}], "source": ["def analyze_missing_factors():\n", "    \"\"\"分析缺失的因子和数据需求\"\"\"\n", "    print(\"\\n=== 缺失因子分析 ===\")\n", "    \n", "    required_factors = {\n", "        'Size': '规模因子 - 需要市值数据',\n", "        'Value': '价值因子 - 需要PB、PE、PS等估值数据',\n", "        'Profitability': '盈利因子 - 需要ROE、ROA、毛利率等财务数据',\n", "        'Growth': '成长因子 - 需要营收增长率、利润增长率等数据',\n", "        'Leverage': '杠杆因子 - 需要资产负债率、债务股本比等数据',\n", "        'Liquidity': '流动性因子 - 需要交易量、换手率等数据'\n", "    }\n", "    \n", "    if final_factors is not None:\n", "        built_factors = [col.replace('_neutral', '') for col in final_factors.columns if col.endswith('_neutral')]\n", "        print(f\"已构建因子: {built_factors}\")\n", "    else:\n", "        built_factors = []\n", "        print(\"已构建因子: 无\")\n", "    \n", "    print(\"\\n缺失因子及所需数据:\")\n", "    for factor, description in required_factors.items():\n", "        if factor not in built_factors:\n", "            print(f\"✗ {factor}: {description}\")\n", "        else:\n", "            print(f\"✓ {factor}: 已构建\")\n", "    \n", "    print(\"\\n数据补充建议:\")\n", "    print(\"1. 财务数据: 资产负债表、利润表、现金流量表\")\n", "    print(\"2. 交易数据: 成交量、成交额、换手率\")\n", "    print(\"3. 估值数据: PE、PS、PCF等多个估值指标\")\n", "    print(\"4. 分析师数据: 盈利预测、评级等\")\n", "    print(\"5. 宏观数据: 利率、汇率等市场环境数据\")\n", "    \n", "    print(\"\\n下一步计划:\")\n", "    print(\"1. 03_行业因子构建.ipynb - 构建行业因子矩阵\")\n", "    print(\"2. 04_因子收益率计算.ipynb - 计算因子收益率\")\n", "    print(\"3. 05_协方差矩阵估计.ipynb - 估计因子协方差矩阵\")\n", "    print(\"4. 06_特质风险模型.ipynb - 构建特质风险模型\")\n", "    print(\"5. 07_风险模型验证.ipynb - 模型验证和回测\")\n", "\n", "analyze_missing_factors()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}