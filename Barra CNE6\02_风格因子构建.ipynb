import pandas as pd
import numpy as np
import h5py
import warnings
from datetime import datetime
import os
from scipy import stats
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 路径设置
DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data'
MARKET_DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data'
PROCESSED_PATH = r'C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\processed_data'
OUTPUT_PATH = r'C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\style_factors'

# 创建输出目录
os.makedirs(OUTPUT_PATH, exist_ok=True)

print("Barra CNE6 风格因子构建开始...")

def load_processed_data():
    """加载预处理后的数据"""
    print("加载预处理数据...")
    
    try:
        # 优先加载HDF5格式
        h5_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.h5')
        if os.path.exists(h5_path):
            data = pd.read_hdf(h5_path, key='data')
            print(f"从HDF5加载数据: {data.shape}")
        else:
            # 备选CSV格式
            csv_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.csv')
            data = pd.read_csv(csv_path)
            data['date'] = pd.to_datetime(data['date'])
            print(f"从CSV加载数据: {data.shape}")
        
        print(f"日期范围: {data['date'].min()} 到 {data['date'].max()}")
        print(f"股票数量: {data['ts_code'].nunique()}")
        print(f"现有因子: {[col for col in data.columns if col not in ['date', 'ts_code', 'l1_name']]}")
        
        return data
        
    except Exception as e:
        print(f"加载预处理数据失败: {e}")
        return None

# 加载数据
base_data = load_processed_data()

def load_market_value_data():
    """加载市值数据"""
    print("\n加载市值数据...")
    
    try:
        market_path = os.path.join(MARKET_DATA_PATH, 'ind.h5')
        
        with h5py.File(market_path, 'r') as f:
            print(f"市值数据文件结构: {list(f.keys())}")
            
            # 探索数据结构
            for key in f.keys():
                obj = f[key]
                if isinstance(obj, h5py.Dataset):
                    print(f"{key}: 形状={obj.shape}, 类型={obj.dtype}")
                elif isinstance(obj, h5py.Group):
                    print(f"{key}: 组，包含 {list(obj.keys())}")
        
        # 这里需要根据实际数据结构来读取
        # 暂时返回None，等确认数据结构后再实现
        return None
        
    except Exception as e:
        print(f"加载市值数据失败: {e}")
        return None

def load_stock_price_data():
    """加载股价数据"""
    print("\n加载股价数据...")
    
    try:
        price_path = os.path.join(DATA_PATH, 'daily0925.h5')
        
        with h5py.File(price_path, 'r') as f:
            print(f"股价数据文件结构: {list(f.keys())}")
            
            # 探索数据结构
            for key in f.keys():
                obj = f[key]
                if isinstance(obj, h5py.Dataset):
                    print(f"{key}: 形状={obj.shape}, 类型={obj.dtype}")
                elif isinstance(obj, h5py.Group):
                    print(f"{key}: 组，包含 {list(obj.keys())}")
        
        return None
        
    except Exception as e:
        print(f"加载股价数据失败: {e}")
        return None

# 加载额外数据
market_data = load_market_value_data()
price_data = load_stock_price_data()

def build_style_factors_from_existing(data):
    """基于现有因子数据构建风格因子"""
    print("\n=== 基于现有数据构建风格因子 ===")
    
    if data is None:
        print("数据为空")
        return None
    
    # 获取现有的因子列
    factor_cols = [col for col in data.columns if col not in ['date', 'ts_code', 'l1_name']]
    print(f"现有因子: {factor_cols}")
    
    style_factors = data.copy()
    
    # 1. 规模因子 (Size)
    if 'total_mv' in factor_cols:
        # 使用对数市值作为规模因子
        style_factors['Size'] = np.log(style_factors['total_mv'])
        print("✓ 构建规模因子 (基于total_mv)")
    else:
        print("✗ 缺少市值数据，无法构建规模因子")
    
    # 2. 价值因子 (Value)
    if 'pb' in factor_cols:
        # 使用PB的倒数作为价值因子 (Book-to-Market)
        style_factors['Value'] = 1 / style_factors['pb']
        # 处理极值
        style_factors['Value'] = style_factors['Value'].clip(
            style_factors['Value'].quantile(0.01),
            style_factors['Value'].quantile(0.99)
        )
        print("✓ 构建价值因子 (基于pb)")
    else:
        print("✗ 缺少PB数据，无法构建价值因子")
    
    # 3. 盈利因子 (Profitability) - 需要财务数据
    print("✗ 缺少盈利数据，无法构建盈利因子")
    
    # 4. 成长因子 (Growth) - 需要历史财务数据
    print("✗ 缺少成长数据，无法构建成长因子")
    
    # 5. 杠杆因子 (Leverage) - 需要财务数据
    print("✗ 缺少财务数据，无法构建杠杆因子")
    
    # 6. 流动性因子 (Liquidity) - 需要交易量数据
    print("✗ 缺少交易量数据，无法构建流动性因子")
    
    return style_factors

# 构建风格因子
if base_data is not None:
    style_data = build_style_factors_from_existing(base_data)
else:
    style_data = None

def standardize_factors(data, factor_cols):
    """因子标准化处理"""
    print("\n=== 因子标准化处理 ===")
    
    if data is None:
        return None
    
    standardized_data = data.copy()
    
    for factor in factor_cols:
        if factor in data.columns:
            print(f"标准化因子: {factor}")
            
            # 按日期分组进行标准化
            def standardize_cross_section(group):
                values = group[factor]
                
                # 去极值 (3倍标准差)
                mean_val = values.mean()
                std_val = values.std()
                
                if std_val > 0:
                    # 去极值
                    values_winsorized = values.clip(
                        mean_val - 3 * std_val,
                        mean_val + 3 * std_val
                    )
                    
                    # 标准化
                    values_standardized = (values_winsorized - values_winsorized.mean()) / values_winsorized.std()
                    
                    group[f'{factor}_std'] = values_standardized
                else:
                    group[f'{factor}_std'] = 0
                
                return group
            
            standardized_data = standardized_data.groupby('date').apply(standardize_cross_section)
            standardized_data = standardized_data.reset_index(drop=True)
    
    return standardized_data

# 标准化处理
if style_data is not None:
    style_factors = ['Size', 'Value']  # 目前只有这两个因子
    available_factors = [f for f in style_factors if f in style_data.columns]
    
    if available_factors:
        standardized_data = standardize_factors(style_data, available_factors)
        print(f"\n已标准化的因子: {[f + '_std' for f in available_factors]}")
    else:
        standardized_data = style_data
        print("\n没有可标准化的因子")
else:
    standardized_data = None

def industry_neutralize_factors(data, factor_cols):
    """行业中性化处理"""
    print("\n=== 行业中性化处理 ===")
    
    if data is None or 'l1_name' not in data.columns:
        print("缺少行业信息，跳过中性化")
        return data
    
    neutralized_data = data.copy()
    
    for factor in factor_cols:
        if factor in data.columns:
            print(f"行业中性化因子: {factor}")
            
            def neutralize_cross_section(group):
                # 过滤有效数据
                valid_data = group.dropna(subset=[factor, 'l1_name'])
                
                if len(valid_data) < 10:
                    group[f'{factor}_neutral'] = group[factor]
                    return group
                
                # 创建行业虚拟变量
                industry_dummies = pd.get_dummies(valid_data['l1_name'], prefix='industry')
                
                try:
                    # 线性回归去除行业影响
                    X = industry_dummies.values
                    y = valid_data[factor].values
                    
                    # 添加截距项
                    X_with_intercept = np.column_stack([np.ones(len(X)), X])
                    
                    # 最小二乘法
                    beta = np.linalg.lstsq(X_with_intercept, y, rcond=None)[0]
                    y_pred = X_with_intercept @ beta
                    residuals = y - y_pred
                    
                    # 将残差赋值回原数据
                    group.loc[valid_data.index, f'{factor}_neutral'] = residuals
                    
                except:
                    # 回归失败时保持原值
                    group[f'{factor}_neutral'] = group[factor]
                
                return group
            
            neutralized_data = neutralized_data.groupby('date').apply(neutralize_cross_section)
            neutralized_data = neutralized_data.reset_index(drop=True)
    
    return neutralized_data

# 行业中性化
if standardized_data is not None:
    std_factor_cols = [col for col in standardized_data.columns if col.endswith('_std')]
    
    if std_factor_cols:
        final_factors = industry_neutralize_factors(standardized_data, std_factor_cols)
        print(f"\n已中性化的因子: {[col.replace('_std', '_neutral') for col in std_factor_cols]}")
    else:
        final_factors = standardized_data
        print("\n没有可中性化的因子")
else:
    final_factors = None

def analyze_factor_quality(data):
    """分析因子质量"""
    print("\n=== 因子质量分析 ===")
    
    if data is None:
        return
    
    # 获取最终因子列
    factor_cols = [col for col in data.columns if col.endswith('_neutral')]
    
    if not factor_cols:
        print("没有找到处理后的因子")
        return
    
    print(f"分析因子: {factor_cols}")
    
    # 1. 因子覆盖率
    print("\n1. 因子覆盖率:")
    for factor in factor_cols:
        coverage = data[factor].notna().mean()
        print(f"   {factor}: {coverage:.2%}")
    
    # 2. 因子分布统计
    print("\n2. 因子分布统计:")
    factor_stats = data[factor_cols].describe()
    print(factor_stats)
    
    # 3. 因子相关性
    if len(factor_cols) > 1:
        print("\n3. 因子相关性:")
        correlation_matrix = data[factor_cols].corr()
        print(correlation_matrix)
    
    # 4. 时间序列稳定性
    print("\n4. 时间序列稳定性:")
    monthly_stats = data.groupby(data['date'].dt.to_period('M'))[factor_cols].agg(['mean', 'std'])
    
    for factor in factor_cols:
        mean_stability = monthly_stats[(factor, 'mean')].std()
        std_stability = monthly_stats[(factor, 'std')].std()
        print(f"   {factor}: 均值稳定性={mean_stability:.4f}, 标准差稳定性={std_stability:.4f}")

# 分析因子质量
if final_factors is not None:
    analyze_factor_quality(final_factors)

def visualize_factors(data):
    """因子可视化"""
    print("\n=== 因子可视化 ===")
    
    if data is None:
        return
    
    factor_cols = [col for col in data.columns if col.endswith('_neutral')]
    
    if not factor_cols:
        print("没有可视化的因子")
        return
    
    # 1. 因子分布图
    fig, axes = plt.subplots(len(factor_cols), 2, figsize=(12, 4*len(factor_cols)))
    
    if len(factor_cols) == 1:
        axes = axes.reshape(1, -1)
    
    for i, factor in enumerate(factor_cols):
        # 分布直方图
        axes[i, 0].hist(data[factor].dropna(), bins=50, alpha=0.7, edgecolor='black')
        axes[i, 0].set_title(f'{factor} 分布')
        axes[i, 0].set_xlabel('因子值')
        axes[i, 0].set_ylabel('频数')
        
        # 时间序列图
        monthly_mean = data.groupby(data['date'].dt.to_period('M'))[factor].mean()
        axes[i, 1].plot(monthly_mean.index.astype(str), monthly_mean.values)
        axes[i, 1].set_title(f'{factor} 时间序列')
        axes[i, 1].set_xlabel('月份')
        axes[i, 1].set_ylabel('因子均值')
        axes[i, 1].tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.show()
    
    # 2. 因子相关性热力图
    if len(factor_cols) > 1:
        plt.figure(figsize=(8, 6))
        correlation_matrix = data[factor_cols].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5)
        plt.title('因子相关性热力图')
        plt.show()

# 可视化因子
if final_factors is not None:
    visualize_factors(final_factors)

def save_style_factors(data, output_path):
    """保存风格因子数据"""
    print("\n=== 保存风格因子数据 ===")
    
    if data is None:
        print("数据为空，无法保存")
        return False
    
    try:
        # 1. 保存完整数据
        h5_path = os.path.join(output_path, 'style_factors_complete.h5')
        data.to_hdf(h5_path, key='data', mode='w', format='table')
        print(f"已保存完整数据: {h5_path}")
        
        # 2. 保存仅因子数据
        factor_cols = ['date', 'ts_code'] + [col for col in data.columns if col.endswith('_neutral')]
        factor_only = data[factor_cols].copy()
        
        factor_h5_path = os.path.join(output_path, 'style_factors_only.h5')
        factor_only.to_hdf(factor_h5_path, key='data', mode='w', format='table')
        print(f"已保存因子数据: {factor_h5_path}")
        
        # 3. 保存CSV格式
        csv_path = os.path.join(output_path, 'style_factors.csv')
        factor_only.to_csv(csv_path, index=False)
        print(f"已保存CSV格式: {csv_path}")
        
        # 4. 保存因子摘要
        summary_path = os.path.join(output_path, 'style_factors_summary.txt')
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("Barra CNE6 风格因子摘要\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"数据形状: {data.shape}\n")
            f.write(f"日期范围: {data['date'].min()} 到 {data['date'].max()}\n")
            f.write(f"股票数量: {data['ts_code'].nunique()}\n")
            
            neutral_factors = [col for col in data.columns if col.endswith('_neutral')]
            f.write(f"风格因子: {neutral_factors}\n\n")
            
            if neutral_factors:
                f.write("因子统计:\n")
                factor_stats = data[neutral_factors].describe()
                f.write(factor_stats.to_string())
                f.write("\n\n")
                
                f.write("因子覆盖率:\n")
                for factor in neutral_factors:
                    coverage = data[factor].notna().mean()
                    f.write(f"{factor}: {coverage:.2%}\n")
        
        print(f"已保存因子摘要: {summary_path}")
        
        return True
        
    except Exception as e:
        print(f"保存风格因子失败: {e}")
        return False

# 保存数据
if final_factors is not None:
    save_success = save_style_factors(final_factors, OUTPUT_PATH)
    
    if save_success:
        print("\n=== 风格因子构建完成 ===")
        
        neutral_factors = [col for col in final_factors.columns if col.endswith('_neutral')]
        print(f"成功构建的风格因子: {neutral_factors}")
        print(f"数据保存路径: {OUTPUT_PATH}")
        
        print("\n可用文件:")
        print("- style_factors_complete.h5 (完整数据)")
        print("- style_factors_only.h5 (仅因子数据)")
        print("- style_factors.csv (CSV格式)")
        print("- style_factors_summary.txt (摘要)")
else:
    print("\n风格因子构建失败")

def analyze_missing_factors():
    """分析缺失的因子和数据需求"""
    print("\n=== 缺失因子分析 ===")
    
    required_factors = {
        'Size': '规模因子 - 需要市值数据',
        'Value': '价值因子 - 需要PB、PE、PS等估值数据',
        'Profitability': '盈利因子 - 需要ROE、ROA、毛利率等财务数据',
        'Growth': '成长因子 - 需要营收增长率、利润增长率等数据',
        'Leverage': '杠杆因子 - 需要资产负债率、债务股本比等数据',
        'Liquidity': '流动性因子 - 需要交易量、换手率等数据'
    }
    
    if final_factors is not None:
        built_factors = [col.replace('_neutral', '') for col in final_factors.columns if col.endswith('_neutral')]
        print(f"已构建因子: {built_factors}")
    else:
        built_factors = []
        print("已构建因子: 无")
    
    print("\n缺失因子及所需数据:")
    for factor, description in required_factors.items():
        if factor not in built_factors:
            print(f"✗ {factor}: {description}")
        else:
            print(f"✓ {factor}: 已构建")
    
    print("\n数据补充建议:")
    print("1. 财务数据: 资产负债表、利润表、现金流量表")
    print("2. 交易数据: 成交量、成交额、换手率")
    print("3. 估值数据: PE、PS、PCF等多个估值指标")
    print("4. 分析师数据: 盈利预测、评级等")
    print("5. 宏观数据: 利率、汇率等市场环境数据")
    
    print("\n下一步计划:")
    print("1. 03_行业因子构建.ipynb - 构建行业因子矩阵")
    print("2. 04_因子收益率计算.ipynb - 计算因子收益率")
    print("3. 05_协方差矩阵估计.ipynb - 估计因子协方差矩阵")
    print("4. 06_特质风险模型.ipynb - 构建特质风险模型")
    print("5. 07_风险模型验证.ipynb - 模型验证和回测")

analyze_missing_factors()