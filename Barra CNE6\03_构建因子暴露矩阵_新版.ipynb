{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 03 构建因子暴露矩阵（新版）\n", "\n", "本notebook完成以下任务：\n", "1. 加载第2步处理后的数据\n", "2. 创建行业因子哑变量\n", "3. 创建国家因子\n", "4. 整合因子暴露矩阵\n", "5. 处理多重共线性问题\n", "6. 保存因子暴露矩阵"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["构建因子暴露矩阵模块开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"构建因子暴露矩阵模块开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.1 加载数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载第2步处理后的数据...\n", "数据形状: (7728286, 17)\n", "数据列: ['trade_date', 'ts_code', 'pb', 'total_mv', 'stock_code', 'stock_return', 'hs300_return', 'sw_ind_code', 'in_date', 'out_date', 'days_since_ipo', 'pb_factor', 'size_factor', 'excess_return', 'pb_std', 'size_std', 'weight']\n", "\n", "数据质量检查:\n", "缺失值统计:\n", "out_date    7358868\n", "dtype: int64\n", "\n", "关键字段检查:\n", "股票数量: 3213\n", "交易日数量: 1704\n", "行业数量: 31\n"]}], "source": ["# 加载第2步处理后的数据\n", "print(\"加载第2步处理后的数据...\")\n", "merged_data = pd.read_hdf('data/processed_data_step2.h5', key='data')\n", "print(f\"数据形状: {merged_data.shape}\")\n", "print(f\"数据列: {merged_data.columns.tolist()}\")\n", "\n", "# 检查数据质量\n", "print(f\"\\n数据质量检查:\")\n", "print(f\"缺失值统计:\")\n", "missing_summary = merged_data.isnull().sum()\n", "print(missing_summary[missing_summary > 0])\n", "\n", "if missing_summary.sum() == 0:\n", "    print(\"✓ 无缺失值\")\n", "\n", "print(f\"\\n关键字段检查:\")\n", "print(f\"股票数量: {merged_data['stock_code'].nunique()}\")\n", "print(f\"交易日数量: {merged_data['trade_date'].nunique()}\")\n", "print(f\"行业数量: {merged_data['sw_ind_code'].nunique()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.2 创建行业因子哑变量"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "创建行业因子哑变量...\n", "行业数量: 31\n", "行业代码: ['801010.SI', '801030.SI', '801040.SI', '801050.SI', '801080.SI', '801110.SI', '801120.SI', '801130.SI', '801140.SI', '801150.SI', '801160.SI', '801170.SI', '801180.SI', '801200.SI', '801210.SI', '801230.SI', '801710.SI', '801720.SI', '801730.SI', '801740.SI', '801750.SI', '801760.SI', '801770.SI', '801780.SI', '801790.SI', '801880.SI', '801890.SI', '801950.SI', '801960.SI', '801970.SI', '801980.SI']\n", "\n", "行业分布（前10个）:\n", "sw_ind_code\n", "801030.SI    812323\n", "801890.SI    655715\n", "801150.SI    600619\n", "801080.SI    510041\n", "801730.SI    394144\n", "801750.SI    358317\n", "801180.SI    358226\n", "801050.SI    300294\n", "801880.SI    287167\n", "801200.SI    258530\n", "Name: count, dtype: int64\n", "\n", "创建行业哑变量...\n", "创建了 31 个行业因子\n", "\n", "行业因子验证:\n", "每行行业因子和的统计:\n", "1    7728286\n", "Name: count, dtype: int64\n", "✓ 行业因子哑变量创建正确\n"]}], "source": ["# 创建行业因子哑变量\n", "print(\"\\n创建行业因子哑变量...\")\n", "\n", "# 获取所有行业代码\n", "industry_codes = sorted(merged_data['sw_ind_code'].dropna().unique())\n", "print(f\"行业数量: {len(industry_codes)}\")\n", "print(f\"行业代码: {industry_codes}\")\n", "\n", "# 检查行业分布\n", "industry_counts = merged_data['sw_ind_code'].value_counts()\n", "print(f\"\\n行业分布（前10个）:\")\n", "print(industry_counts.head(10))\n", "\n", "# 创建行业哑变量\n", "print(f\"\\n创建行业哑变量...\")\n", "for ind_code in industry_codes:\n", "    col_name = f'ind_{ind_code}'\n", "    merged_data[col_name] = (merged_data['sw_ind_code'] == ind_code).astype(int)\n", "\n", "# 验证行业哑变量\n", "industry_factor_cols = [f'ind_{code}' for code in industry_codes]\n", "print(f\"创建了 {len(industry_factor_cols)} 个行业因子\")\n", "\n", "# 检查每行的行业因子和（应该为1）\n", "industry_sum = merged_data[industry_factor_cols].sum(axis=1)\n", "print(f\"\\n行业因子验证:\")\n", "print(f\"每行行业因子和的统计:\")\n", "print(industry_sum.value_counts())\n", "\n", "if (industry_sum == 1).all():\n", "    print(\"✓ 行业因子哑变量创建正确\")\n", "else:\n", "    print(\"❌ 行业因子哑变量有问题\")\n", "    problem_rows = (industry_sum != 1).sum()\n", "    print(f\"问题行数: {problem_rows}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.3 处理多重共线性问题"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理多重共线性问题...\n", "选择 801980.SI 作为基准行业（样本数: 18583）\n", "将从因子矩阵中移除 ind_801980.SI\n", "最终行业因子数量: 30\n", "注意：为避免完全共线性，暂不使用国家因子\n", "\n", "最终因子构成:\n", "- 行业因子: 30\n", "- 风格因子: 2\n", "- 总因子数: 32\n", "- 基准行业: 801980.SI（已排除）\n"]}], "source": ["# 处理多重共线性问题\n", "print(\"\\n处理多重共线性问题...\")\n", "\n", "# 方案：去掉一个行业因子避免完全共线性\n", "# 选择样本数量最少的行业作为基准行业（去掉）\n", "industry_counts_sorted = industry_counts.sort_values()\n", "base_industry = industry_counts_sorted.index[0]  # 样本最少的行业\n", "base_industry_col = f'ind_{base_industry}'\n", "\n", "print(f\"选择 {base_industry} 作为基准行业（样本数: {industry_counts_sorted.iloc[0]}）\")\n", "print(f\"将从因子矩阵中移除 {base_industry_col}\")\n", "\n", "# 更新行业因子列表（去掉基准行业）\n", "industry_factor_cols_final = [col for col in industry_factor_cols if col != base_industry_col]\n", "print(f\"最终行业因子数量: {len(industry_factor_cols_final)}\")\n", "\n", "# 创建国家因子（暂时不使用，避免与行业因子完全共线性）\n", "# merged_data['country_factor'] = 1\n", "print(\"注意：为避免完全共线性，暂不使用国家因子\")\n", "\n", "# 定义最终的因子列表\n", "style_factors = ['size_std', 'pb_std']\n", "all_factors = industry_factor_cols_final + style_factors\n", "\n", "print(f\"\\n最终因子构成:\")\n", "print(f\"- 行业因子: {len(industry_factor_cols_final)}\")\n", "print(f\"- 风格因子: {len(style_factors)}\")\n", "print(f\"- 总因子数: {len(all_factors)}\")\n", "print(f\"- 基准行业: {base_industry}（已排除）\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.4 构建因子暴露矩阵"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "构建因子暴露矩阵...\n", "因子暴露矩阵形状: (7728286, 34)\n", "因子暴露矩阵列: ['stock_code', 'trade_date', 'ind_801010.SI', 'ind_801030.SI', 'ind_801040.SI', 'ind_801050.SI', 'ind_801080.SI', 'ind_801110.SI', 'ind_801120.SI', 'ind_801130.SI', 'ind_801140.SI', 'ind_801150.SI', 'ind_801160.SI', 'ind_801170.SI', 'ind_801180.SI', 'ind_801200.SI', 'ind_801210.SI', 'ind_801230.SI', 'ind_801710.SI', 'ind_801720.SI', 'ind_801730.SI', 'ind_801740.SI', 'ind_801750.SI', 'ind_801760.SI', 'ind_801770.SI', 'ind_801780.SI', 'ind_801790.SI', 'ind_801880.SI', 'ind_801890.SI', 'ind_801950.SI', 'ind_801960.SI', 'ind_801970.SI', 'size_std', 'pb_std']\n", "设置索引后形状: (7728286, 32)\n", "\n", "因子暴露矩阵质量检查:\n", "缺失值数量: 0\n", "无穷值数量: 0\n", "\n", "风格因子统计:\n", "size_std: 均值=-0.000000, 标准差=0.999890\n", "pb_std: 均值=0.000000, 标准差=0.999890\n", "\n", "行业因子统计:\n", "各行业因子的总暴露度:\n", "ind_801030.SI    812323\n", "ind_801890.SI    655715\n", "ind_801150.SI    600619\n", "ind_801080.SI    510041\n", "ind_801730.SI    394144\n", "ind_801750.SI    358317\n", "ind_801180.SI    358226\n", "ind_801050.SI    300294\n", "ind_801880.SI    287167\n", "ind_801200.SI    258530\n", "dtype: int64\n"]}], "source": ["# 构建因子暴露矩阵\n", "print(\"\\n构建因子暴露矩阵...\")\n", "\n", "# 选择因子暴露矩阵的列\n", "factor_exposure_cols = ['stock_code', 'trade_date'] + all_factors\n", "factor_exposure_matrix = merged_data[factor_exposure_cols].copy()\n", "\n", "print(f\"因子暴露矩阵形状: {factor_exposure_matrix.shape}\")\n", "print(f\"因子暴露矩阵列: {factor_exposure_matrix.columns.tolist()}\")\n", "\n", "# 设置多重索引\n", "factor_exposure_matrix = factor_exposure_matrix.set_index(['stock_code', 'trade_date'])\n", "print(f\"设置索引后形状: {factor_exposure_matrix.shape}\")\n", "\n", "# 检查因子暴露矩阵的数据质量\n", "print(f\"\\n因子暴露矩阵质量检查:\")\n", "print(f\"缺失值数量: {factor_exposure_matrix.isnull().sum().sum()}\")\n", "print(f\"无穷值数量: {np.isinf(factor_exposure_matrix.values).sum()}\")\n", "\n", "# 检查因子的基本统计\n", "print(f\"\\n风格因子统计:\")\n", "for factor in style_factors:\n", "    factor_stats = factor_exposure_matrix[factor].describe()\n", "    print(f\"{factor}: 均值={factor_stats['mean']:.6f}, 标准差={factor_stats['std']:.6f}\")\n", "\n", "# 检查行业因子统计\n", "print(f\"\\n行业因子统计:\")\n", "industry_factor_sums = factor_exposure_matrix[industry_factor_cols_final].sum()\n", "print(f\"各行业因子的总暴露度:\")\n", "print(industry_factor_sums.sort_values(ascending=False).head(10))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.5 矩阵条件数检查"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "检查矩阵条件数...\n", "日期 2015-12-31: 条件数 = 3.45e+02\n", "日期 2016-01-04: 条件数 = 3.39e+02\n", "日期 2016-01-05: 条件数 = 3.34e+02\n", "日期 2016-01-06: 条件数 = 3.29e+02\n", "日期 2016-01-07: 条件数 = 3.28e+02\n", "\n", "条件数统计:\n", "平均条件数: 3.35e+02\n", "最大条件数: 3.45e+02\n", "✓ 矩阵条件数在可接受范围内\n"]}], "source": ["# 检查矩阵条件数\n", "print(\"\\n检查矩阵条件数...\")\n", "\n", "# 随机选择几个交易日检查条件数\n", "test_dates = sorted(merged_data['trade_date'].unique())[:5]\n", "\n", "condition_numbers = []\n", "for date in test_dates:\n", "    date_data = merged_data[merged_data['trade_date'] == date]\n", "    if len(date_data) > len(all_factors):\n", "        X = date_data[all_factors].values\n", "        weights = date_data['weight'].values\n", "        \n", "        # 加权矩阵\n", "        sqrt_weights = np.sqrt(weights)\n", "        X_weighted = X * sqrt_weights.reshape(-1, 1)\n", "        \n", "        # 计算条件数\n", "        XTX = X_weighted.T @ X_weighted\n", "        cond_num = np.linalg.cond(XTX)\n", "        condition_numbers.append(cond_num)\n", "        \n", "        print(f\"日期 {date.strftime('%Y-%m-%d')}: 条件数 = {cond_num:.2e}\")\n", "\n", "if len(condition_numbers) > 0:\n", "    avg_cond_num = np.mean(condition_numbers)\n", "    max_cond_num = np.max(condition_numbers)\n", "    \n", "    print(f\"\\n条件数统计:\")\n", "    print(f\"平均条件数: {avg_cond_num:.2e}\")\n", "    print(f\"最大条件数: {max_cond_num:.2e}\")\n", "    \n", "    if max_cond_num < 1e12:\n", "        print(\"✓ 矩阵条件数在可接受范围内\")\n", "    else:\n", "        print(\"⚠️ 矩阵条件数较大，可能存在多重共线性问题\")\n", "else:\n", "    print(\"无法计算条件数\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.6 可视化分析"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "可视化因子相关性...\n", "风格因子相关性矩阵:\n", "          size_std    pb_std\n", "size_std  1.000000  0.105168\n", "pb_std    0.105168  1.000000\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化因子相关性\n", "print(\"\\n可视化因子相关性...\")\n", "\n", "# 计算风格因子相关性\n", "style_factor_corr = factor_exposure_matrix[style_factors].corr()\n", "print(\"风格因子相关性矩阵:\")\n", "print(style_factor_corr)\n", "\n", "# 绘制相关性热力图\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(style_factor_corr, annot=True, cmap='coolwarm', center=0, \n", "            square=True, linewidths=0.5, cbar_kws={'label': '相关系数'})\n", "plt.title('风格因子相关性矩阵')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 行业因子分布\n", "plt.figure(figsize=(15, 8))\n", "industry_exposure_sums = factor_exposure_matrix[industry_factor_cols_final].sum().sort_values(ascending=False)\n", "plt.bar(range(len(industry_exposure_sums)), industry_exposure_sums.values)\n", "plt.title('各行业因子总暴露度')\n", "plt.xlabel('行业')\n", "plt.ylabel('总暴露度')\n", "plt.xticks(range(len(industry_exposure_sums)), \n", "           [col.replace('ind_', '') for col in industry_exposure_sums.index], \n", "           rotation=45)\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 风格因子时间序列（选择几个日期）\n", "sample_dates = sorted(merged_data['trade_date'].unique())[::100]  # 每100个交易日取一个\n", "if len(sample_dates) > 0:\n", "    fig, axes = plt.subplots(1, 2, figsize=(15, 6))\n", "    \n", "    for factor, ax in zip(style_factors, axes):\n", "        factor_means = []\n", "        for date in sample_dates:\n", "            date_data = merged_data[merged_data['trade_date'] == date]\n", "            if len(date_data) > 0:\n", "                factor_means.append(date_data[factor].mean())\n", "            else:\n", "                factor_means.append(0)\n", "        \n", "        ax.plot(sample_dates, factor_means, marker='o')\n", "        ax.set_title(f'{factor} 时间序列均值')\n", "        ax.set_xlabel('日期')\n", "        ax.set_ylabel('因子均值')\n", "        ax.tick_params(axis='x', rotation=45)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3.7 保存因子暴露矩阵"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "保存因子暴露矩阵...\n", "数据已保存到:\n", "- data/因子暴露矩阵X.csv\n", "- data/factor_exposure_matrix.h5\n", "- data/processed_data_step3.csv\n", "- data/processed_data_step3.h5\n", "- data/因子信息.csv\n", "\n", "第3步完成！\n", "关键成果:\n", "- ✅ 因子暴露矩阵构建完成 (7728286 × 32)\n", "- ✅ 行业因子哑变量创建完成 (30 个)\n", "- ✅ 多重共线性问题已处理（排除基准行业 801980.SI）\n", "- ✅ 矩阵条件数在可接受范围内\n", "- ✅ 数据质量良好，无缺失值\n", "\n", "可以继续运行第4步：计算被解释变量\n"]}], "source": ["# 保存因子暴露矩阵\n", "print(\"\\n保存因子暴露矩阵...\")\n", "\n", "# 保存因子暴露矩阵\n", "factor_exposure_matrix.to_csv('data/因子暴露矩阵X.csv')\n", "factor_exposure_matrix.to_hdf('data/factor_exposure_matrix.h5', key='data', mode='w')\n", "\n", "# 保存完整数据（包含所有字段）\n", "merged_data.to_csv('data/processed_data_step3.csv', index=False)\n", "merged_data.to_hdf('data/processed_data_step3.h5', key='data', mode='w')\n", "\n", "# 保存因子列表信息\n", "factor_info = {\n", "    '因子类型': ['行业因子'] * len(industry_factor_cols_final) + ['风格因子'] * len(style_factors),\n", "    '因子名称': industry_factor_cols_final + style_factors,\n", "    '因子描述': [f'行业{col.replace(\"ind_\", \"\")}' for col in industry_factor_cols_final] + \n", "                ['标准化市值因子', '标准化PB因子']\n", "}\n", "\n", "factor_info_df = pd.DataFrame(factor_info)\n", "factor_info_df.to_csv('data/因子信息.csv', index=False)\n", "\n", "print(f\"数据已保存到:\")\n", "print(f\"- data/因子暴露矩阵X.csv\")\n", "print(f\"- data/factor_exposure_matrix.h5\")\n", "print(f\"- data/processed_data_step3.csv\")\n", "print(f\"- data/processed_data_step3.h5\")\n", "print(f\"- data/因子信息.csv\")\n", "\n", "print(f\"\\n第3步完成！\")\n", "print(f\"关键成果:\")\n", "print(f\"- ✅ 因子暴露矩阵构建完成 ({factor_exposure_matrix.shape[0]} × {factor_exposure_matrix.shape[1]})\")\n", "print(f\"- ✅ 行业因子哑变量创建完成 ({len(industry_factor_cols_final)} 个)\")\n", "print(f\"- ✅ 多重共线性问题已处理（排除基准行业 {base_industry}）\")\n", "print(f\"- ✅ 矩阵条件数在可接受范围内\")\n", "print(f\"- ✅ 数据质量良好，无缺失值\")\n", "\n", "print(\"\\n可以继续运行第4步：计算被解释变量\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}