import pandas as pd
import numpy as np
import warnings
from datetime import datetime
import os
import matplotlib.pyplot as plt
import seaborn as sns

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

# 路径设置
DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data'
PROCESSED_PATH = r'C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\processed_data'
STYLE_PATH = r'C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\style_factors'
OUTPUT_PATH = r'C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\industry_factors'

# 创建输出目录
os.makedirs(OUTPUT_PATH, exist_ok=True)

print("Barra CNE6 行业因子构建开始...")

def load_base_data():
    """加载基础数据"""
    print("加载基础数据...")
    
    try:
        # 加载预处理数据
        processed_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.h5')
        if os.path.exists(processed_path):
            base_data = pd.read_hdf(processed_path, key='data')
            print(f"加载预处理数据: {base_data.shape}")
        else:
            csv_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.csv')
            base_data = pd.read_csv(csv_path)
            base_data['date'] = pd.to_datetime(base_data['date'])
            print(f"加载预处理数据(CSV): {base_data.shape}")
        
        return base_data
        
    except Exception as e:
        print(f"加载基础数据失败: {e}")
        return None

def load_industry_data():
    """加载行业分类数据"""
    print("\n加载行业分类数据...")
    
    try:
        industry_path = os.path.join(DATA_PATH, 'swind.xlsx')
        industry_data = pd.read_excel(industry_path)
        
        print(f"行业数据形状: {industry_data.shape}")
        print(f"列名: {list(industry_data.columns)}")
        
        if 'l1_name' in industry_data.columns:
            print(f"行业数量: {industry_data['l1_name'].nunique()}")
            print("\n行业分布:")
            industry_counts = industry_data['l1_name'].value_counts()
            print(industry_counts.head(10))
        
        return industry_data
        
    except Exception as e:
        print(f"加载行业数据失败: {e}")
        return None

# 加载数据
base_data = load_base_data()
industry_data = load_industry_data()

def process_industry_classification(base_data, industry_data):
    """处理行业分类"""
    print("\n=== 处理行业分类 ===")
    
    if base_data is None or industry_data is None:
        print("数据缺失，无法处理")
        return None
    
    # 处理行业数据：取每只股票最新的行业分类
    industry_clean = industry_data.copy()
    
    if 'in_date' in industry_clean.columns:
        industry_clean['in_date'] = pd.to_datetime(industry_clean['in_date'])
        industry_clean = industry_clean.sort_values('in_date').groupby('ts_code').last().reset_index()
        print("使用最新行业分类")
    
    # 合并行业信息到基础数据
    data_with_industry = base_data.merge(
        industry_clean[['ts_code']], 
        on='ts_code', 
        how='left'
    )
    
    print(f"合并前股票数: {base_data['ts_code'].nunique()}")
    print(f"合并后股票数: {data_with_industry['ts_code'].nunique()}")
    print(f"有行业信息的股票: {data_with_industry['l1_name'].notna().sum()}")
    print(f"行业覆盖率: {data_with_industry['l1_name'].notna().mean():.2%}")
    
    # 处理缺失行业的股票
    missing_industry = data_with_industry['l1_name'].isna()
    if missing_industry.sum() > 0:
        print(f"\n缺失行业信息的记录: {missing_industry.sum()}条")
        # 将缺失行业的股票归类为"其他"
        data_with_industry.loc[missing_industry, 'l1_name'] = '其他'
        print("已将缺失行业的股票归类为'其他'")
    
    # 统计最终行业分布
    print("\n最终行业分布:")
    final_industry_counts = data_with_industry['l1_name'].value_counts()
    print(final_industry_counts)
    
    return data_with_industry

# 处理行业分类
data_with_industry = process_industry_classification(base_data, industry_data)

def build_industry_factor_matrix(data):
    """构建行业因子矩阵"""
    print("\n=== 构建行业因子矩阵 ===")
    
    if data is None:
        print("数据为空")
        return None
    
    # 获取所有行业
    industries = sorted(data['l1_name'].unique())
    print(f"行业数量: {len(industries)}")
    print(f"行业列表: {industries}")
    
    # 为每个行业创建虚拟变量
    industry_matrix_list = []
    
    for date in sorted(data['date'].unique()):
        date_data = data[data['date'] == date].copy()
        
        if len(date_data) == 0:
            continue
        
        # 创建行业虚拟变量
        industry_dummies = pd.get_dummies(date_data['l1_name'], prefix='Industry')
        
        # 确保所有行业都有列（即使某天没有该行业的股票）
        for industry in industries:
            col_name = f'Industry_{industry}'
            if col_name not in industry_dummies.columns:
                industry_dummies[col_name] = 0
        
        # 按行业顺序排列列
        industry_cols = [f'Industry_{industry}' for industry in industries]
        industry_dummies = industry_dummies[industry_cols]
        
        # 添加基础信息
        result = pd.concat([
            date_data[['date', 'ts_code']].reset_index(drop=True),
            industry_dummies.reset_index(drop=True)
        ], axis=1)
        
        industry_matrix_list.append(result)
    
    if industry_matrix_list:
        industry_matrix = pd.concat(industry_matrix_list, ignore_index=True)
        
        print(f"\n行业因子矩阵构建完成:")
        print(f"数据形状: {industry_matrix.shape}")
        print(f"行业因子列: {[col for col in industry_matrix.columns if col.startswith('Industry_')]}")
        
        # 验证矩阵性质
        industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]
        row_sums = industry_matrix[industry_cols].sum(axis=1)
        print(f"每行和检验 (应该都是1): 最小值={row_sums.min()}, 最大值={row_sums.max()}")
        
        return industry_matrix
    else:
        print("构建行业因子矩阵失败")
        return None

# 构建行业因子矩阵
industry_matrix = build_industry_factor_matrix(data_with_industry)

def calculate_industry_weights(data):
    """计算行业权重（基于市值）"""
    print("\n=== 计算行业权重 ===")
    
    if data is None:
        print("数据为空")
        return None
    
    # 检查是否有市值数据
    if 'total_mv' not in data.columns:
        print("缺少市值数据，使用等权重")
        
        # 计算等权重
        industry_weights = data.groupby(['date', 'l1_name']).size().reset_index(name='stock_count')
        total_stocks = industry_weights.groupby('date')['stock_count'].transform('sum')
        industry_weights['weight'] = industry_weights['stock_count'] / total_stocks
        
    else:
        print("使用市值加权")
        
        # 计算市值权重
        industry_weights = data.groupby(['date', 'l1_name'])['total_mv'].sum().reset_index()
        total_mv = industry_weights.groupby('date')['total_mv'].transform('sum')
        industry_weights['weight'] = industry_weights['total_mv'] / total_mv
        industry_weights['stock_count'] = data.groupby(['date', 'l1_name']).size().reset_index(name='count')['count']
    
    print(f"行业权重数据形状: {industry_weights.shape}")
    
    # 显示最新日期的行业权重
    latest_date = industry_weights['date'].max()
    latest_weights = industry_weights[industry_weights['date'] == latest_date].copy()
    latest_weights = latest_weights.sort_values('weight', ascending=False)
    
    print(f"\n最新日期({latest_date.strftime('%Y-%m-%d')})行业权重:")
    for _, row in latest_weights.head(10).iterrows():
        print(f"  {row['l1_name']}: {row['weight']:.2%} (股票数: {row.get('stock_count', 'N/A')})")
    
    return industry_weights

# 计算行业权重
if data_with_industry is not None:
    industry_weights = calculate_industry_weights(data_with_industry)
else:
    industry_weights = None

def validate_industry_factors(industry_matrix, industry_weights):
    """验证行业因子有效性"""
    print("\n=== 行业因子有效性验证 ===")
    
    if industry_matrix is None:
        print("行业矩阵为空")
        return
    
    industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]
    
    # 1. 矩阵性质检验
    print("1. 矩阵性质检验:")
    
    # 每行和应该为1
    row_sums = industry_matrix[industry_cols].sum(axis=1)
    print(f"   每行和检验: 最小值={row_sums.min():.6f}, 最大值={row_sums.max():.6f}")
    print(f"   和为1的比例: {(row_sums == 1).mean():.2%}")
    
    # 每个元素应该为0或1
    all_values = industry_matrix[industry_cols].values.flatten()
    unique_values = np.unique(all_values)
    print(f"   矩阵中的唯一值: {unique_values}")
    
    # 2. 行业覆盖度检验
    print("\n2. 行业覆盖度检验:")
    
    for col in industry_cols:
        coverage = (industry_matrix[col] == 1).sum()
        total_records = len(industry_matrix)
        print(f"   {col}: {coverage}条记录 ({coverage/total_records:.2%})")
    
    # 3. 时间稳定性检验
    print("\n3. 时间稳定性检验:")
    
    monthly_coverage = industry_matrix.groupby(industry_matrix['date'].dt.to_period('M'))[industry_cols].sum()
    
    for col in industry_cols[:5]:  # 只显示前5个行业
        stability = monthly_coverage[col].std()
        mean_coverage = monthly_coverage[col].mean()
        cv = stability / mean_coverage if mean_coverage > 0 else np.inf
        print(f"   {col}: 月度覆盖度变异系数={cv:.3f}")
    
    # 4. 行业权重稳定性
    if industry_weights is not None:
        print("\n4. 行业权重稳定性:")
        
        # 计算权重变异系数
        weight_stability = industry_weights.groupby('l1_name')['weight'].agg(['mean', 'std'])
        weight_stability['cv'] = weight_stability['std'] / weight_stability['mean']
        weight_stability = weight_stability.sort_values('mean', ascending=False)
        
        print("   主要行业权重稳定性 (变异系数):")
        for industry, row in weight_stability.head(10).iterrows():
            print(f"     {industry}: 平均权重={row['mean']:.2%}, CV={row['cv']:.3f}")

# 验证行业因子
validate_industry_factors(industry_matrix, industry_weights)

def visualize_industry_factors(industry_weights, industry_matrix):
    """可视化行业因子"""
    print("\n=== 行业因子可视化 ===")
    
    if industry_weights is None:
        print("缺少行业权重数据")
        return
    
    # 1. 行业权重时间序列图
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # 选择主要行业（权重最大的前8个）
    latest_date = industry_weights['date'].max()
    latest_weights = industry_weights[industry_weights['date'] == latest_date]
    top_industries = latest_weights.nlargest(8, 'weight')['l1_name'].tolist()
    
    # 绘制主要行业权重时间序列
    for industry in top_industries:
        industry_ts = industry_weights[industry_weights['l1_name'] == industry]
        axes[0, 0].plot(industry_ts['date'], industry_ts['weight'], 
                       label=industry, linewidth=2, alpha=0.8)
    
    axes[0, 0].set_title('主要行业权重时间序列', fontsize=12, fontweight='bold')
    axes[0, 0].set_xlabel('日期')
    axes[0, 0].set_ylabel('权重')
    axes[0, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. 最新行业权重饼图
    latest_weights_sorted = latest_weights.sort_values('weight', ascending=False)
    
    # 取前8个行业，其余归为"其他"
    top_8 = latest_weights_sorted.head(8)
    others_weight = latest_weights_sorted.tail(-8)['weight'].sum()
    
    pie_data = top_8['weight'].tolist()
    pie_labels = top_8['l1_name'].tolist()
    
    if others_weight > 0:
        pie_data.append(others_weight)
        pie_labels.append('其他')
    
    axes[0, 1].pie(pie_data, labels=pie_labels, autopct='%1.1f%%', startangle=90)
    axes[0, 1].set_title(f'行业权重分布 ({latest_date.strftime("%Y-%m-%d")})', 
                        fontsize=12, fontweight='bold')
    
    # 3. 行业股票数量分布
    if 'stock_count' in latest_weights.columns:
        stock_counts = latest_weights.sort_values('stock_count', ascending=True)
        
        axes[1, 0].barh(range(len(stock_counts)), stock_counts['stock_count'])
        axes[1, 0].set_yticks(range(len(stock_counts)))
        axes[1, 0].set_yticklabels(stock_counts['l1_name'], fontsize=8)
        axes[1, 0].set_title('各行业股票数量', fontsize=12, fontweight='bold')
        axes[1, 0].set_xlabel('股票数量')
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. 行业权重vs股票数量散点图
    if 'stock_count' in latest_weights.columns:
        axes[1, 1].scatter(latest_weights['stock_count'], latest_weights['weight'], 
                          alpha=0.7, s=60)
        
        # 添加行业标签
        for _, row in latest_weights.iterrows():
            if row['weight'] > 0.05 or row['stock_count'] > 100:  # 只标注主要行业
                axes[1, 1].annotate(row['l1_name'], 
                                   (row['stock_count'], row['weight']),
                                   xytext=(5, 5), textcoords='offset points',
                                   fontsize=8, alpha=0.8)
        
        axes[1, 1].set_title('行业权重 vs 股票数量', fontsize=12, fontweight='bold')
        axes[1, 1].set_xlabel('股票数量')
        axes[1, 1].set_ylabel('权重')
        axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # 5. 行业因子矩阵热力图（样本）
    if industry_matrix is not None:
        industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]
        
        # 取最新日期的样本数据
        latest_matrix = industry_matrix[industry_matrix['date'] == industry_matrix['date'].max()]
        sample_matrix = latest_matrix[industry_cols].head(50)  # 取前50只股票作为样本
        
        plt.figure(figsize=(12, 8))
        sns.heatmap(sample_matrix.T, cmap='Blues', cbar_kws={'label': '暴露度'})
        plt.title('行业因子矩阵样本 (前50只股票)', fontsize=14, fontweight='bold')
        plt.xlabel('股票序号')
        plt.ylabel('行业因子')
        plt.tight_layout()
        plt.show()

# 可视化
visualize_industry_factors(industry_weights, industry_matrix)

def save_industry_factors(industry_matrix, industry_weights, output_path):
    """保存行业因子数据"""
    print("\n=== 保存行业因子数据 ===")
    
    if industry_matrix is None:
        print("行业矩阵为空，无法保存")
        return False
    
    try:
        # 1. 保存行业因子矩阵
        matrix_h5_path = os.path.join(output_path, 'industry_factor_matrix.h5')
        industry_matrix.to_hdf(matrix_h5_path, key='data', mode='w', format='table')
        print(f"已保存行业因子矩阵: {matrix_h5_path}")
        
        # 2. 保存行业权重
        if industry_weights is not None:
            weights_h5_path = os.path.join(output_path, 'industry_weights.h5')
            industry_weights.to_hdf(weights_h5_path, key='data', mode='w', format='table')
            print(f"已保存行业权重: {weights_h5_path}")
        
        # 3. 保存CSV格式
        matrix_csv_path = os.path.join(output_path, 'industry_factor_matrix.csv')
        industry_matrix.to_csv(matrix_csv_path, index=False)
        print(f"已保存矩阵CSV: {matrix_csv_path}")
        
        if industry_weights is not None:
            weights_csv_path = os.path.join(output_path, 'industry_weights.csv')
            industry_weights.to_csv(weights_csv_path, index=False)
            print(f"已保存权重CSV: {weights_csv_path}")
        
        # 4. 保存行业因子摘要
        summary_path = os.path.join(output_path, 'industry_factors_summary.txt')
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("Barra CNE6 行业因子摘要\n")
            f.write("=" * 50 + "\n\n")
            
            # 行业因子矩阵信息
            f.write("行业因子矩阵信息:\n")
            f.write(f"数据形状: {industry_matrix.shape}\n")
            f.write(f"日期范围: {industry_matrix['date'].min()} 到 {industry_matrix['date'].max()}\n")
            f.write(f"股票数量: {industry_matrix['ts_code'].nunique()}\n")
            
            industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]
            f.write(f"行业数量: {len(industry_cols)}\n")
            f.write(f"行业列表: {[col.replace('Industry_', '') for col in industry_cols]}\n\n")
            
            # 行业权重信息
            if industry_weights is not None:
                f.write("行业权重信息:\n")
                latest_date = industry_weights['date'].max()
                latest_weights = industry_weights[industry_weights['date'] == latest_date]
                latest_weights = latest_weights.sort_values('weight', ascending=False)
                
                f.write(f"最新日期: {latest_date}\n")
                f.write("行业权重排序:\n")
                for _, row in latest_weights.iterrows():
                    stock_info = f" (股票数: {row.get('stock_count', 'N/A')})" if 'stock_count' in row else ""
                    f.write(f"  {row['l1_name']}: {row['weight']:.2%}{stock_info}\n")
        
        print(f"已保存行业因子摘要: {summary_path}")
        
        # 5. 保存行业映射表
        mapping_path = os.path.join(output_path, 'industry_mapping.csv')
        industry_mapping = pd.DataFrame({
            'industry_factor': industry_cols,
            'industry_name': [col.replace('Industry_', '') for col in industry_cols]
        })
        industry_mapping.to_csv(mapping_path, index=False)
        print(f"已保存行业映射表: {mapping_path}")
        
        return True
        
    except Exception as e:
        print(f"保存行业因子数据失败: {e}")
        return False

# 保存数据
if industry_matrix is not None:
    save_success = save_industry_factors(industry_matrix, industry_weights, OUTPUT_PATH)
    
    if save_success:
        print("\n=== 行业因子构建完成 ===")
        
        industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]
        print(f"成功构建{len(industry_cols)}个行业因子")
        print(f"数据保存路径: {OUTPUT_PATH}")
        
        print("\n可用文件:")
        print("- industry_factor_matrix.h5 (行业因子矩阵)")
        print("- industry_weights.h5 (行业权重)")
        print("- industry_factor_matrix.csv (矩阵CSV)")
        print("- industry_weights.csv (权重CSV)")
        print("- industry_mapping.csv (行业映射表)")
        print("- industry_factors_summary.txt (摘要)")
else:
    print("\n行业因子构建失败")

def next_steps_plan():
    """下一步计划"""
    print("\n=== 下一步计划 ===")
    
    print("已完成:")
    print("✓ 01_数据预处理.ipynb - 数据清洗和预处理")
    print("✓ 02_风格因子构建.ipynb - 风格因子构建")
    print("✓ 03_行业因子构建.ipynb - 行业因子构建")
    
    print("\n待完成:")
    print("□ 04_因子收益率计算.ipynb - 计算因子收益率")
    print("□ 05_协方差矩阵估计.ipynb - 估计因子协方差矩阵")
    print("□ 06_特质风险模型.ipynb - 构建特质风险模型")
    print("□ 07_风险模型验证.ipynb - 模型验证和回测")
    
    print("\n关键数据需求:")
    print("1. 股票收益率数据 - 用于因子收益率回归")
    print("2. 更多风格因子原始数据 - 构建完整的6个风格因子")
    print("3. 基准指数数据 - 用于模型验证")
    print("4. 交易成本数据 - 用于实际应用")
    
    print("\n技术要点:")
    print("1. 因子收益率计算需要使用加权最小二乘法")
    print("2. 协方差矩阵估计需要考虑时间衰减")
    print("3. 特质风险模型需要处理异方差性")
    print("4. 模型验证需要多个维度的测试")

next_steps_plan()