{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Barra CNE6 风险模型复现 - 第三步：行业因子构建\n", "\n", "## 目标\n", "构建Barra CNE6模型的行业因子矩阵：\n", "1. **行业分类**: 基于中信一级行业分类\n", "2. **行业因子矩阵**: 构建股票-行业暴露度矩阵\n", "3. **行业权重**: 计算各行业的市值权重\n", "4. **行业因子验证**: 验证行业因子的有效性\n", "\n", "## Barra行业因子特点\n", "- 每只股票在且仅在一个行业中有暴露度1\n", "- 行业因子相互正交\n", "- 行业因子捕捉行业系统性风险\n", "- 需要处理行业变更和新股上市"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Barra CNE6 行业因子构建开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import warnings\n", "from datetime import datetime\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 路径设置\n", "DATA_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\单因子\\data'\n", "PROCESSED_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\processed_data'\n", "STYLE_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\style_factors'\n", "OUTPUT_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\industry_factors'\n", "\n", "# 创建输出目录\n", "os.makedirs(OUTPUT_PATH, exist_ok=True)\n", "\n", "print(\"Barra CNE6 行业因子构建开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载基础数据...\n", "加载预处理数据: (98527, 5)\n", "\n", "加载行业分类数据...\n", "行业数据形状: (7827, 10)\n", "列名: ['ts_code', 'name', 'in_date', 'out_date', 'l1_code', 'l1_name', 'l2_code', 'l2_name', 'l3_code', 'l3_name']\n", "行业数量: 31\n", "\n", "行业分布:\n", "l1_name\n", "机械设备    830\n", "基础化工    806\n", "电子      683\n", "医药生物    638\n", "电力设备    461\n", "计算机     422\n", "汽车      340\n", "有色金属    254\n", "房地产     248\n", "建筑装饰    229\n", "Name: count, dtype: int64\n"]}], "source": ["def load_base_data():\n", "    \"\"\"加载基础数据\"\"\"\n", "    print(\"加载基础数据...\")\n", "    \n", "    try:\n", "        # 加载预处理数据\n", "        processed_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.h5')\n", "        if os.path.exists(processed_path):\n", "            base_data = pd.read_hdf(processed_path, key='data')\n", "            print(f\"加载预处理数据: {base_data.shape}\")\n", "        else:\n", "            csv_path = os.path.join(PROCESSED_PATH, 'factor_data_processed.csv')\n", "            base_data = pd.read_csv(csv_path)\n", "            base_data['date'] = pd.to_datetime(base_data['date'])\n", "            print(f\"加载预处理数据(CSV): {base_data.shape}\")\n", "        \n", "        return base_data\n", "        \n", "    except Exception as e:\n", "        print(f\"加载基础数据失败: {e}\")\n", "        return None\n", "\n", "def load_industry_data():\n", "    \"\"\"加载行业分类数据\"\"\"\n", "    print(\"\\n加载行业分类数据...\")\n", "    \n", "    try:\n", "        industry_path = os.path.join(DATA_PATH, 'swind.xlsx')\n", "        industry_data = pd.read_excel(industry_path)\n", "        \n", "        print(f\"行业数据形状: {industry_data.shape}\")\n", "        print(f\"列名: {list(industry_data.columns)}\")\n", "        \n", "        if 'l1_name' in industry_data.columns:\n", "            print(f\"行业数量: {industry_data['l1_name'].nunique()}\")\n", "            print(\"\\n行业分布:\")\n", "            industry_counts = industry_data['l1_name'].value_counts()\n", "            print(industry_counts.head(10))\n", "        \n", "        return industry_data\n", "        \n", "    except Exception as e:\n", "        print(f\"加载行业数据失败: {e}\")\n", "        return None\n", "\n", "# 加载数据\n", "base_data = load_base_data()\n", "industry_data = load_industry_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 行业分类处理"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 处理行业分类 ===\n", "使用最新行业分类\n", "合并前股票数: 2681\n", "合并后股票数: 2681\n", "有行业信息的股票: 98527\n", "行业覆盖率: 100.00%\n", "\n", "最终行业分布:\n", "l1_name\n", "医药生物    8288\n", "机械设备    7848\n", "基础化工    6560\n", "电力设备    6075\n", "计算机     5554\n", "电子      5082\n", "汽车      4421\n", "房地产     4254\n", "公用事业    3591\n", "有色金属    3476\n", "交通运输    3341\n", "传媒      3295\n", "商贸零售    2983\n", "建筑装饰    2953\n", "食品饮料    2708\n", "农林牧渔    2667\n", "纺织服饰    2453\n", "国防军工    2426\n", "非银金融    2414\n", "环保      2280\n", "轻工制造    2139\n", "通信      2034\n", "社会服务    1939\n", "建筑材料    1935\n", "家用电器    1908\n", "钢铁      1396\n", "石油石化    1373\n", "煤炭      1358\n", "综合       889\n", "银行       613\n", "美容护理     274\n", "Name: count, dtype: int64\n"]}], "source": ["def process_industry_classification(base_data, industry_data):\n", "    \"\"\"处理行业分类\"\"\"\n", "    print(\"\\n=== 处理行业分类 ===\")\n", "    \n", "    if base_data is None or industry_data is None:\n", "        print(\"数据缺失，无法处理\")\n", "        return None\n", "    \n", "    # 处理行业数据：取每只股票最新的行业分类\n", "    industry_clean = industry_data.copy()\n", "    \n", "    if 'in_date' in industry_clean.columns:\n", "        industry_clean['in_date'] = pd.to_datetime(industry_clean['in_date'])\n", "        industry_clean = industry_clean.sort_values('in_date').groupby('ts_code').last().reset_index()\n", "        print(\"使用最新行业分类\")\n", "    \n", "    # 合并行业信息到基础数据\n", "    data_with_industry = base_data.merge(\n", "        industry_clean[['ts_code']], \n", "        on='ts_code', \n", "        how='left'\n", "    )\n", "    \n", "    print(f\"合并前股票数: {base_data['ts_code'].nunique()}\")\n", "    print(f\"合并后股票数: {data_with_industry['ts_code'].nunique()}\")\n", "    print(f\"有行业信息的股票: {data_with_industry['l1_name'].notna().sum()}\")\n", "    print(f\"行业覆盖率: {data_with_industry['l1_name'].notna().mean():.2%}\")\n", "    \n", "    # 处理缺失行业的股票\n", "    missing_industry = data_with_industry['l1_name'].isna()\n", "    if missing_industry.sum() > 0:\n", "        print(f\"\\n缺失行业信息的记录: {missing_industry.sum()}条\")\n", "        # 将缺失行业的股票归类为\"其他\"\n", "        data_with_industry.loc[missing_industry, 'l1_name'] = '其他'\n", "        print(\"已将缺失行业的股票归类为'其他'\")\n", "    \n", "    # 统计最终行业分布\n", "    print(\"\\n最终行业分布:\")\n", "    final_industry_counts = data_with_industry['l1_name'].value_counts()\n", "    print(final_industry_counts)\n", "    \n", "    return data_with_industry\n", "\n", "# 处理行业分类\n", "data_with_industry = process_industry_classification(base_data, industry_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 构建行业因子矩阵"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 构建行业因子矩阵 ===\n", "行业数量: 31\n", "行业列表: ['交通运输', '传媒', '公用事业', '农林牧渔', '医药生物', '商贸零售', '国防军工', '基础化工', '家用电器', '建筑材料', '建筑装饰', '房地产', '有色金属', '机械设备', '汽车', '煤炭', '环保', '电力设备', '电子', '石油石化', '社会服务', '纺织服饰', '综合', '美容护理', '计算机', '轻工制造', '通信', '钢铁', '银行', '非银金融', '食品饮料']\n", "\n", "行业因子矩阵构建完成:\n", "数据形状: (98527, 33)\n", "行业因子列: ['Industry_交通运输', 'Industry_传媒', 'Industry_公用事业', 'Industry_农林牧渔', 'Industry_医药生物', 'Industry_商贸零售', 'Industry_国防军工', 'Industry_基础化工', 'Industry_家用电器', 'Industry_建筑材料', 'Industry_建筑装饰', 'Industry_房地产', 'Industry_有色金属', 'Industry_机械设备', 'Industry_汽车', 'Industry_煤炭', 'Industry_环保', 'Industry_电力设备', 'Industry_电子', 'Industry_石油石化', 'Industry_社会服务', 'Industry_纺织服饰', 'Industry_综合', 'Industry_美容护理', 'Industry_计算机', 'Industry_轻工制造', 'Industry_通信', 'Industry_钢铁', 'Industry_银行', 'Industry_非银金融', 'Industry_食品饮料']\n", "每行和检验 (应该都是1): 最小值=1, 最大值=1\n"]}], "source": ["def build_industry_factor_matrix(data):\n", "    \"\"\"构建行业因子矩阵\"\"\"\n", "    print(\"\\n=== 构建行业因子矩阵 ===\")\n", "    \n", "    if data is None:\n", "        print(\"数据为空\")\n", "        return None\n", "    \n", "    # 获取所有行业\n", "    industries = sorted(data['l1_name'].unique())\n", "    print(f\"行业数量: {len(industries)}\")\n", "    print(f\"行业列表: {industries}\")\n", "    \n", "    # 为每个行业创建虚拟变量\n", "    industry_matrix_list = []\n", "    \n", "    for date in sorted(data['date'].unique()):\n", "        date_data = data[data['date'] == date].copy()\n", "        \n", "        if len(date_data) == 0:\n", "            continue\n", "        \n", "        # 创建行业虚拟变量\n", "        industry_dummies = pd.get_dummies(date_data['l1_name'], prefix='Industry')\n", "        \n", "        # 确保所有行业都有列（即使某天没有该行业的股票）\n", "        for industry in industries:\n", "            col_name = f'Industry_{industry}'\n", "            if col_name not in industry_dummies.columns:\n", "                industry_dummies[col_name] = 0\n", "        \n", "        # 按行业顺序排列列\n", "        industry_cols = [f'Industry_{industry}' for industry in industries]\n", "        industry_dummies = industry_dummies[industry_cols]\n", "        \n", "        # 添加基础信息\n", "        result = pd.concat([\n", "            date_data[['date', 'ts_code']].reset_index(drop=True),\n", "            industry_dummies.reset_index(drop=True)\n", "        ], axis=1)\n", "        \n", "        industry_matrix_list.append(result)\n", "    \n", "    if industry_matrix_list:\n", "        industry_matrix = pd.concat(industry_matrix_list, ignore_index=True)\n", "        \n", "        print(f\"\\n行业因子矩阵构建完成:\")\n", "        print(f\"数据形状: {industry_matrix.shape}\")\n", "        print(f\"行业因子列: {[col for col in industry_matrix.columns if col.startswith('Industry_')]}\")\n", "        \n", "        # 验证矩阵性质\n", "        industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]\n", "        row_sums = industry_matrix[industry_cols].sum(axis=1)\n", "        print(f\"每行和检验 (应该都是1): 最小值={row_sums.min()}, 最大值={row_sums.max()}\")\n", "        \n", "        return industry_matrix\n", "    else:\n", "        print(\"构建行业因子矩阵失败\")\n", "        return None\n", "\n", "# 构建行业因子矩阵\n", "industry_matrix = build_industry_factor_matrix(data_with_industry)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 计算行业权重"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 计算行业权重 ===\n", "使用市值加权\n", "行业权重数据形状: (1240, 5)\n", "\n", "最新日期(2016-03-03)行业权重:\n", "  房地产: 11.58% (股票数: 42)\n", "  医药生物: 7.69% (股票数: 38)\n", "  非银金融: 7.48% (股票数: 17)\n", "  电子: 5.18% (股票数: 17)\n", "  食品饮料: 4.70% (股票数: 12)\n", "  机械设备: 4.68% (股票数: 25)\n", "  公用事业: 4.42% (股票数: 30)\n", "  传媒: 4.38% (股票数: 12)\n", "  汽车: 4.33% (股票数: 24)\n", "  家用电器: 4.30% (股票数: 14)\n"]}], "source": ["def calculate_industry_weights(data):\n", "    \"\"\"计算行业权重（基于市值）\"\"\"\n", "    print(\"\\n=== 计算行业权重 ===\")\n", "    \n", "    if data is None:\n", "        print(\"数据为空\")\n", "        return None\n", "    \n", "    # 检查是否有市值数据\n", "    if 'total_mv' not in data.columns:\n", "        print(\"缺少市值数据，使用等权重\")\n", "        \n", "        # 计算等权重\n", "        industry_weights = data.groupby(['date', 'l1_name']).size().reset_index(name='stock_count')\n", "        total_stocks = industry_weights.groupby('date')['stock_count'].transform('sum')\n", "        industry_weights['weight'] = industry_weights['stock_count'] / total_stocks\n", "        \n", "    else:\n", "        print(\"使用市值加权\")\n", "        \n", "        # 计算市值权重\n", "        industry_weights = data.groupby(['date', 'l1_name'])['total_mv'].sum().reset_index()\n", "        total_mv = industry_weights.groupby('date')['total_mv'].transform('sum')\n", "        industry_weights['weight'] = industry_weights['total_mv'] / total_mv\n", "        industry_weights['stock_count'] = data.groupby(['date', 'l1_name']).size().reset_index(name='count')['count']\n", "    \n", "    print(f\"行业权重数据形状: {industry_weights.shape}\")\n", "    \n", "    # 显示最新日期的行业权重\n", "    latest_date = industry_weights['date'].max()\n", "    latest_weights = industry_weights[industry_weights['date'] == latest_date].copy()\n", "    latest_weights = latest_weights.sort_values('weight', ascending=False)\n", "    \n", "    print(f\"\\n最新日期({latest_date.strftime('%Y-%m-%d')})行业权重:\")\n", "    for _, row in latest_weights.head(10).iterrows():\n", "        print(f\"  {row['l1_name']}: {row['weight']:.2%} (股票数: {row.get('stock_count', 'N/A')})\")\n", "    \n", "    return industry_weights\n", "\n", "# 计算行业权重\n", "if data_with_industry is not None:\n", "    industry_weights = calculate_industry_weights(data_with_industry)\n", "else:\n", "    industry_weights = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 行业因子有效性验证"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 行业因子有效性验证 ===\n", "1. 矩阵性质检验:\n", "   每行和检验: 最小值=1.000000, 最大值=1.000000\n", "   和为1的比例: 100.00%\n", "   矩阵中的唯一值: [False  True]\n", "\n", "2. 行业覆盖度检验:\n", "   Industry_交通运输: 3341条记录 (3.39%)\n", "   Industry_传媒: 3295条记录 (3.34%)\n", "   Industry_公用事业: 3591条记录 (3.64%)\n", "   Industry_农林牧渔: 2667条记录 (2.71%)\n", "   Industry_医药生物: 8288条记录 (8.41%)\n", "   Industry_商贸零售: 2983条记录 (3.03%)\n", "   Industry_国防军工: 2426条记录 (2.46%)\n", "   Industry_基础化工: 6560条记录 (6.66%)\n", "   Industry_家用电器: 1908条记录 (1.94%)\n", "   Industry_建筑材料: 1935条记录 (1.96%)\n", "   Industry_建筑装饰: 2953条记录 (3.00%)\n", "   Industry_房地产: 4254条记录 (4.32%)\n", "   Industry_有色金属: 3476条记录 (3.53%)\n", "   Industry_机械设备: 7848条记录 (7.97%)\n", "   Industry_汽车: 4421条记录 (4.49%)\n", "   Industry_煤炭: 1358条记录 (1.38%)\n", "   Industry_环保: 2280条记录 (2.31%)\n", "   Industry_电力设备: 6075条记录 (6.17%)\n", "   Industry_电子: 5082条记录 (5.16%)\n", "   Industry_石油石化: 1373条记录 (1.39%)\n", "   Industry_社会服务: 1939条记录 (1.97%)\n", "   Industry_纺织服饰: 2453条记录 (2.49%)\n", "   Industry_综合: 889条记录 (0.90%)\n", "   Industry_美容护理: 274条记录 (0.28%)\n", "   Industry_计算机: 5554条记录 (5.64%)\n", "   Industry_轻工制造: 2139条记录 (2.17%)\n", "   Industry_通信: 2034条记录 (2.06%)\n", "   Industry_钢铁: 1396条记录 (1.42%)\n", "   Industry_银行: 613条记录 (0.62%)\n", "   Industry_非银金融: 2414条记录 (2.45%)\n", "   Industry_食品饮料: 2708条记录 (2.75%)\n", "\n", "3. 时间稳定性检验:\n", "   Industry_交通运输: 月度覆盖度变异系数=0.981\n", "   Industry_传媒: 月度覆盖度变异系数=0.985\n", "   Industry_公用事业: 月度覆盖度变异系数=0.976\n", "   Industry_农林牧渔: 月度覆盖度变异系数=0.981\n", "   Industry_医药生物: 月度覆盖度变异系数=0.988\n", "\n", "4. 行业权重稳定性:\n", "   主要行业权重稳定性 (变异系数):\n", "     银行: 平均权重=16.24%, CV=0.144\n", "     非银金融: 平均权重=8.60%, CV=0.030\n", "     医药生物: 平均权重=5.98%, CV=0.050\n", "     石油石化: 平均权重=5.37%, CV=0.134\n", "     机械设备: 平均权重=4.39%, CV=0.020\n", "     房地产: 平均权重=4.12%, CV=0.295\n", "     计算机: 平均权重=3.94%, CV=0.098\n", "     汽车: 平均权重=3.80%, CV=0.028\n", "     交通运输: 平均权重=3.71%, CV=0.044\n", "     电力设备: 平均权重=3.58%, CV=0.089\n"]}], "source": ["def validate_industry_factors(industry_matrix, industry_weights):\n", "    \"\"\"验证行业因子有效性\"\"\"\n", "    print(\"\\n=== 行业因子有效性验证 ===\")\n", "    \n", "    if industry_matrix is None:\n", "        print(\"行业矩阵为空\")\n", "        return\n", "    \n", "    industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]\n", "    \n", "    # 1. 矩阵性质检验\n", "    print(\"1. 矩阵性质检验:\")\n", "    \n", "    # 每行和应该为1\n", "    row_sums = industry_matrix[industry_cols].sum(axis=1)\n", "    print(f\"   每行和检验: 最小值={row_sums.min():.6f}, 最大值={row_sums.max():.6f}\")\n", "    print(f\"   和为1的比例: {(row_sums == 1).mean():.2%}\")\n", "    \n", "    # 每个元素应该为0或1\n", "    all_values = industry_matrix[industry_cols].values.flatten()\n", "    unique_values = np.unique(all_values)\n", "    print(f\"   矩阵中的唯一值: {unique_values}\")\n", "    \n", "    # 2. 行业覆盖度检验\n", "    print(\"\\n2. 行业覆盖度检验:\")\n", "    \n", "    for col in industry_cols:\n", "        coverage = (industry_matrix[col] == 1).sum()\n", "        total_records = len(industry_matrix)\n", "        print(f\"   {col}: {coverage}条记录 ({coverage/total_records:.2%})\")\n", "    \n", "    # 3. 时间稳定性检验\n", "    print(\"\\n3. 时间稳定性检验:\")\n", "    \n", "    monthly_coverage = industry_matrix.groupby(industry_matrix['date'].dt.to_period('M'))[industry_cols].sum()\n", "    \n", "    for col in industry_cols[:5]:  # 只显示前5个行业\n", "        stability = monthly_coverage[col].std()\n", "        mean_coverage = monthly_coverage[col].mean()\n", "        cv = stability / mean_coverage if mean_coverage > 0 else np.inf\n", "        print(f\"   {col}: 月度覆盖度变异系数={cv:.3f}\")\n", "    \n", "    # 4. 行业权重稳定性\n", "    if industry_weights is not None:\n", "        print(\"\\n4. 行业权重稳定性:\")\n", "        \n", "        # 计算权重变异系数\n", "        weight_stability = industry_weights.groupby('l1_name')['weight'].agg(['mean', 'std'])\n", "        weight_stability['cv'] = weight_stability['std'] / weight_stability['mean']\n", "        weight_stability = weight_stability.sort_values('mean', ascending=False)\n", "        \n", "        print(\"   主要行业权重稳定性 (变异系数):\")\n", "        for industry, row in weight_stability.head(10).iterrows():\n", "            print(f\"     {industry}: 平均权重={row['mean']:.2%}, CV={row['cv']:.3f}\")\n", "\n", "# 验证行业因子\n", "validate_industry_factors(industry_matrix, industry_weights)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 行业因子可视化"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 行业因子可视化 ===\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1500x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def visualize_industry_factors(industry_weights, industry_matrix):\n", "    \"\"\"可视化行业因子\"\"\"\n", "    print(\"\\n=== 行业因子可视化 ===\")\n", "    \n", "    if industry_weights is None:\n", "        print(\"缺少行业权重数据\")\n", "        return\n", "    \n", "    # 1. 行业权重时间序列图\n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 10))\n", "    \n", "    # 选择主要行业（权重最大的前8个）\n", "    latest_date = industry_weights['date'].max()\n", "    latest_weights = industry_weights[industry_weights['date'] == latest_date]\n", "    top_industries = latest_weights.nlargest(8, 'weight')['l1_name'].tolist()\n", "    \n", "    # 绘制主要行业权重时间序列\n", "    for industry in top_industries:\n", "        industry_ts = industry_weights[industry_weights['l1_name'] == industry]\n", "        axes[0, 0].plot(industry_ts['date'], industry_ts['weight'], \n", "                       label=industry, linewidth=2, alpha=0.8)\n", "    \n", "    axes[0, 0].set_title('主要行业权重时间序列', fontsize=12, fontweight='bold')\n", "    axes[0, 0].set_xlabel('日期')\n", "    axes[0, 0].set_ylabel('权重')\n", "    axes[0, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. 最新行业权重饼图\n", "    latest_weights_sorted = latest_weights.sort_values('weight', ascending=False)\n", "    \n", "    # 取前8个行业，其余归为\"其他\"\n", "    top_8 = latest_weights_sorted.head(8)\n", "    others_weight = latest_weights_sorted.tail(-8)['weight'].sum()\n", "    \n", "    pie_data = top_8['weight'].tolist()\n", "    pie_labels = top_8['l1_name'].tolist()\n", "    \n", "    if others_weight > 0:\n", "        pie_data.append(others_weight)\n", "        pie_labels.append('其他')\n", "    \n", "    axes[0, 1].pie(pie_data, labels=pie_labels, autopct='%1.1f%%', startangle=90)\n", "    axes[0, 1].set_title(f'行业权重分布 ({latest_date.strftime(\"%Y-%m-%d\")})', \n", "                        fontsize=12, fontweight='bold')\n", "    \n", "    # 3. 行业股票数量分布\n", "    if 'stock_count' in latest_weights.columns:\n", "        stock_counts = latest_weights.sort_values('stock_count', ascending=True)\n", "        \n", "        axes[1, 0].barh(range(len(stock_counts)), stock_counts['stock_count'])\n", "        axes[1, 0].set_yticks(range(len(stock_counts)))\n", "        axes[1, 0].set_yticklabels(stock_counts['l1_name'], fontsize=8)\n", "        axes[1, 0].set_title('各行业股票数量', fontsize=12, fontweight='bold')\n", "        axes[1, 0].set_xlabel('股票数量')\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "    # 4. 行业权重vs股票数量散点图\n", "    if 'stock_count' in latest_weights.columns:\n", "        axes[1, 1].scatter(latest_weights['stock_count'], latest_weights['weight'], \n", "                          alpha=0.7, s=60)\n", "        \n", "        # 添加行业标签\n", "        for _, row in latest_weights.iterrows():\n", "            if row['weight'] > 0.05 or row['stock_count'] > 100:  # 只标注主要行业\n", "                axes[1, 1].annotate(row['l1_name'], \n", "                                   (row['stock_count'], row['weight']),\n", "                                   xytext=(5, 5), textcoords='offset points',\n", "                                   fontsize=8, alpha=0.8)\n", "        \n", "        axes[1, 1].set_title('行业权重 vs 股票数量', fontsize=12, fontweight='bold')\n", "        axes[1, 1].set_xlabel('股票数量')\n", "        axes[1, 1].set_ylabel('权重')\n", "        axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 5. 行业因子矩阵热力图（样本）\n", "    if industry_matrix is not None:\n", "        industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]\n", "        \n", "        # 取最新日期的样本数据\n", "        latest_matrix = industry_matrix[industry_matrix['date'] == industry_matrix['date'].max()]\n", "        sample_matrix = latest_matrix[industry_cols].head(50)  # 取前50只股票作为样本\n", "        \n", "        plt.figure(figsize=(12, 8))\n", "        sns.heatmap(sample_matrix.T, cmap='Blues', cbar_kws={'label': '暴露度'})\n", "        plt.title('行业因子矩阵样本 (前50只股票)', fontsize=14, fontweight='bold')\n", "        plt.xlabel('股票序号')\n", "        plt.ylabel('行业因子')\n", "        plt.tight_layout()\n", "        plt.show()\n", "\n", "# 可视化\n", "visualize_industry_factors(industry_weights, industry_matrix)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 保存行业因子数据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def save_industry_factors(industry_matrix, industry_weights, output_path):\n", "    \"\"\"保存行业因子数据\"\"\"\n", "    print(\"\\n=== 保存行业因子数据 ===\")\n", "    \n", "    if industry_matrix is None:\n", "        print(\"行业矩阵为空，无法保存\")\n", "        return False\n", "    \n", "    try:\n", "        # 1. 保存行业因子矩阵\n", "        matrix_h5_path = os.path.join(output_path, 'industry_factor_matrix.h5')\n", "        industry_matrix.to_hdf(matrix_h5_path, key='data', mode='w', format='table')\n", "        print(f\"已保存行业因子矩阵: {matrix_h5_path}\")\n", "        \n", "        # 2. 保存行业权重\n", "        if industry_weights is not None:\n", "            weights_h5_path = os.path.join(output_path, 'industry_weights.h5')\n", "            industry_weights.to_hdf(weights_h5_path, key='data', mode='w', format='table')\n", "            print(f\"已保存行业权重: {weights_h5_path}\")\n", "        \n", "        # 3. 保存CSV格式\n", "        matrix_csv_path = os.path.join(output_path, 'industry_factor_matrix.csv')\n", "        industry_matrix.to_csv(matrix_csv_path, index=False)\n", "        print(f\"已保存矩阵CSV: {matrix_csv_path}\")\n", "        \n", "        if industry_weights is not None:\n", "            weights_csv_path = os.path.join(output_path, 'industry_weights.csv')\n", "            industry_weights.to_csv(weights_csv_path, index=False)\n", "            print(f\"已保存权重CSV: {weights_csv_path}\")\n", "        \n", "        # 4. 保存行业因子摘要\n", "        summary_path = os.path.join(output_path, 'industry_factors_summary.txt')\n", "        with open(summary_path, 'w', encoding='utf-8') as f:\n", "            f.write(\"Barra CNE6 行业因子摘要\\n\")\n", "            f.write(\"=\" * 50 + \"\\n\\n\")\n", "            \n", "            # 行业因子矩阵信息\n", "            f.write(\"行业因子矩阵信息:\\n\")\n", "            f.write(f\"数据形状: {industry_matrix.shape}\\n\")\n", "            f.write(f\"日期范围: {industry_matrix['date'].min()} 到 {industry_matrix['date'].max()}\\n\")\n", "            f.write(f\"股票数量: {industry_matrix['ts_code'].nunique()}\\n\")\n", "            \n", "            industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]\n", "            f.write(f\"行业数量: {len(industry_cols)}\\n\")\n", "            f.write(f\"行业列表: {[col.replace('Industry_', '') for col in industry_cols]}\\n\\n\")\n", "            \n", "            # 行业权重信息\n", "            if industry_weights is not None:\n", "                f.write(\"行业权重信息:\\n\")\n", "                latest_date = industry_weights['date'].max()\n", "                latest_weights = industry_weights[industry_weights['date'] == latest_date]\n", "                latest_weights = latest_weights.sort_values('weight', ascending=False)\n", "                \n", "                f.write(f\"最新日期: {latest_date}\\n\")\n", "                f.write(\"行业权重排序:\\n\")\n", "                for _, row in latest_weights.iterrows():\n", "                    stock_info = f\" (股票数: {row.get('stock_count', 'N/A')})\" if 'stock_count' in row else \"\"\n", "                    f.write(f\"  {row['l1_name']}: {row['weight']:.2%}{stock_info}\\n\")\n", "        \n", "        print(f\"已保存行业因子摘要: {summary_path}\")\n", "        \n", "        # 5. 保存行业映射表\n", "        mapping_path = os.path.join(output_path, 'industry_mapping.csv')\n", "        industry_mapping = pd.DataFrame({\n", "            'industry_factor': industry_cols,\n", "            'industry_name': [col.replace('Industry_', '') for col in industry_cols]\n", "        })\n", "        industry_mapping.to_csv(mapping_path, index=False)\n", "        print(f\"已保存行业映射表: {mapping_path}\")\n", "        \n", "        return True\n", "        \n", "    except Exception as e:\n", "        print(f\"保存行业因子数据失败: {e}\")\n", "        return False\n", "\n", "# 保存数据\n", "if industry_matrix is not None:\n", "    save_success = save_industry_factors(industry_matrix, industry_weights, OUTPUT_PATH)\n", "    \n", "    if save_success:\n", "        print(\"\\n=== 行业因子构建完成 ===\")\n", "        \n", "        industry_cols = [col for col in industry_matrix.columns if col.startswith('Industry_')]\n", "        print(f\"成功构建{len(industry_cols)}个行业因子\")\n", "        print(f\"数据保存路径: {OUTPUT_PATH}\")\n", "        \n", "        print(\"\\n可用文件:\")\n", "        print(\"- industry_factor_matrix.h5 (行业因子矩阵)\")\n", "        print(\"- industry_weights.h5 (行业权重)\")\n", "        print(\"- industry_factor_matrix.csv (矩阵CSV)\")\n", "        print(\"- industry_weights.csv (权重CSV)\")\n", "        print(\"- industry_mapping.csv (行业映射表)\")\n", "        print(\"- industry_factors_summary.txt (摘要)\")\n", "else:\n", "    print(\"\\n行业因子构建失败\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 下一步计划"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def next_steps_plan():\n", "    \"\"\"下一步计划\"\"\"\n", "    print(\"\\n=== 下一步计划 ===\")\n", "    \n", "    print(\"已完成:\")\n", "    print(\"✓ 01_数据预处理.ipynb - 数据清洗和预处理\")\n", "    print(\"✓ 02_风格因子构建.ipynb - 风格因子构建\")\n", "    print(\"✓ 03_行业因子构建.ipynb - 行业因子构建\")\n", "    \n", "    print(\"\\n待完成:\")\n", "    print(\"□ 04_因子收益率计算.ipynb - 计算因子收益率\")\n", "    print(\"□ 05_协方差矩阵估计.ipynb - 估计因子协方差矩阵\")\n", "    print(\"□ 06_特质风险模型.ipynb - 构建特质风险模型\")\n", "    print(\"□ 07_风险模型验证.ipynb - 模型验证和回测\")\n", "    \n", "    print(\"\\n关键数据需求:\")\n", "    print(\"1. 股票收益率数据 - 用于因子收益率回归\")\n", "    print(\"2. 更多风格因子原始数据 - 构建完整的6个风格因子\")\n", "    print(\"3. 基准指数数据 - 用于模型验证\")\n", "    print(\"4. 交易成本数据 - 用于实际应用\")\n", "    \n", "    print(\"\\n技术要点:\")\n", "    print(\"1. 因子收益率计算需要使用加权最小二乘法\")\n", "    print(\"2. 协方差矩阵估计需要考虑时间衰减\")\n", "    print(\"3. 特质风险模型需要处理异方差性\")\n", "    print(\"4. 模型验证需要多个维度的测试\")\n", "\n", "next_steps_plan()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}