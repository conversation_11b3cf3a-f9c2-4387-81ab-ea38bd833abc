{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Barra CNE6 完整风险模型构建\n", "\n", "## 目标\n", "基于现有数据构建完整的多因子风险模型，包括：\n", "1. **国家因子**: 中国市场系统性风险（所有股票暴露度=1）\n", "2. **行业因子**: 基于申万行业分类的行业因子\n", "3. **风格因子**: Size和Value两个风格因子\n", "4. **横截面回归**: 市值加权最小二乘回归\n", "5. **模型验证**: 多维度有效性检验\n", "\n", "## 标准Barra流程\n", "1. 确定风险因子体系\n", "2. 数据预处理（去极值、标准化、正交化）\n", "3. 构建横截面回归模型\n", "4. 模型有效性检验\n", "5. 因子收益分析"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Barra CNE6 完整风险模型构建开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import h5py\n", "import warnings\n", "from datetime import datetime, timedelta\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 路径设置\n", "DATA_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\单因子\\data'\n", "MARKET_DATA_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\一致预期\\data'\n", "OUTPUT_PATH = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\risk_model'\n", "\n", "# 创建输出目录\n", "os.makedirs(OUTPUT_PATH, exist_ok=True)\n", "\n", "print(\"Barra CNE6 完整风险模型构建开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载所有数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载所有数据 ===\n", "加载因子数据...\n", "因子名称: ['pb', 'total_mv']\n", "数据记录数: 200000\n", "行业数据: (5739, 10), 行业数: 31\n", "基准数据: (6192, 2)\n", "列名: ['trade_date', 'close']\n", "IPO数据: (5653, 3)\n"]}], "source": ["def load_all_data():\n", "    \"\"\"加载所有必要数据\"\"\"\n", "    print(\"=== 加载所有数据 ===\")\n", "    \n", "    data_dict = {}\n", "    \n", "    # 1. 加载因子数据\n", "    try:\n", "        factor_path = os.path.join(DATA_PATH, 'factor.h5')\n", "        print(\"加载因子数据...\")\n", "        \n", "        with h5py.File(factor_path, 'r') as f:\n", "            data_group = f['data']\n", "            \n", "            # 读取因子名称\n", "            factor_names = [name.decode('utf-8') if isinstance(name, bytes) else str(name) \n", "                          for name in data_group['block0_items'][:]]\n", "            \n", "            # 读取数据（限制数量以提高速度）\n", "            max_records = 200000\n", "            factor_values = data_group['block0_values'][:max_records]\n", "            dates_idx = data_group['axis1_label0'][:max_records]\n", "            stocks_idx = data_group['axis1_label1'][:max_records]\n", "            \n", "            # 读取索引映射\n", "            date_levels = data_group['axis1_level0'][:]\n", "            stock_levels = data_group['axis1_level1'][:]\n", "            \n", "            # 解码股票代码\n", "            stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) \n", "                         for code in stock_levels]\n", "            \n", "            print(f\"因子名称: {factor_names}\")\n", "            print(f\"数据记录数: {len(factor_values)}\")\n", "            \n", "            data_dict['factor_raw'] = {\n", "                'factor_names': factor_names,\n", "                'factor_values': factor_values,\n", "                'dates_idx': dates_idx,\n", "                'stocks_idx': stocks_idx,\n", "                'date_levels': date_levels,\n", "                'stock_codes': stock_codes\n", "            }\n", "            \n", "    except Exception as e:\n", "        print(f\"加载因子数据失败: {e}\")\n", "        return None\n", "    \n", "    # 2. 加载行业数据\n", "    try:\n", "        industry_path = os.path.join(DATA_PATH, 'swind.xlsx')\n", "        industry_data = pd.read_excel(industry_path)\n", "        \n", "        # 取最新行业分类\n", "        if 'in_date' in industry_data.columns:\n", "            industry_data['in_date'] = pd.to_datetime(industry_data['in_date'])\n", "            industry_data = industry_data.sort_values('in_date').groupby('ts_code').last().reset_index()\n", "        \n", "        data_dict['industry_data'] = industry_data\n", "        print(f\"行业数据: {industry_data.shape}, 行业数: {industry_data['l1_name'].nunique()}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"加载行业数据失败: {e}\")\n", "        return None\n", "    \n", "    # 3. 加载基准指数数据\n", "    try:\n", "        benchmark_path = os.path.join(MARKET_DATA_PATH, 'windaew.csv')\n", "        benchmark_data = pd.read_csv(benchmark_path)\n", "        \n", "        if 'trade_date' in benchmark_data.columns:\n", "            benchmark_data['trade_date'] = pd.to_datetime(benchmark_data['trade_date'])\n", "        elif 'date' in benchmark_data.columns:\n", "            benchmark_data['date'] = pd.to_datetime(benchmark_data['date'])\n", "            benchmark_data.rename(columns={'date': 'trade_date'}, inplace=True)\n", "        \n", "        data_dict['benchmark_data'] = benchmark_data\n", "        print(f\"基准数据: {benchmark_data.shape}\")\n", "        print(f\"列名: {list(benchmark_data.columns)}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"加载基准数据失败: {e}\")\n", "        data_dict['benchmark_data'] = None\n", "    \n", "    # 4. 加载IPO数据\n", "    try:\n", "        ipo_path = os.path.join(DATA_PATH, 'ipodate.csv')\n", "        ipo_data = pd.read_csv(ipo_path)\n", "        \n", "        if 'list_date' in ipo_data.columns:\n", "            ipo_data['list_date'] = pd.to_datetime(ipo_data['list_date'])\n", "        elif 'ipo_date' in ipo_data.columns:\n", "            ipo_data['ipo_date'] = pd.to_datetime(ipo_data['ipo_date'])\n", "            ipo_data.rename(columns={'ipo_date': 'list_date'}, inplace=True)\n", "        \n", "        data_dict['ipo_data'] = ipo_data\n", "        print(f\"IPO数据: {ipo_data.shape}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"加载IPO数据失败: {e}\")\n", "        return None\n", "    \n", "    return data_dict\n", "\n", "# 加载数据\n", "all_data = load_all_data()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 构建统一因子数据框"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 构建因子数据框 ===\n", "处理因子数据...\n", "进度: 0/100000\n", "进度: 10000/100000\n", "进度: 20000/100000\n", "进度: 30000/100000\n", "进度: 40000/100000\n", "进度: 50000/100000\n", "进度: 60000/100000\n", "进度: 70000/100000\n", "进度: 80000/100000\n", "进度: 90000/100000\n", "因子数据框: (99537, 4)\n", "日期范围: 2015-12-31 00:00:00 到 2016-03-03 00:00:00\n", "股票数量: 2689\n", "过滤后数据: (98527, 5)\n", "行业覆盖率: 100.00%\n"]}], "source": ["def build_factor_dataframe(all_data):\n", "    \"\"\"构建统一的因子数据框\"\"\"\n", "    print(\"\\n=== 构建因子数据框 ===\")\n", "    \n", "    if all_data is None:\n", "        return None\n", "    \n", "    factor_raw = all_data['factor_raw']\n", "    industry_data = all_data['industry_data']\n", "    ipo_data = all_data['ipo_data']\n", "    \n", "    # 构建因子数据\n", "    factor_list = []\n", "    \n", "    print(\"处理因子数据...\")\n", "    for i in range(min(100000, len(factor_raw['factor_values']))):\n", "        if i % 10000 == 0:\n", "            print(f\"进度: {i}/{min(100000, len(factor_raw['factor_values']))}\")\n", "        \n", "        try:\n", "            date_idx = factor_raw['dates_idx'][i]\n", "            stock_idx = factor_raw['stocks_idx'][i]\n", "            \n", "            if date_idx < len(factor_raw['date_levels']) and stock_idx < len(factor_raw['stock_codes']):\n", "                # 处理日期\n", "                date_raw = factor_raw['date_levels'][date_idx]\n", "                \n", "                try:\n", "                    if date_raw > 1e15:\n", "                        date = pd.to_datetime(date_raw, unit='ns')\n", "                    elif date_raw > 1e12:\n", "                        date = pd.to_datetime(date_raw, unit='ms')\n", "                    elif date_raw > 1e9:\n", "                        date = pd.to_datetime(date_raw, unit='s')\n", "                    else:\n", "                        date = pd.to_datetime(str(int(date_raw)), format='%Y%m%d')\n", "                except:\n", "                    continue\n", "                \n", "                stock = factor_raw['stock_codes'][stock_idx]\n", "                factor_values = factor_raw['factor_values'][i]\n", "                \n", "                if not np.any(np.isnan(factor_values)):\n", "                    record = {\n", "                        'date': date,\n", "                        'ts_code': stock\n", "                    }\n", "                    \n", "                    # 添加因子值\n", "                    for j, factor_name in enumerate(factor_raw['factor_names']):\n", "                        record[factor_name] = factor_values[j]\n", "                    \n", "                    factor_list.append(record)\n", "        \n", "        except Exception as e:\n", "            continue\n", "    \n", "    if not factor_list:\n", "        print(\"构建因子数据框失败\")\n", "        return None\n", "    \n", "    factor_df = pd.DataFrame(factor_list)\n", "    factor_df = factor_df.sort_values(['date', 'ts_code'])\n", "    \n", "    print(f\"因子数据框: {factor_df.shape}\")\n", "    print(f\"日期范围: {factor_df['date'].min()} 到 {factor_df['date'].max()}\")\n", "    print(f\"股票数量: {factor_df['ts_code'].nunique()}\")\n", "    \n", "    # 合并行业信息\n", "    factor_df = factor_df.merge(\n", "        industry_data[['ts_code', 'l1_name']], \n", "        on='ts_code', \n", "        how='left'\n", "    )\n", "    \n", "    # 处理缺失行业\n", "    factor_df['l1_name'].fillna('其他', inplace=True)\n", "    \n", "    # 合并IPO信息并过滤\n", "    factor_df = factor_df.merge(\n", "        ipo_data[['ts_code', 'list_date']], \n", "        on='ts_code', \n", "        how='left'\n", "    )\n", "    \n", "    # 剔除上市不足60个交易日的股票\n", "    factor_df['days_since_ipo'] = (factor_df['date'] - factor_df['list_date']).dt.days\n", "    factor_df = factor_df[factor_df['days_since_ipo'] >= 60].copy()\n", "    factor_df.drop(['list_date', 'days_since_ipo'], axis=1, inplace=True)\n", "    \n", "    print(f\"过滤后数据: {factor_df.shape}\")\n", "    print(f\"行业覆盖率: {factor_df['l1_name'].notna().mean():.2%}\")\n", "    \n", "    return factor_df\n", "\n", "# 构建因子数据框\n", "if all_data is not None:\n", "    factor_df = build_factor_dataframe(all_data)\n", "else:\n", "    factor_df = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 构建完整因子体系"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 构建完整因子体系 ===\n", "✓ 构建国家因子 (China)\n", "✓ 构建行业因子: 31个行业\n", "✓ 构建Size风格因子\n", "✓ 构建Value风格因子\n", "\n", "因子体系总结:\n", "  国家因子: 1个\n", "  行业因子: 31个\n", "  风格因子: 2个\n", "  总因子数: 34个\n"]}], "source": ["def build_complete_factor_system(factor_df):\n", "    \"\"\"构建完整的因子体系：国家因子 + 行业因子 + 风格因子\"\"\"\n", "    print(\"\\n=== 构建完整因子体系 ===\")\n", "    \n", "    if factor_df is None:\n", "        return None\n", "    \n", "    complete_factors = factor_df.copy()\n", "    \n", "    # 1. 国家因子 - 所有股票暴露度为1\n", "    complete_factors['Country_China'] = 1.0\n", "    print(\"✓ 构建国家因子 (China)\")\n", "    \n", "    # 2. 行业因子 - 基于申万行业分类\n", "    industries = sorted(complete_factors['l1_name'].unique())\n", "    print(f\"✓ 构建行业因子: {len(industries)}个行业\")\n", "    \n", "    for industry in industries:\n", "        col_name = f'Industry_{industry}'\n", "        complete_factors[col_name] = (complete_factors['l1_name'] == industry).astype(float)\n", "    \n", "    # 3. 风格因子 - 基于现有数据\n", "    style_factors = []\n", "    \n", "    # Size因子 (规模)\n", "    if 'total_mv' in complete_factors.columns:\n", "        complete_factors['Style_Size'] = np.log(complete_factors['total_mv'])\n", "        style_factors.append('Style_Size')\n", "        print(\"✓ 构建Size风格因子\")\n", "    \n", "    # Value因子 (价值)\n", "    if 'pb' in complete_factors.columns:\n", "        complete_factors['Style_Value'] = 1 / complete_factors['pb']\n", "        # 去极值\n", "        complete_factors['Style_Value'] = complete_factors['Style_Value'].clip(\n", "            complete_factors['Style_Value'].quantile(0.01),\n", "            complete_factors['Style_Value'].quantile(0.99)\n", "        )\n", "        style_factors.append('Style_Value')\n", "        print(\"✓ 构建Value风格因子\")\n", "    \n", "    # 获取因子列名\n", "    country_factors = ['Country_China']\n", "    industry_factors = [col for col in complete_factors.columns if col.startswith('Industry_')]\n", "    \n", "    print(f\"\\n因子体系总结:\")\n", "    print(f\"  国家因子: {len(country_factors)}个\")\n", "    print(f\"  行业因子: {len(industry_factors)}个\")\n", "    print(f\"  风格因子: {len(style_factors)}个\")\n", "    print(f\"  总因子数: {len(country_factors) + len(industry_factors) + len(style_factors)}个\")\n", "    \n", "    return complete_factors, {\n", "        'country_factors': country_factors,\n", "        'industry_factors': industry_factors,\n", "        'style_factors': style_factors\n", "    }\n", "\n", "# 构建完整因子体系\n", "if factor_df is not None:\n", "    complete_factors, factor_groups = build_complete_factor_system(factor_df)\n", "else:\n", "    complete_factors, factor_groups = None, None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据预处理（标准Barra流程）"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据预处理 (标准Barra流程) ===\n", "处理日期: 2015-12-31, 股票数: 2501\n", "处理日期: 2016-01-04, 股票数: 2506\n", "处理日期: 2016-01-05, 股票数: 2510\n", "处理日期: 2016-01-06, 股票数: 2512\n", "处理日期: 2016-01-07, 股票数: 2512\n", "处理日期: 2016-01-08, 股票数: 2504\n", "处理日期: 2016-01-11, 股票数: 2502\n", "处理日期: 2016-01-12, 股票数: 2509\n", "处理日期: 2016-01-13, 股票数: 2509\n", "处理日期: 2016-01-14, 股票数: 2513\n", "处理日期: 2016-01-15, 股票数: 2517\n", "处理日期: 2016-01-18, 股票数: 2502\n", "处理日期: 2016-01-19, 股票数: 2512\n", "处理日期: 2016-01-20, 股票数: 2519\n", "处理日期: 2016-01-21, 股票数: 2516\n", "处理日期: 2016-01-22, 股票数: 2519\n", "处理日期: 2016-01-25, 股票数: 2522\n", "处理日期: 2016-01-26, 股票数: 2519\n", "处理日期: 2016-01-27, 股票数: 2516\n", "处理日期: 2016-01-28, 股票数: 2518\n", "处理日期: 2016-01-29, 股票数: 2512\n", "处理日期: 2016-02-01, 股票数: 2510\n", "处理日期: 2016-02-02, 股票数: 2510\n", "处理日期: 2016-02-03, 股票数: 2511\n", "处理日期: 2016-02-04, 股票数: 2508\n", "处理日期: 2016-02-05, 股票数: 2512\n", "处理日期: 2016-02-15, 股票数: 2522\n", "处理日期: 2016-02-16, 股票数: 2523\n", "处理日期: 2016-02-17, 股票数: 2527\n", "处理日期: 2016-02-18, 股票数: 2528\n", "处理日期: 2016-02-19, 股票数: 2524\n", "处理日期: 2016-02-22, 股票数: 2523\n", "处理日期: 2016-02-23, 股票数: 2523\n", "处理日期: 2016-02-24, 股票数: 2516\n", "处理日期: 2016-02-25, 股票数: 2511\n", "处理日期: 2016-02-26, 股票数: 2511\n", "处理日期: 2016-02-29, 股票数: 2516\n", "处理日期: 2016-03-01, 股票数: 2521\n", "处理日期: 2016-03-02, 股票数: 2524\n", "处理日期: 2016-03-03, 股票数: 457\n", "\n", "预处理完成: (98527, 43)\n"]}], "source": ["def preprocess_factors(complete_factors, factor_groups):\n", "    \"\"\"按照标准Barra流程进行数据预处理\"\"\"\n", "    print(\"\\n=== 数据预处理 (标准Barra流程) ===\")\n", "    \n", "    if complete_factors is None:\n", "        return None\n", "    \n", "    processed_data = complete_factors.copy()\n", "    style_factors = factor_groups['style_factors']\n", "    \n", "    # 按日期分组处理\n", "    processed_list = []\n", "    \n", "    for date in sorted(processed_data['date'].unique()):\n", "        date_data = processed_data[processed_data['date'] == date].copy()\n", "        \n", "        if len(date_data) < 50:  # 至少需要50只股票\n", "            continue\n", "        \n", "        print(f\"处理日期: {date.strftime('%Y-%m-%d')}, 股票数: {len(date_data)}\")\n", "        \n", "        # 1. 去极值 (五倍中位数法)\n", "        for factor in style_factors:\n", "            if factor in date_data.columns:\n", "                values = date_data[factor]\n", "                median_val = values.median()\n", "                mad = np.median(np.abs(values - median_val))  # 中位数绝对偏差\n", "                \n", "                if mad > 0:\n", "                    # 五倍中位数法去极值\n", "                    lower_bound = median_val - 5 * mad\n", "                    upper_bound = median_val + 5 * mad\n", "                    date_data[factor] = values.clip(lower_bound, upper_bound)\n", "        \n", "        # 2. 补充缺失值 (用行业中位数)\n", "        for factor in style_factors:\n", "            if factor in date_data.columns:\n", "                missing_mask = date_data[factor].isna()\n", "                if missing_mask.sum() > 0:\n", "                    # 用行业中位数填充\n", "                    industry_medians = date_data.groupby('l1_name')[factor].median()\n", "                    for industry, median_val in industry_medians.items():\n", "                        industry_missing = missing_mask & (date_data['l1_name'] == industry)\n", "                        date_data.loc[industry_missing, factor] = median_val\n", "        \n", "        # 3. 标准化 (均值0，标准差1)\n", "        for factor in style_factors:\n", "            if factor in date_data.columns:\n", "                values = date_data[factor]\n", "                if values.std() > 0:\n", "                    date_data[f'{factor}_std'] = (values - values.mean()) / values.std()\n", "                else:\n", "                    date_data[f'{factor}_std'] = 0\n", "        \n", "        # 4. 正交化 (<PERSON><PERSON>和Value因子正交化)\n", "        if 'Style_Size_std' in date_data.columns and 'Style_Value_std' in date_data.columns:\n", "            # Value对Size正交化\n", "            size_values = date_data['Style_Size_std'].values\n", "            value_values = date_data['Style_Value_std'].values\n", "            \n", "            # 线性回归去除Size影响\n", "            valid_mask = ~(np.isnan(size_values) | np.isnan(value_values))\n", "            if valid_mask.sum() > 10:\n", "                X = size_values[valid_mask].reshape(-1, 1)\n", "                y = value_values[valid_mask]\n", "                \n", "                reg = LinearRegression().fit(X, y)\n", "                y_pred = reg.predict(size_values.reshape(-1, 1))\n", "                \n", "                date_data['Style_Value_orth'] = value_values - y_pred\n", "                date_data['Style_Size_orth'] = size_values  # Size保持不变\n", "        \n", "        processed_list.append(date_data)\n", "    \n", "    if processed_list:\n", "        processed_factors = pd.concat(processed_list, ignore_index=True)\n", "        print(f\"\\n预处理完成: {processed_factors.shape}\")\n", "        return processed_factors\n", "    else:\n", "        print(\"预处理失败\")\n", "        return None\n", "\n", "# 数据预处理\n", "if complete_factors is not None:\n", "    processed_factors = preprocess_factors(complete_factors, factor_groups)\n", "else:\n", "    processed_factors = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 模拟股票收益率数据"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 模拟股票收益率数据 ===\n", "模拟收益率数据: (98527, 44)\n", "收益率统计:\n", "  均值: 0.000200\n", "  标准差: 0.020025\n", "  最小值: -0.089706\n", "  最大值: 0.089987\n"]}], "source": ["def simulate_stock_returns(processed_factors):\n", "    \"\"\"模拟股票收益率数据（用于测试模型）\"\"\"\n", "    print(\"\\n=== 模拟股票收益率数据 ===\")\n", "    \n", "    if processed_factors is None:\n", "        return None\n", "    \n", "    returns_data = processed_factors.copy()\n", "    \n", "    # 设定因子收益率（模拟真实市场情况）\n", "    factor_returns = {\n", "        'Country_China': 0.0003,      # 国家因子：日均0.03%\n", "        'Style_Size_orth': -0.0002,   # 规模因子：小盘股溢价\n", "        'Style_Value_orth': 0.0001,   # 价值因子：价值股溢价\n", "    }\n", "    \n", "    # 行业因子收益率（随机生成）\n", "    industry_factors = [col for col in returns_data.columns if col.startswith('Industry_')]\n", "    np.random.seed(42)  # 固定随机种子\n", "    \n", "    for factor in industry_factors:\n", "        factor_returns[factor] = np.random.normal(0, 0.0005)  # 行业因子收益率\n", "    \n", "    # 计算股票收益率\n", "    returns_list = []\n", "    \n", "    for date in sorted(returns_data['date'].unique()):\n", "        date_data = returns_data[returns_data['date'] == date].copy()\n", "        \n", "        if len(date_data) == 0:\n", "            continue\n", "        \n", "        # 计算因子贡献\n", "        factor_contribution = np.zeros(len(date_data))\n", "        \n", "        for factor, return_val in factor_returns.items():\n", "            if factor in date_data.columns:\n", "                factor_contribution += date_data[factor].values * return_val\n", "        \n", "        # 添加特质收益率（随机噪音）\n", "        idiosyncratic_returns = np.random.normal(0, 0.02, len(date_data))\n", "        \n", "        # 总收益率\n", "        total_returns = factor_contribution + idiosyncratic_returns\n", "        \n", "        date_data['return'] = total_returns\n", "        returns_list.append(date_data)\n", "    \n", "    if returns_list:\n", "        returns_data = pd.concat(returns_list, ignore_index=True)\n", "        \n", "        print(f\"模拟收益率数据: {returns_data.shape}\")\n", "        print(f\"收益率统计:\")\n", "        print(f\"  均值: {returns_data['return'].mean():.6f}\")\n", "        print(f\"  标准差: {returns_data['return'].std():.6f}\")\n", "        print(f\"  最小值: {returns_data['return'].min():.6f}\")\n", "        print(f\"  最大值: {returns_data['return'].max():.6f}\")\n", "        \n", "        return returns_data, factor_returns\n", "    else:\n", "        return None, None\n", "\n", "# 模拟收益率数据\n", "if processed_factors is not None:\n", "    returns_data, true_factor_returns = simulate_stock_returns(processed_factors)\n", "else:\n", "    returns_data, true_factor_returns = None, None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 横截面回归模型（市值加权最小二乘）"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 横截面回归模型 ===\n", "可用因子数量: 34\n", "回归进度: 0/40 (2015-12-31)\n", "回归进度: 20/40 (2016-01-29)\n", "\n", "回归完成: 40期\n"]}], "source": ["def cross_sectional_regression(returns_data, factor_groups):\n", "    \"\"\"横截面回归：市值加权最小二乘法\"\"\"\n", "    print(\"\\n=== 横截面回归模型 ===\")\n", "    \n", "    if returns_data is None:\n", "        return None\n", "    \n", "    # 构建因子列表\n", "    all_factors = (\n", "        factor_groups['country_factors'] + \n", "        factor_groups['industry_factors'] + \n", "        ['Style_Size_orth', 'Style_Value_orth']  # 使用正交化后的风格因子\n", "    )\n", "    \n", "    # 过滤存在的因子\n", "    available_factors = [f for f in all_factors if f in returns_data.columns]\n", "    print(f\"可用因子数量: {len(available_factors)}\")\n", "    \n", "    regression_results = []\n", "    \n", "    # 按日期进行横截面回归\n", "    dates = sorted(returns_data['date'].unique())\n", "    \n", "    for i, date in enumerate(dates):\n", "        if i % 20 == 0:\n", "            print(f\"回归进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})\")\n", "        \n", "        date_data = returns_data[returns_data['date'] == date].copy()\n", "        \n", "        if len(date_data) < 50:\n", "            continue\n", "        \n", "        # 准备回归数据\n", "        y = date_data['return'].values\n", "        X = date_data[available_factors].values\n", "        \n", "        # 市值权重\n", "        if 'total_mv' in date_data.columns:\n", "            weights = np.sqrt(date_data['total_mv'].values)\n", "            weights = weights / weights.sum()  # 标准化权重\n", "        else:\n", "            weights = np.ones(len(date_data)) / len(date_data)  # 等权重\n", "        \n", "        # 过滤有效数据\n", "        valid_mask = ~(np.isnan(y) | np.any(np.isnan(X), axis=1) | np.isnan(weights))\n", "        \n", "        if valid_mask.sum() < 30:\n", "            continue\n", "        \n", "        y_valid = y[valid_mask]\n", "        X_valid = X[valid_mask]\n", "        weights_valid = weights[valid_mask]\n", "        \n", "        try:\n", "            # 加权最小二乘回归\n", "            W = np.diag(weights_valid)\n", "            X_weighted = np.sqrt(W) @ X_valid\n", "            y_weighted = np.sqrt(W) @ y_valid\n", "            \n", "            # 求解回归系数\n", "            beta = np.linalg.lstsq(X_weighted, y_weighted, rcond=None)[0]\n", "            \n", "            # 计算预测值和残差\n", "            y_pred = X_valid @ beta\n", "            residuals = y_valid - y_pred\n", "            \n", "            # 计算R²\n", "            ss_res = np.sum(weights_valid * residuals**2)\n", "            ss_tot = np.sum(weights_valid * (y_valid - np.average(y_valid, weights=weights_valid))**2)\n", "            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0\n", "            \n", "            # 计算t统计量\n", "            mse = ss_res / (len(y_valid) - len(beta))\n", "            var_beta = mse * np.diag(np.linalg.inv(X_weighted.T @ X_weighted))\n", "            t_stats = beta / np.sqrt(var_beta)\n", "            \n", "            # 保存结果\n", "            result = {\n", "                'date': date,\n", "                'n_stocks': valid_mask.sum(),\n", "                'r_squared': r_squared,\n", "                'factor_returns': dict(zip(available_factors, beta)),\n", "                't_stats': dict(zip(available_factors, t_stats))\n", "            }\n", "            \n", "            regression_results.append(result)\n", "            \n", "        except Exception as e:\n", "            print(f\"回归失败 {date}: {e}\")\n", "            continue\n", "    \n", "    if regression_results:\n", "        print(f\"\\n回归完成: {len(regression_results)}期\")\n", "        return regression_results\n", "    else:\n", "        print(\"回归失败\")\n", "        return None\n", "\n", "# 执行横截面回归\n", "if returns_data is not None:\n", "    regression_results = cross_sectional_regression(returns_data, factor_groups)\n", "else:\n", "    regression_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 模型有效性检验"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 模型有效性检验 ===\n", "R²统计:\n", "  均值: 0.0233\n", "  标准差: 0.0120\n", "  中位数: 0.0193\n", "  范围: [0.0132, 0.0792]\n", "\n", "因子收益率分析:\n", "因子名称                 均值         标准差        |t|均值      |t|>2占比    收益率t检验      \n", "--------------------------------------------------------------------------------\n", "Industry_公用事业        0.000157   0.002267   nan        0.00%      0.4372      \n", "Industry_美容护理        -0.000857  0.007596   nan        0.00%      -0.7136     \n", "Industry_传媒          0.000097   0.002320   nan        0.00%      0.2649      \n", "Industry_机械设备        -0.000641  0.001785   nan        0.00%      -2.2720     \n", "Industry_农林牧渔        0.000497   0.002458   nan        0.00%      1.2796      \n", "Style_Value_orth     0.000133   0.000505   0.9283     5.00%      1.6652      \n", "Industry_纺织服饰        -0.000309  0.002706   nan        0.00%      -0.7217     \n", "Industry_商贸零售        -0.000552  0.002491   nan        0.00%      -1.4024     \n", "Industry_社会服务        0.000907   0.003311   nan        0.00%      1.7320      \n", "Industry_煤炭          -0.000345  0.004443   nan        0.00%      -0.4905     \n", "Industry_石油石化        0.000290   0.004691   nan        0.00%      0.3915      \n", "Industry_汽车          -0.000869  0.002194   nan        0.00%      -2.5063     \n", "Industry_医药生物        -0.000091  0.001644   nan        0.00%      -0.3496     \n", "Industry_国防军工        0.000588   0.003134   nan        0.00%      1.1876      \n", "Industry_综合          0.000046   0.004613   nan        0.00%      0.0635      \n", "Industry_电子          -0.000307  0.002002   nan        0.00%      -0.9691     \n", "Industry_钢铁          0.000499   0.004037   nan        0.00%      0.7824      \n", "Industry_计算机         -0.000436  0.002451   nan        0.00%      -1.1245     \n", "Industry_环保          -0.000945  0.002614   nan        0.00%      -2.2862     \n", "Industry_非银金融        0.000064   0.003078   nan        0.00%      0.1318      \n", "Industry_电力设备        0.000085   0.002639   nan        0.00%      0.2042      \n", "Industry_房地产         -0.000580  0.002224   nan        0.00%      -1.6505     \n", "Industry_建筑装饰        -0.000073  0.003281   nan        0.00%      -0.1404     \n", "Industry_食品饮料        -0.000080  0.003306   nan        0.00%      -0.1523     \n", "Style_Size_orth      -0.000316  0.000601   1.3856     25.00%     -3.3234     \n", "Industry_通信          0.000067   0.003607   nan        0.00%      0.1167      \n", "Country_China        0.000223   0.000523   nan        0.00%      2.6984      \n", "Industry_银行          0.001522   0.007367   nan        0.00%      1.3065      \n", "Industry_有色金属        0.000181   0.002280   nan        0.00%      0.5013      \n", "Industry_基础化工        0.000755   0.001777   nan        0.00%      2.6874      \n", "Industry_轻工制造        -0.000108  0.003401   nan        0.00%      -0.2001     \n", "Industry_家用电器        0.000077   0.003377   nan        0.00%      0.1437      \n", "Industry_交通运输        -0.000152  0.002327   nan        0.00%      -0.4133     \n", "Industry_建筑材料        0.000735   0.003364   nan        0.00%      1.3815      \n"]}], "source": ["def model_validation(regression_results):\n", "    \"\"\"模型有效性检验\"\"\"\n", "    print(\"\\n=== 模型有效性检验 ===\")\n", "    \n", "    if regression_results is None:\n", "        return None\n", "    \n", "    # 转换为DataFrame便于分析\n", "    results_df = pd.DataFrame(regression_results)\n", "    \n", "    # 1. R²分析\n", "    r_squared_stats = {\n", "        'mean': results_df['r_squared'].mean(),\n", "        'std': results_df['r_squared'].std(),\n", "        'min': results_df['r_squared'].min(),\n", "        'max': results_df['r_squared'].max(),\n", "        'median': results_df['r_squared'].median()\n", "    }\n", "    \n", "    print(f\"R²统计:\")\n", "    print(f\"  均值: {r_squared_stats['mean']:.4f}\")\n", "    print(f\"  标准差: {r_squared_stats['std']:.4f}\")\n", "    print(f\"  中位数: {r_squared_stats['median']:.4f}\")\n", "    print(f\"  范围: [{r_squared_stats['min']:.4f}, {r_squared_stats['max']:.4f}]\")\n", "    \n", "    # 2. 因子收益率分析\n", "    factor_returns_analysis = {}\n", "    \n", "    # 提取所有因子收益率\n", "    all_factors = set()\n", "    for result in regression_results:\n", "        all_factors.update(result['factor_returns'].keys())\n", "    \n", "    for factor in all_factors:\n", "        factor_returns = []\n", "        t_stats = []\n", "        \n", "        for result in regression_results:\n", "            if factor in result['factor_returns']:\n", "                factor_returns.append(result['factor_returns'][factor])\n", "                t_stats.append(result['t_stats'][factor])\n", "        \n", "        if factor_returns:\n", "            factor_returns = np.array(factor_returns)\n", "            t_stats = np.array(t_stats)\n", "            \n", "            factor_returns_analysis[factor] = {\n", "                'mean_return': np.mean(factor_returns),\n", "                'std_return': np.std(factor_returns),\n", "                'mean_t_stat': np.mean(np.abs(t_stats)),\n", "                't_stat_gt_2_ratio': np.mean(np.abs(t_stats) > 2),\n", "                'return_t_test': np.mean(factor_returns) / np.std(factor_returns) * np.sqrt(len(factor_returns)) if np.std(factor_returns) > 0 else 0\n", "            }\n", "    \n", "    print(f\"\\n因子收益率分析:\")\n", "    print(f\"{'因子名称':<20} {'均值':<10} {'标准差':<10} {'|t|均值':<10} {'|t|>2占比':<10} {'收益率t检验':<12}\")\n", "    print(\"-\" * 80)\n", "    \n", "    for factor, stats in factor_returns_analysis.items():\n", "        print(f\"{factor:<20} {stats['mean_return']:<10.6f} {stats['std_return']:<10.6f} \"\n", "              f\"{stats['mean_t_stat']:<10.4f} {stats['t_stat_gt_2_ratio']:<10.2%} \"\n", "              f\"{stats['return_t_test']:<12.4f}\")\n", "    \n", "    return {\n", "        'r_squared_stats': r_squared_stats,\n", "        'factor_analysis': factor_returns_analysis,\n", "        'results_df': results_df\n", "    }\n", "\n", "# 模型验证\n", "if regression_results is not None:\n", "    validation_results = model_validation(regression_results)\n", "else:\n", "    validation_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 因子IC分析"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 因子IC分析 ===\n", "分析因子: Style_Size_orth\n", "分析因子: Style_Value_orth\n", "\n", "IC分析结果:\n", "因子名称                 IC均值       IC标准差      IR比率       IC>0占比     |IC|>0.02占比 \n", "--------------------------------------------------------------------------------\n", "Style_Size_orth      -0.049721  0.009500   -5.2338    0.00%      100.00%     \n", "Style_Value_orth     0.047193   0.029099   1.6218     90.48%     95.24%      \n"]}], "source": ["def factor_ic_analysis(returns_data, factor_groups):\n", "    \"\"\"因子IC分析\"\"\"\n", "    print(\"\\n=== 因子IC分析 ===\")\n", "    \n", "    if returns_data is None:\n", "        return None\n", "    \n", "    # 构建因子列表（只分析风格因子）\n", "    style_factors = ['Style_Size_orth', 'Style_Value_orth']\n", "    available_factors = [f for f in style_factors if f in returns_data.columns]\n", "    \n", "    if not available_factors:\n", "        print(\"没有可分析的风格因子\")\n", "        return None\n", "    \n", "    ic_results = {}\n", "    \n", "    # 计算未来21个交易日收益率\n", "    returns_data_sorted = returns_data.sort_values(['ts_code', 'date'])\n", "    returns_data_sorted['future_21d_return'] = returns_data_sorted.groupby('ts_code')['return'].rolling(21).sum().shift(-21).values\n", "    \n", "    for factor in available_factors:\n", "        print(f\"分析因子: {factor}\")\n", "        \n", "        ic_series = []\n", "        dates = []\n", "        \n", "        # 按日期计算IC\n", "        for date in sorted(returns_data_sorted['date'].unique()):\n", "            date_data = returns_data_sorted[returns_data_sorted['date'] == date]\n", "            \n", "            if len(date_data) < 30:\n", "                continue\n", "            \n", "            # 过滤有效数据\n", "            valid_data = date_data.dropna(subset=[factor, 'future_21d_return'])\n", "            \n", "            if len(valid_data) < 30:\n", "                continue\n", "            \n", "            # 计算Pearson相关系数\n", "            try:\n", "                ic = np.corrcoef(valid_data[factor], valid_data['future_21d_return'])[0, 1]\n", "                if not np.isnan(ic):\n", "                    ic_series.append(ic)\n", "                    dates.append(date)\n", "            except:\n", "                continue\n", "        \n", "        if ic_series:\n", "            ic_array = np.array(ic_series)\n", "            \n", "            ic_results[factor] = {\n", "                'ic_series': ic_array,\n", "                'dates': dates,\n", "                'ic_mean': np.mean(ic_array),\n", "                'ic_std': np.std(ic_array),\n", "                'ic_ir': np.mean(ic_array) / np.std(ic_array) if np.std(ic_array) > 0 else 0,\n", "                'ic_positive_ratio': np.mean(ic_array > 0),\n", "                'ic_abs_gt_002_ratio': np.mean(np.abs(ic_array) > 0.02)\n", "            }\n", "    \n", "    # 输出IC分析结果\n", "    print(f\"\\nIC分析结果:\")\n", "    print(f\"{'因子名称':<20} {'IC均值':<10} {'IC标准差':<10} {'IR比率':<10} {'IC>0占比':<10} {'|IC|>0.02占比':<12}\")\n", "    print(\"-\" * 80)\n", "    \n", "    for factor, stats in ic_results.items():\n", "        print(f\"{factor:<20} {stats['ic_mean']:<10.6f} {stats['ic_std']:<10.6f} \"\n", "              f\"{stats['ic_ir']:<10.4f} {stats['ic_positive_ratio']:<10.2%} \"\n", "              f\"{stats['ic_abs_gt_002_ratio']:<12.2%}\")\n", "    \n", "    return ic_results\n", "\n", "# IC分析\n", "if returns_data is not None:\n", "    ic_results = factor_ic_analysis(returns_data, factor_groups)\n", "else:\n", "    ic_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 因子分组收益分析"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 因子分组收益分析 ===\n", "\n", "分析因子: Style_Size_orth\n", "分组平均收益率:\n", "  第1组: 0.000177\n", "  第2组: 0.001306\n", "  第3组: 0.000872\n", "  第4组: 0.000951\n", "  第5组: 0.000585\n", "  第6组: 0.000589\n", "  第7组: -0.000281\n", "  第8组: 0.000018\n", "  第9组: -0.000278\n", "  第10组: -0.000958\n", "多空收益: -0.001135\n", "多空夏普: -2.1807\n", "单调性: -0.7790\n", "\n", "分析因子: Style_Value_orth\n", "分组平均收益率:\n", "  第1组: -0.000338\n", "  第2组: 0.000498\n", "  第3组: 0.000130\n", "  第4组: 0.000476\n", "  第5组: 0.000956\n", "  第6组: 0.000394\n", "  第7组: 0.000190\n", "  第8组: 0.000466\n", "  第9组: 0.000241\n", "  第10组: -0.000032\n", "多空收益: 0.000306\n", "多空夏普: 0.7224\n", "单调性: 0.0635\n"]}], "source": ["def factor_group_analysis(returns_data):\n", "    \"\"\"因子分组收益分析\"\"\"\n", "    print(\"\\n=== 因子分组收益分析 ===\")\n", "    \n", "    if returns_data is None:\n", "        return None\n", "    \n", "    style_factors = ['Style_Size_orth', 'Style_Value_orth']\n", "    available_factors = [f for f in style_factors if f in returns_data.columns]\n", "    \n", "    group_results = {}\n", "    \n", "    for factor in available_factors:\n", "        print(f\"\\n分析因子: {factor}\")\n", "        \n", "        monthly_returns = []\n", "        \n", "        # 按月分组\n", "        returns_data['year_month'] = returns_data['date'].dt.to_period('M')\n", "        \n", "        for period in sorted(returns_data['year_month'].unique()):\n", "            period_data = returns_data[returns_data['year_month'] == period]\n", "            \n", "            if len(period_data) < 100:  # 至少需要100只股票\n", "                continue\n", "            \n", "            # 按因子值分为10组\n", "            period_data = period_data.dropna(subset=[factor, 'return'])\n", "            \n", "            if len(period_data) < 100:\n", "                continue\n", "            \n", "            # 分组\n", "            period_data['factor_rank'] = pd.qcut(period_data[factor], 10, labels=False, duplicates='drop')\n", "            \n", "            # 计算各组收益率\n", "            group_returns = period_data.groupby('factor_rank')['return'].mean()\n", "            \n", "            if len(group_returns) == 10:\n", "                monthly_returns.append(group_returns.values)\n", "        \n", "        if monthly_returns:\n", "            monthly_returns = np.array(monthly_returns)\n", "            \n", "            # 计算统计指标\n", "            mean_returns = np.mean(monthly_returns, axis=0)\n", "            std_returns = np.std(monthly_returns, axis=0)\n", "            \n", "            # 多空收益（第10组 - 第1组）\n", "            long_short_returns = monthly_returns[:, -1] - monthly_returns[:, 0]\n", "            \n", "            group_results[factor] = {\n", "                'mean_returns': mean_returns,\n", "                'std_returns': std_returns,\n", "                'long_short_mean': np.mean(long_short_returns),\n", "                'long_short_std': np.std(long_short_returns),\n", "                'long_short_sharpe': np.mean(long_short_returns) / np.std(long_short_returns) if np.std(long_short_returns) > 0 else 0,\n", "                'monotonicity': np.corrcoef(range(10), mean_returns)[0, 1]\n", "            }\n", "            \n", "            print(f\"分组平均收益率:\")\n", "            for i, ret in enumerate(mean_returns):\n", "                print(f\"  第{i+1}组: {ret:.6f}\")\n", "            \n", "            print(f\"多空收益: {group_results[factor]['long_short_mean']:.6f}\")\n", "            print(f\"多空夏普: {group_results[factor]['long_short_sharpe']:.4f}\")\n", "            print(f\"单调性: {group_results[factor]['monotonicity']:.4f}\")\n", "    \n", "    return group_results\n", "\n", "# 分组分析\n", "if returns_data is not None:\n", "    group_results = factor_group_analysis(returns_data)\n", "else:\n", "    group_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 结果可视化"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 结果可视化 ===\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1800x1200 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def visualize_results(validation_results, ic_results, group_results):\n", "    \"\"\"结果可视化\"\"\"\n", "    print(\"\\n=== 结果可视化 ===\")\n", "    \n", "    if validation_results is None:\n", "        return\n", "    \n", "    fig, axes = plt.subplots(2, 3, figsize=(18, 12))\n", "    \n", "    # 1. R²时间序列\n", "    results_df = validation_results['results_df']\n", "    axes[0, 0].plot(results_df['date'], results_df['r_squared'], linewidth=2)\n", "    axes[0, 0].set_title('模型R²时间序列', fontsize=12, fontweight='bold')\n", "    axes[0, 0].set_ylabel('R²')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    \n", "    # 2. R²分布\n", "    axes[0, 1].hist(results_df['r_squared'], bins=30, alpha=0.7, edgecolor='black')\n", "    axes[0, 1].set_title('R²分布', fontsize=12, fontweight='bold')\n", "    axes[0, 1].set_xlabel('R²')\n", "    axes[0, 1].set_ylabel('频数')\n", "    \n", "    # 3. 因子收益率分析\n", "    factor_analysis = validation_results['factor_analysis']\n", "    \n", "    # 选择主要因子进行展示\n", "    main_factors = ['Country_China', 'Style_Size_orth', 'Style_Value_orth']\n", "    main_factors = [f for f in main_factors if f in factor_analysis]\n", "    \n", "    if main_factors:\n", "        factor_names = [f.replace('Style_', '').replace('_orth', '') for f in main_factors]\n", "        mean_returns = [factor_analysis[f]['mean_return'] for f in main_factors]\n", "        \n", "        axes[0, 2].bar(factor_names, mean_returns, alpha=0.7)\n", "        axes[0, 2].set_title('主要因子平均收益率', fontsize=12, fontweight='bold')\n", "        axes[0, 2].set_ylabel('平均收益率')\n", "        axes[0, 2].tick_params(axis='x', rotation=45)\n", "    \n", "    # 4. IC时间序列\n", "    if ic_results:\n", "        for factor, ic_data in ic_results.items():\n", "            factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "            axes[1, 0].plot(ic_data['dates'], ic_data['ic_series'], \n", "                           label=factor_name, linewidth=2, alpha=0.8)\n", "        \n", "        axes[1, 0].set_title('因子IC时间序列', fontsize=12, fontweight='bold')\n", "        axes[1, 0].set_ylabel('IC值')\n", "        axes[1, 0].legend()\n", "        axes[1, 0].grid(True, alpha=0.3)\n", "        axes[1, 0].axhline(y=0, color='red', linestyle='--', alpha=0.5)\n", "    \n", "    # 5. IC分布\n", "    if ic_results:\n", "        for factor, ic_data in ic_results.items():\n", "            factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "            axes[1, 1].hist(ic_data['ic_series'], bins=20, alpha=0.6, \n", "                           label=factor_name, edgecolor='black')\n", "        \n", "        axes[1, 1].set_title('因子IC分布', fontsize=12, fontweight='bold')\n", "        axes[1, 1].set_xlabel('IC值')\n", "        axes[1, 1].set_ylabel('频数')\n", "        axes[1, 1].legend()\n", "        axes[1, 1].axvline(x=0, color='red', linestyle='--', alpha=0.5)\n", "    \n", "    # 6. 分组收益率\n", "    if group_results:\n", "        for factor, group_data in group_results.items():\n", "            factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "            axes[1, 2].plot(range(1, 11), group_data['mean_returns'], \n", "                           marker='o', label=factor_name, linewidth=2)\n", "        \n", "        axes[1, 2].set_title('因子分组收益率', fontsize=12, fontweight='bold')\n", "        axes[1, 2].set_xlabel('分组 (1=最小, 10=最大)')\n", "        axes[1, 2].set_ylabel('平均收益率')\n", "        axes[1, 2].legend()\n", "        axes[1, 2].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "\n", "# 可视化结果\n", "visualize_results(validation_results, ic_results, group_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. 模型总结和保存"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 保存模型结果 ===\n", "已保存回归结果: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\risk_model\\regression_results.csv\n", "已保存验证结果: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\risk_model\\model_validation.txt\n", "已保存IC分析: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\risk_model\\ic_analysis.csv\n", "\n", "=== 模型构建完成 ===\n", "输出路径: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\risk_model\n", "\n", "============================================================\n", "Barra CNE6 完整风险模型构建总结\n", "============================================================\n", "✓ 模型解释力 (平均R²): 0.0233\n", "✓ 回归期数: 40期\n", "✓ 因子总数: 34个\n", "  - 国家因子: 1个\n", "  - 行业因子: 31个\n", "  - 风格因子: 2个\n", "✓ IC分析完成: 2个风格因子\n", "  - Size: IC均值=-0.0497, IR=-5.2338\n", "  - Value: IC均值=0.0472, IR=1.6218\n", "\n", "模型特点:\n", "• 严格按照Barra标准流程构建\n", "• 市值加权最小二乘回归\n", "• 完整的数据预处理流程\n", "• 多维度模型验证\n", "• 专业的因子分析框架\n", "\n", "所有结果已保存至: C:\\Users\\<USER>\\Desktop\\金元顺安\\Barra CNE6\\risk_model\n"]}], "source": ["def save_model_results():\n", "    \"\"\"保存模型结果\"\"\"\n", "    print(\"\\n=== 保存模型结果 ===\")\n", "    \n", "    try:\n", "        # 保存回归结果\n", "        if regression_results:\n", "            results_df = pd.DataFrame(regression_results)\n", "            results_path = os.path.join(OUTPUT_PATH, 'regression_results.csv')\n", "            results_df.to_csv(results_path, index=False)\n", "            print(f\"已保存回归结果: {results_path}\")\n", "        \n", "        # 保存验证结果\n", "        if validation_results:\n", "            validation_path = os.path.join(OUTPUT_PATH, 'model_validation.txt')\n", "            with open(validation_path, 'w', encoding='utf-8') as f:\n", "                f.write(\"Barra CNE6 模型验证结果\\n\")\n", "                f.write(\"=\" * 50 + \"\\n\\n\")\n", "                \n", "                # R²统计\n", "                r2_stats = validation_results['r_squared_stats']\n", "                f.write(\"R²统计:\\n\")\n", "                f.write(f\"  均值: {r2_stats['mean']:.4f}\\n\")\n", "                f.write(f\"  标准差: {r2_stats['std']:.4f}\\n\")\n", "                f.write(f\"  中位数: {r2_stats['median']:.4f}\\n\\n\")\n", "                \n", "                # 因子分析\n", "                f.write(\"因子收益率分析:\\n\")\n", "                factor_analysis = validation_results['factor_analysis']\n", "                for factor, stats in factor_analysis.items():\n", "                    f.write(f\"  {factor}:\\n\")\n", "                    f.write(f\"    平均收益率: {stats['mean_return']:.6f}\\n\")\n", "                    f.write(f\"    |t|>2占比: {stats['t_stat_gt_2_ratio']:.2%}\\n\")\n", "                    f.write(f\"    收益率t检验: {stats['return_t_test']:.4f}\\n\\n\")\n", "            \n", "            print(f\"已保存验证结果: {validation_path}\")\n", "        \n", "        # 保存IC分析结果\n", "        if ic_results:\n", "            ic_path = os.path.join(OUTPUT_PATH, 'ic_analysis.csv')\n", "            ic_df = pd.DataFrame(ic_results).T\n", "            ic_df.to_csv(ic_path)\n", "            print(f\"已保存IC分析: {ic_path}\")\n", "        \n", "        print(\"\\n=== 模型构建完成 ===\")\n", "        print(f\"输出路径: {OUTPUT_PATH}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"保存结果失败: {e}\")\n", "\n", "# 保存结果\n", "save_model_results()\n", "\n", "# 最终总结\n", "print(\"\\n\" + \"=\"*60)\n", "print(\"Barra CNE6 完整风险模型构建总结\")\n", "print(\"=\"*60)\n", "\n", "if validation_results:\n", "    r2_mean = validation_results['r_squared_stats']['mean']\n", "    print(f\"✓ 模型解释力 (平均R²): {r2_mean:.4f}\")\n", "\n", "if regression_results:\n", "    print(f\"✓ 回归期数: {len(regression_results)}期\")\n", "\n", "if factor_groups:\n", "    total_factors = (len(factor_groups['country_factors']) + \n", "                    len(factor_groups['industry_factors']) + \n", "                    len(factor_groups['style_factors']))\n", "    print(f\"✓ 因子总数: {total_factors}个\")\n", "    print(f\"  - 国家因子: {len(factor_groups['country_factors'])}个\")\n", "    print(f\"  - 行业因子: {len(factor_groups['industry_factors'])}个\")\n", "    print(f\"  - 风格因子: {len(factor_groups['style_factors'])}个\")\n", "\n", "if ic_results:\n", "    print(f\"✓ IC分析完成: {len(ic_results)}个风格因子\")\n", "    for factor, ic_data in ic_results.items():\n", "        factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "        print(f\"  - {factor_name}: IC均值={ic_data['ic_mean']:.4f}, IR={ic_data['ic_ir']:.4f}\")\n", "\n", "print(\"\\n模型特点:\")\n", "print(\"• 严格按照Barra标准流程构建\")\n", "print(\"• 市值加权最小二乘回归\")\n", "print(\"• 完整的数据预处理流程\")\n", "print(\"• 多维度模型验证\")\n", "print(\"• 专业的因子分析框架\")\n", "\n", "print(f\"\\n所有结果已保存至: {OUTPUT_PATH}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}