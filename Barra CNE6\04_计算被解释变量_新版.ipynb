{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 04 计算被解释变量（新版）\n", "\n", "本notebook完成以下任务：\n", "1. 加载第3步处理后的数据\n", "2. 计算超额收益率\n", "3. 处理极端值\n", "4. 计算权重\n", "5. 数据质量检查\n", "6. 保存被解释变量数据"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["计算被解释变量模块开始...\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"计算被解释变量模块开始...\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.1 加载数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载第3步处理后的数据...\n", "数据形状: (7728286, 48)\n", "数据列数: 48\n", "✓ 所有必要字段都存在\n", "\n", "数据基本信息:\n", "股票数量: 3213\n", "交易日数量: 1704\n", "总记录数: 7728286\n"]}], "source": ["# 加载第3步处理后的数据\n", "print(\"加载第3步处理后的数据...\")\n", "merged_data = pd.read_hdf('data/processed_data_step3.h5', key='data')\n", "print(f\"数据形状: {merged_data.shape}\")\n", "print(f\"数据列数: {len(merged_data.columns)}\")\n", "\n", "# 检查关键字段\n", "required_cols = ['stock_code', 'trade_date', 'stock_return', 'hs300_return', \n", "                 'total_mv', 'excess_return', 'weight']\n", "missing_cols = [col for col in required_cols if col not in merged_data.columns]\n", "if missing_cols:\n", "    print(f\"❌ 缺少必要字段: {missing_cols}\")\n", "else:\n", "    print(\"✓ 所有必要字段都存在\")\n", "\n", "print(f\"\\n数据基本信息:\")\n", "print(f\"股票数量: {merged_data['stock_code'].nunique()}\")\n", "print(f\"交易日数量: {merged_data['trade_date'].nunique()}\")\n", "print(f\"总记录数: {len(merged_data)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.2 验证超额收益率计算"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "验证超额收益率计算...\n", "超额收益率已存在，验证计算正确性...\n", "❌ 超额收益率计算有误，最大差异: 0.08067645288466513\n", "已重新计算超额收益率\n", "\n", "超额收益率统计:\n", "count    7.728286e+06\n", "mean     1.203151e-04\n", "std      2.573492e-02\n", "min     -2.307937e-01\n", "25%     -1.235883e-02\n", "50%     -1.437806e-03\n", "75%      1.030665e-02\n", "max      2.806765e-01\n", "Name: excess_return, dtype: float64\n", "\n", "超额收益率质量检查:\n", "缺失值数量: 0\n", "无穷值数量: 0\n", "极端值数量 (|r| > 0.2): 1799\n"]}], "source": ["# 验证超额收益率计算\n", "print(\"\\n验证超额收益率计算...\")\n", "\n", "# 检查是否已经计算了超额收益率\n", "if 'excess_return' in merged_data.columns:\n", "    print(\"超额收益率已存在，验证计算正确性...\")\n", "    \n", "    # 重新计算验证\n", "    excess_return_check = merged_data['stock_return'] - merged_data['hs300_return']\n", "    diff = abs(merged_data['excess_return'] - excess_return_check)\n", "    max_diff = diff.max()\n", "    \n", "    if max_diff < 1e-10:\n", "        print(\"✓ 超额收益率计算正确\")\n", "    else:\n", "        print(f\"❌ 超额收益率计算有误，最大差异: {max_diff}\")\n", "        # 重新计算\n", "        merged_data['excess_return'] = excess_return_check\n", "        print(\"已重新计算超额收益率\")\n", "else:\n", "    print(\"计算超额收益率...\")\n", "    merged_data['excess_return'] = merged_data['stock_return'] - merged_data['hs300_return']\n", "\n", "# 超额收益率统计\n", "print(f\"\\n超额收益率统计:\")\n", "excess_stats = merged_data['excess_return'].describe()\n", "print(excess_stats)\n", "\n", "print(f\"\\n超额收益率质量检查:\")\n", "print(f\"缺失值数量: {merged_data['excess_return'].isnull().sum()}\")\n", "print(f\"无穷值数量: {np.isinf(merged_data['excess_return']).sum()}\")\n", "print(f\"极端值数量 (|r| > 0.2): {(abs(merged_data['excess_return']) > 0.2).sum()}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.3 处理超额收益率极端值"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理超额收益率极端值...\n", "极端值阈值: ±0.2\n", "极端值数量: 1799 (0.02%)\n", "\n", "极端值分布:\n", "最大值: 0.2807\n", "最小值: -0.2308\n", "\n", "应用Winsorize处理...\n", "处理后极端值数量: 0\n", "处理后超额收益率范围: [-0.2000, 0.2000]\n", "\n", "处理后超额收益率统计:\n", "count    7.728286e+06\n", "mean     1.184370e-04\n", "std      2.571638e-02\n", "min     -2.000000e-01\n", "25%     -1.235883e-02\n", "50%     -1.437806e-03\n", "75%      1.030665e-02\n", "max      2.000000e-01\n", "Name: excess_return, dtype: float64\n"]}], "source": ["# 处理超额收益率极端值\n", "print(\"\\n处理超额收益率极端值...\")\n", "\n", "# 设置极端值阈值\n", "threshold = 0.20\n", "extreme_mask = abs(merged_data['excess_return']) > threshold\n", "extreme_count = extreme_mask.sum()\n", "\n", "print(f\"极端值阈值: ±{threshold}\")\n", "print(f\"极端值数量: {extreme_count} ({extreme_count/len(merged_data)*100:.2f}%)\")\n", "\n", "if extreme_count > 0:\n", "    print(\"\\n极端值分布:\")\n", "    extreme_values = merged_data.loc[extreme_mask, 'excess_return']\n", "    print(f\"最大值: {extreme_values.max():.4f}\")\n", "    print(f\"最小值: {extreme_values.min():.4f}\")\n", "    \n", "    # Winsorize处理：截断到阈值\n", "    print(f\"\\n应用Winsorize处理...\")\n", "    merged_data.loc[extreme_mask, 'excess_return'] = np.sign(\n", "        merged_data.loc[extreme_mask, 'excess_return']\n", "    ) * threshold\n", "    \n", "    print(f\"处理后极端值数量: {(abs(merged_data['excess_return']) > threshold).sum()}\")\n", "    print(f\"处理后超额收益率范围: [{merged_data['excess_return'].min():.4f}, {merged_data['excess_return'].max():.4f}]\")\n", "else:\n", "    print(\"无需处理极端值\")\n", "\n", "# 处理后统计\n", "print(f\"\\n处理后超额收益率统计:\")\n", "print(merged_data['excess_return'].describe())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.4 验证权重计算"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "验证权重计算...\n", "权重缺失数量: 0\n", "\n", "验证权重计算...\n", "每日权重和统计:\n", "count    1.704000e+03\n", "mean     1.000000e+00\n", "std      9.598804e-17\n", "min      1.000000e+00\n", "25%      1.000000e+00\n", "50%      1.000000e+00\n", "75%      1.000000e+00\n", "max      1.000000e+00\n", "Name: weight, dtype: float64\n", "✓ 权重计算正确，每日权重和为1\n", "\n", "权重统计:\n", "count    7.728286e+06\n", "mean     2.204887e-04\n", "std      1.789235e-04\n", "min      1.977506e-05\n", "25%      1.297232e-04\n", "50%      1.732372e-04\n", "75%      2.497010e-04\n", "max      3.899076e-03\n", "Name: weight, dtype: float64\n"]}], "source": ["# 验证权重计算\n", "print(\"\\n验证权重计算...\")\n", "\n", "# 检查权重是否已存在且正确\n", "if 'weight' in merged_data.columns:\n", "    weight_missing = merged_data['weight'].isnull().sum()\n", "    print(f\"权重缺失数量: {weight_missing}\")\n", "    \n", "    if weight_missing > 0:\n", "        print(\"权重有缺失值，重新计算...\")\n", "        # 重新计算权重\n", "        def recalculate_weights(df):\n", "            df_copy = df.copy()\n", "            for date in df_copy['trade_date'].unique():\n", "                date_mask = df_copy['trade_date'] == date\n", "                date_data = df_copy[date_mask]\n", "                \n", "                # 使用原始市值计算权重\n", "                sqrt_caps = np.sqrt(date_data['total_mv'])\n", "                total_sqrt_cap = sqrt_caps.sum()\n", "                \n", "                if total_sqrt_cap > 0:\n", "                    weights = sqrt_caps / total_sqrt_cap\n", "                else:\n", "                    weights = pd.Series(1.0 / len(date_data), index=date_data.index)\n", "                \n", "                df_copy.loc[date_mask, 'weight'] = weights\n", "            \n", "            return df_copy\n", "        \n", "        merged_data = recalculate_weights(merged_data)\n", "        print(\"权重重新计算完成\")\n", "    \n", "    # 验证权重\n", "    print(\"\\n验证权重计算...\")\n", "    daily_weight_sums = merged_data.groupby('trade_date')['weight'].sum()\n", "    \n", "    print(f\"每日权重和统计:\")\n", "    print(daily_weight_sums.describe())\n", "    \n", "    # 检查权重和是否接近1\n", "    weight_sum_tolerance = 1e-10\n", "    weight_sum_check = abs(daily_weight_sums - 1.0) < weight_sum_tolerance\n", "    \n", "    if weight_sum_check.all():\n", "        print(\"✓ 权重计算正确，每日权重和为1\")\n", "    else:\n", "        problematic_dates = daily_weight_sums[~weight_sum_check]\n", "        print(f\"❌ 权重计算有误，{len(problematic_dates)}个日期的权重和不为1\")\n", "        print(\"权重和异常的日期（前5个）:\")\n", "        print(problematic_dates.head())\n", "    \n", "    # 权重统计\n", "    print(f\"\\n权重统计:\")\n", "    print(merged_data['weight'].describe())\n", "    \n", "else:\n", "    print(\"❌ 权重字段不存在\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.5 数据质量最终检查"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据质量最终检查...\n", "关键字段缺失值检查:\n", "stock_code: 0 (0.00%)\n", "trade_date: 0 (0.00%)\n", "excess_return: 0 (0.00%)\n", "weight: 0 (0.00%)\n", "\n", "数据类型检查:\n", "excess_return类型: float64\n", "weight类型: float64\n", "\n", "异常值检查:\n", "超额收益率无穷值: 0\n", "权重无穷值: 0\n", "权重负值: 0\n", "\n", "数据完整性评分: 100.00%\n", "✅ 数据质量优秀，可以进行回归分析\n"]}], "source": ["# 数据质量最终检查\n", "print(\"\\n数据质量最终检查...\")\n", "\n", "# 检查所有关键字段的缺失值\n", "key_fields = ['stock_code', 'trade_date', 'excess_return', 'weight']\n", "print(\"关键字段缺失值检查:\")\n", "for field in key_fields:\n", "    missing_count = merged_data[field].isnull().sum()\n", "    print(f\"{field}: {missing_count} ({missing_count/len(merged_data)*100:.2f}%)\")\n", "\n", "# 检查数据类型\n", "print(f\"\\n数据类型检查:\")\n", "print(f\"excess_return类型: {merged_data['excess_return'].dtype}\")\n", "print(f\"weight类型: {merged_data['weight'].dtype}\")\n", "\n", "# 检查异常值\n", "print(f\"\\n异常值检查:\")\n", "print(f\"超额收益率无穷值: {np.isinf(merged_data['excess_return']).sum()}\")\n", "print(f\"权重无穷值: {np.isinf(merged_data['weight']).sum()}\")\n", "print(f\"权重负值: {(merged_data['weight'] < 0).sum()}\")\n", "\n", "# 数据完整性评分\n", "total_cells = len(merged_data) * len(key_fields)\n", "missing_cells = sum(merged_data[field].isnull().sum() for field in key_fields)\n", "completeness = (1 - missing_cells / total_cells) * 100\n", "\n", "print(f\"\\n数据完整性评分: {completeness:.2f}%\")\n", "\n", "if completeness == 100.0:\n", "    print(\"✅ 数据质量优秀，可以进行回归分析\")\n", "elif completeness >= 95.0:\n", "    print(\"⚠️ 数据质量良好，但有少量缺失值\")\n", "else:\n", "    print(\"❌ 数据质量较差，需要进一步处理\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.6 可视化分析"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "可视化分析...\n"]}, {"data": {"image/png": "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***************************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "统计摘要:\n", "              超额收益率            权重\n", "count  7.728286e+06  7.728286e+06\n", "mean   1.184370e-04  2.204887e-04\n", "std    2.571638e-02  1.789235e-04\n", "min   -2.000000e-01  1.977506e-05\n", "25%   -1.235883e-02  1.297232e-04\n", "50%   -1.437806e-03  1.732372e-04\n", "75%    1.030665e-02  2.497010e-04\n", "max    2.000000e-01  3.899076e-03\n"]}], "source": ["# 可视化分析\n", "print(\"\\n可视化分析...\")\n", "\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "\n", "# 超额收益率分布\n", "axes[0, 0].hist(merged_data['excess_return'], bins=100, alpha=0.7, edgecolor='black')\n", "axes[0, 0].set_title('超额收益率分布')\n", "axes[0, 0].set_xlabel('超额收益率')\n", "axes[0, 0].set_ylabel('频数')\n", "axes[0, 0].axvline(0, color='red', linestyle='--', alpha=0.7)\n", "\n", "# 权重分布\n", "axes[0, 1].hist(merged_data['weight'], bins=100, alpha=0.7, edgecolor='black')\n", "axes[0, 1].set_title('权重分布')\n", "axes[0, 1].set_xlabel('权重')\n", "axes[0, 1].set_ylabel('频数')\n", "\n", "# 超额收益率时间序列（日均值）\n", "daily_excess_return = merged_data.groupby('trade_date')['excess_return'].mean()\n", "axes[1, 0].plot(daily_excess_return.index, daily_excess_return.values, alpha=0.7)\n", "axes[1, 0].set_title('日均超额收益率时间序列')\n", "axes[1, 0].set_xlabel('日期')\n", "axes[1, 0].set_ylabel('日均超额收益率')\n", "axes[1, 0].axhline(0, color='red', linestyle='--', alpha=0.7)\n", "axes[1, 0].tick_params(axis='x', rotation=45)\n", "\n", "# 超额收益率 vs 权重散点图（抽样）\n", "sample_data = merged_data.sample(n=min(10000, len(merged_data)), random_state=42)\n", "axes[1, 1].scatter(sample_data['weight'], sample_data['excess_return'], alpha=0.5, s=1)\n", "axes[1, 1].set_title('超额收益率 vs 权重（抽样）')\n", "axes[1, 1].set_xlabel('权重')\n", "axes[1, 1].set_ylabel('超额收益率')\n", "axes[1, 1].axhline(0, color='red', linestyle='--', alpha=0.7)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 统计摘要表\n", "print(\"\\n统计摘要:\")\n", "summary_stats = pd.DataFrame({\n", "    '超额收益率': merged_data['excess_return'].describe(),\n", "    '权重': merged_data['weight'].describe()\n", "})\n", "print(summary_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4.7 保存被解释变量数据"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "保存被解释变量数据...\n", "数据已保存到:\n", "- data/被解释变量Y.csv\n", "- data/dependent_variable.h5\n", "- data/processed_data_step4.csv\n", "- data/processed_data_step4.h5\n", "\n", "第4步完成！\n", "处理摘要:\n", "          指标                 数值\n", "0       总记录数            7728286\n", "1       股票数量               3213\n", "2      交易日数量               1704\n", "3    超额收益率均值           0.000118\n", "4   超额收益率标准差           0.025716\n", "5    超额收益率范围  [-0.2000, 0.2000]\n", "6       权重均值           0.000220\n", "7      权重标准差           0.000179\n", "8     权重缺失数量                  0\n", "9      数据完整性            100.00%\n", "10     极端值处理    Winsorize ±0.20\n", "\n", "关键成果:\n", "- ✅ 超额收益率计算和验证完成\n", "- ✅ 极端值处理完成（Winsorize ±0.20）\n", "- ✅ 权重计算验证完成（每日权重和=1）\n", "- ✅ 数据质量检查通过（完整性 100.0%）\n", "- ✅ 被解释变量数据准备就绪\n", "\n", "可以继续运行第5步：横截面回归模型估计\n"]}], "source": ["# 保存被解释变量数据\n", "print(\"\\n保存被解释变量数据...\")\n", "\n", "# 创建被解释变量数据集\n", "dependent_vars = merged_data[['stock_code', 'trade_date', 'excess_return', 'weight']].copy()\n", "\n", "# 保存为多种格式\n", "dependent_vars.to_csv('data/被解释变量Y.csv', index=False)\n", "dependent_vars.to_hdf('data/dependent_variable.h5', key='data', mode='w')\n", "\n", "# 保存完整数据\n", "merged_data.to_csv('data/processed_data_step4.csv', index=False)\n", "merged_data.to_hdf('data/processed_data_step4.h5', key='data', mode='w')\n", "\n", "print(f\"数据已保存到:\")\n", "print(f\"- data/被解释变量Y.csv\")\n", "print(f\"- data/dependent_variable.h5\")\n", "print(f\"- data/processed_data_step4.csv\")\n", "print(f\"- data/processed_data_step4.h5\")\n", "\n", "# 生成处理摘要\n", "summary_info = {\n", "    '指标': [\n", "        '总记录数', '股票数量', '交易日数量', \n", "        '超额收益率均值', '超额收益率标准差', '超额收益率范围',\n", "        '权重均值', '权重标准差', '权重缺失数量',\n", "        '数据完整性', '极端值处理'\n", "    ],\n", "    '数值': [\n", "        len(merged_data),\n", "        merged_data['stock_code'].nunique(),\n", "        merged_data['trade_date'].nunique(),\n", "        f\"{merged_data['excess_return'].mean():.6f}\",\n", "        f\"{merged_data['excess_return'].std():.6f}\",\n", "        f\"[{merged_data['excess_return'].min():.4f}, {merged_data['excess_return'].max():.4f}]\",\n", "        f\"{merged_data['weight'].mean():.6f}\",\n", "        f\"{merged_data['weight'].std():.6f}\",\n", "        merged_data['weight'].isnull().sum(),\n", "        f\"{completeness:.2f}%\",\n", "        \"Winsorize ±0.20\"\n", "    ]\n", "}\n", "\n", "summary_df = pd.DataFrame(summary_info)\n", "summary_df.to_csv('data/step4_summary.csv', index=False)\n", "\n", "print(f\"\\n第4步完成！\")\n", "print(f\"处理摘要:\")\n", "print(summary_df)\n", "\n", "print(f\"\\n关键成果:\")\n", "print(f\"- ✅ 超额收益率计算和验证完成\")\n", "print(f\"- ✅ 极端值处理完成（Winsorize ±0.20）\")\n", "print(f\"- ✅ 权重计算验证完成（每日权重和=1）\")\n", "print(f\"- ✅ 数据质量检查通过（完整性 {completeness:.1f}%）\")\n", "print(f\"- ✅ 被解释变量数据准备就绪\")\n", "\n", "print(\"\\n可以继续运行第5步：横截面回归模型估计\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}