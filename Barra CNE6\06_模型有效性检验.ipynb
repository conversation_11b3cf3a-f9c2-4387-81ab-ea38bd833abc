import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.linear_model import LinearRegression, Ridge
from scipy import stats
from scipy.stats import ttest_1samp
import warnings
warnings.filterwarnings('ignore')

plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

print("模型有效性检验模块开始...")
print("这是Barra模型构建的最后一步")

# 加载数据和回归结果
print("加载数据和回归结果...")

# 加载完整数据
merged_data = pd.read_hdf('data/processed_data_step4.h5', key='data')
print(f"完整数据形状: {merged_data.shape}")

# 加载回归结果
regression_results = pd.read_hdf('data/regression_results.h5', key='data')
print(f"回归结果形状: {regression_results.shape}")

# 加载因子收益
factor_returns = pd.read_hdf('data/factor_returns.h5', key='data')
print(f"因子收益形状: {factor_returns.shape}")

# 识别因子列
all_columns = merged_data.columns.tolist()
industry_factors = [col for col in all_columns if col.startswith('ind_')]
style_factors = ['size_std', 'pb_std']
available_style_factors = [col for col in style_factors if col in all_columns]
all_factors = industry_factors + available_style_factors

print(f"\n因子构成:")
print(f"- 行业因子: {len(industry_factors)}")
print(f"- 风格因子: {len(available_style_factors)}")
print(f"- 总因子数: {len(all_factors)}")

# 检查成功回归的日期
successful_dates = regression_results[regression_results['success'] == True]['trade_date'].tolist()
print(f"\n成功回归日期数: {len(successful_dates)}")
print(f"成功率: {len(successful_dates) / len(regression_results) * 100:.1f}%")

# 计算非中心化交叉验证 R²
print("\n计算非中心化交叉验证 R²...")

def calculate_cv_r_squared(date_data, factor_cols):
    """
    计算单日的交叉验证R²
    """
    try:
        n_samples = len(date_data)
        if n_samples <= len(factor_cols) + 1:
            return np.nan
        
        X = date_data[factor_cols].values
        y = date_data['excess_return'].values
        weights = date_data['weight'].values
        
        cv_residuals = []
        
        # 对每个股票进行留一法交叉验证
        for i in range(n_samples):
            # 剔除第i个股票
            X_train = np.delete(X, i, axis=0)
            y_train = np.delete(y, i)
            weights_train = np.delete(weights, i)
            
            # 加权回归
            sqrt_weights_train = np.sqrt(weights_train)
            X_weighted = X_train * sqrt_weights_train.reshape(-1, 1)
            y_weighted = y_train * sqrt_weights_train
            
            # 检查条件数
            XTX = X_weighted.T @ X_weighted
            condition_number = np.linalg.cond(XTX)
            
            if condition_number < 1e12:
                model = LinearRegression(fit_intercept=False)
            else:
                model = Ridge(alpha=0.01, fit_intercept=False)
            
            model.fit(X_weighted, y_weighted)
            
            # 预测第i个股票
            y_pred_i = X[i] @ model.coef_
            residual_i = y[i] - y_pred_i
            cv_residuals.append(residual_i)
        
        cv_residuals = np.array(cv_residuals)
        
        # 计算CV R²
        weighted_ss_res = np.sum(weights * cv_residuals**2)
        weighted_ss_tot = np.sum(weights * y**2)  # 非中心化
        
        cv_r_squared = 1 - (weighted_ss_res / weighted_ss_tot) if weighted_ss_tot > 0 else 0
        
        return cv_r_squared
        
    except Exception as e:
        print(f"CV R²计算错误: {e}")
        return np.nan

# 计算每日CV R²
cv_r_squared_results = []
sample_dates = successful_dates[:20]  # 先用前20个日期测试

print(f"计算前{len(sample_dates)}个交易日的CV R²（示例）...")
for i, date in enumerate(sample_dates):
    if (i + 1) % 5 == 0:
        print(f"进度: {i + 1}/{len(sample_dates)}")
    
    date_data = merged_data[merged_data['trade_date'] == date]
    if len(date_data) > 0:
        cv_r2 = calculate_cv_r_squared(date_data, all_factors)
        cv_r_squared_results.append({
            'trade_date': date,
            'cv_r_squared': cv_r2
        })

cv_r_squared_df = pd.DataFrame(cv_r_squared_results)
valid_cv_r2 = cv_r_squared_df['cv_r_squared'].dropna()

print(f"\nCV R²结果:")
if len(valid_cv_r2) > 0:
    print(f"有效计算日数: {len(valid_cv_r2)}")
    print(f"CV R²均值: {valid_cv_r2.mean():.4f}")
    print(f"CV R²标准差: {valid_cv_r2.std():.4f}")
    print(f"CV R²范围: [{valid_cv_r2.min():.4f}, {valid_cv_r2.max():.4f}]")
else:
    print("无有效CV R²结果")

# 计算学生化 R²
print("\n计算学生化 R²...")

def calculate_studentized_r_squared(date_data, factor_cols):
    """
    计算学生化R²
    """
    try:
        X = date_data[factor_cols].values
        y = date_data['excess_return'].values
        weights = date_data['weight'].values
        
        n_samples, n_features = X.shape
        if n_samples <= n_features:
            return np.nan
        
        # 加权回归
        sqrt_weights = np.sqrt(weights)
        X_weighted = X * sqrt_weights.reshape(-1, 1)
        y_weighted = y * sqrt_weights
        
        # 检查条件数并选择回归方法
        XTX = X_weighted.T @ X_weighted
        condition_number = np.linalg.cond(XTX)
        
        if condition_number < 1e12:
            model = LinearRegression(fit_intercept=False)
        else:
            model = Ridge(alpha=0.01, fit_intercept=False)
        
        model.fit(X_weighted, y_weighted)
        
        # 计算投影矩阵H的对角元素（杠杆值）
        try:
            XTX_inv = np.linalg.inv(XTX)
            H_diag = np.sum((X_weighted @ XTX_inv) * X_weighted, axis=1)
        except:
            # 如果矩阵奇异，使用伪逆
            XTX_pinv = np.linalg.pinv(XTX)
            H_diag = np.sum((X_weighted @ XTX_pinv) * X_weighted, axis=1)
        
        # 计算残差
        y_pred = X_weighted @ model.coef_
        residuals = y_weighted - y_pred
        
        # 学生化残差
        studentized_residuals = residuals / np.sqrt(1 - H_diag + 1e-10)  # 避免除零
        
        # 计算学生化R²
        weighted_ss_res_student = np.sum(weights * studentized_residuals**2)
        weighted_ss_tot = np.sum(weights * y**2)
        
        studentized_r_squared = 1 - (weighted_ss_res_student / weighted_ss_tot) if weighted_ss_tot > 0 else 0
        
        return studentized_r_squared
        
    except Exception as e:
        print(f"学生化R²计算错误: {e}")
        return np.nan

# 计算学生化R²
studentized_r_squared_results = []

print(f"计算前{len(sample_dates)}个交易日的学生化R²...")
for i, date in enumerate(sample_dates):
    if (i + 1) % 5 == 0:
        print(f"进度: {i + 1}/{len(sample_dates)}")
    
    date_data = merged_data[merged_data['trade_date'] == date]
    if len(date_data) > 0:
        student_r2 = calculate_studentized_r_squared(date_data, all_factors)
        studentized_r_squared_results.append({
            'trade_date': date,
            'studentized_r_squared': student_r2
        })

studentized_r_squared_df = pd.DataFrame(studentized_r_squared_results)
valid_student_r2 = studentized_r_squared_df['studentized_r_squared'].dropna()

print(f"\n学生化R²结果:")
if len(valid_student_r2) > 0:
    print(f"有效计算日数: {len(valid_student_r2)}")
    print(f"学生化R²均值: {valid_student_r2.mean():.4f}")
    print(f"学生化R²标准差: {valid_student_r2.std():.4f}")
    print(f"学生化R²范围: [{valid_student_r2.min():.4f}, {valid_student_r2.max():.4f}]")
else:
    print("无有效学生化R²结果")

# 模型解释力可视化
print("\n模型解释力可视化...")

if len(valid_cv_r2) > 0 and len(valid_student_r2) > 0:
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    
    # 原始R²时间序列
    successful_results = regression_results[regression_results['success'] == True]
    axes[0, 0].plot(successful_results['trade_date'], successful_results['r_squared'], 
                    label='原始R²', alpha=0.7)
    axes[0, 0].set_title('原始R²时间序列')
    axes[0, 0].set_xlabel('日期')
    axes[0, 0].set_ylabel('R²')
    axes[0, 0].tick_params(axis='x', rotation=45)
    axes[0, 0].legend()
    
    # CV R²时间序列
    axes[0, 1].plot(cv_r_squared_df['trade_date'], cv_r_squared_df['cv_r_squared'], 
                    label='CV R²', alpha=0.7, color='orange')
    axes[0, 1].set_title('交叉验证R²时间序列')
    axes[0, 1].set_xlabel('日期')
    axes[0, 1].set_ylabel('CV R²')
    axes[0, 1].tick_params(axis='x', rotation=45)
    axes[0, 1].legend()
    
    # 学生化R²时间序列
    axes[1, 0].plot(studentized_r_squared_df['trade_date'], 
                    studentized_r_squared_df['studentized_r_squared'], 
                    label='学生化R²', alpha=0.7, color='green')
    axes[1, 0].set_title('学生化R²时间序列')
    axes[1, 0].set_xlabel('日期')
    axes[1, 0].set_ylabel('学生化R²')
    axes[1, 0].tick_params(axis='x', rotation=45)
    axes[1, 0].legend()
    
    # R²分布对比
    axes[1, 1].hist(successful_results['r_squared'], bins=20, alpha=0.5, 
                    label='原始R²', density=True)
    axes[1, 1].hist(valid_cv_r2, bins=20, alpha=0.5, 
                    label='CV R²', density=True)
    axes[1, 1].hist(valid_student_r2, bins=20, alpha=0.5, 
                    label='学生化R²', density=True)
    axes[1, 1].set_title('R²分布对比')
    axes[1, 1].set_xlabel('R²')
    axes[1, 1].set_ylabel('密度')
    axes[1, 1].legend()
    
    plt.tight_layout()
    plt.show()
    
    # 统计摘要
    print("\n模型解释力统计摘要:")
    summary_stats = pd.DataFrame({
        '原始R²': successful_results['r_squared'].describe(),
        'CV R²': valid_cv_r2.describe(),
        '学生化R²': valid_student_r2.describe()
    })
    print(summary_stats)
    
else:
    print("数据不足，无法生成可视化图表")

# 因子收益显著性检验
print("\n因子收益显著性检验...")

# 计算风格因子的t统计量
factor_significance_results = {}

for factor in available_style_factors:
    if factor in factor_returns.columns:
        factor_return_series = factor_returns[factor].dropna()
        
        if len(factor_return_series) > 1:
            # t检验：检验因子收益是否显著不为0
            t_stat, p_value = ttest_1samp(factor_return_series, 0)
            
            # 计算t值绝对值大于1.64的频率（10%显著性水平）
            daily_t_stats = factor_return_series / factor_return_series.std() * np.sqrt(len(factor_return_series))
            significant_days = (abs(daily_t_stats) > 1.64).sum()
            significance_freq = significant_days / len(daily_t_stats)
            
            factor_significance_results[factor] = {
                'mean_return': factor_return_series.mean(),
                'std_return': factor_return_series.std(),
                't_statistic': t_stat,
                'p_value': p_value,
                'significance_freq_10pct': significance_freq,
                'n_observations': len(factor_return_series)
            }
            
            print(f"\n{factor} 因子:")
            print(f"  平均收益: {factor_return_series.mean():.6f}")
            print(f"  收益标准差: {factor_return_series.std():.6f}")
            print(f"  t统计量: {t_stat:.4f}")
            print(f"  p值: {p_value:.4f}")
            print(f"  10%显著性频率: {significance_freq:.2%}")
            
            if p_value < 0.05:
                print(f"  ✅ 在5%水平下显著")
            elif p_value < 0.10:
                print(f"  ⚠️ 在10%水平下显著")
            else:
                print(f"  ❌ 不显著")

# 转换为DataFrame
if factor_significance_results:
    significance_df = pd.DataFrame(factor_significance_results).T
    print(f"\n因子显著性检验汇总:")
    print(significance_df)

# 信息系数 (IC) 分析
print("\n信息系数 (IC) 分析...")

def calculate_ic_analysis(data, factor_cols, forward_days=20):
    """
    计算因子的信息系数
    """
    # 获取每月最后一个交易日
    data['year_month'] = data['trade_date'].dt.to_period('M')
    month_end_dates = data.groupby('year_month')['trade_date'].max().tolist()
    
    ic_results = {factor: [] for factor in factor_cols}
    ic_dates = []
    
    print(f"计算IC，使用{len(month_end_dates)}个月末日期...")
    
    for i, month_end in enumerate(month_end_dates[:12]):  # 先计算前12个月
        if (i + 1) % 3 == 0:
            print(f"进度: {i + 1}/{min(12, len(month_end_dates))}")
        
        # 获取月末因子暴露
        month_end_data = data[data['trade_date'] == month_end]
        
        if len(month_end_data) == 0:
            continue
        
        # 计算未来收益
        future_returns = []
        valid_stocks = []
        
        for stock in month_end_data['stock_code'].unique():
            stock_data = data[data['stock_code'] == stock]
            stock_data_sorted = stock_data.sort_values('trade_date')
            
            # 找到月末日期的位置
            month_end_idx = stock_data_sorted[stock_data_sorted['trade_date'] == month_end].index
            if len(month_end_idx) == 0:
                continue
            
            month_end_pos = stock_data_sorted.index.get_loc(month_end_idx[0])
            
            # 计算未来20个交易日的累计收益
            future_data = stock_data_sorted.iloc[month_end_pos+1:month_end_pos+1+forward_days]
            
            if len(future_data) >= forward_days * 0.8:  # 至少有80%的数据
                future_return = future_data['excess_return'].sum()
                future_returns.append(future_return)
                valid_stocks.append(stock)
        
        if len(future_returns) < 30:  # 至少需要30个股票
            continue
        
        # 获取有效股票的因子暴露
        valid_month_data = month_end_data[month_end_data['stock_code'].isin(valid_stocks)]
        
        # 计算IC
        for factor in factor_cols:
            if factor in valid_month_data.columns:
                factor_exposure = valid_month_data.set_index('stock_code')[factor].reindex(valid_stocks)
                
                if len(factor_exposure.dropna()) >= 30:
                    ic = np.corrcoef(factor_exposure.dropna(), 
                                   np.array(future_returns)[:len(factor_exposure.dropna())])[0, 1]
                    if not np.isnan(ic):
                        ic_results[factor].append(ic)
        
        ic_dates.append(month_end)
    
    return ic_results, ic_dates

# 计算IC
ic_results, ic_dates = calculate_ic_analysis(merged_data, available_style_factors)

# 分析IC结果
ic_analysis = {}
for factor in available_style_factors:
    if factor in ic_results and len(ic_results[factor]) > 0:
        ic_series = np.array(ic_results[factor])
        
        ic_analysis[factor] = {
            'ic_mean': ic_series.mean(),
            'ic_std': ic_series.std(),
            'icir': ic_series.mean() / ic_series.std() if ic_series.std() > 0 else 0,
            'ic_positive_rate': (ic_series > 0).mean(),
            'n_periods': len(ic_series)
        }
        
        print(f"\n{factor} IC分析:")
        print(f"  IC均值: {ic_series.mean():.4f}")
        print(f"  IC标准差: {ic_series.std():.4f}")
        print(f"  ICIR: {ic_analysis[factor]['icir']:.4f}")
        print(f"  IC正值比例: {ic_analysis[factor]['ic_positive_rate']:.2%}")
        print(f"  有效期数: {len(ic_series)}")

if ic_analysis:
    ic_analysis_df = pd.DataFrame(ic_analysis).T
    print(f"\nIC分析汇总:")
    print(ic_analysis_df)

# 分组收益检验
print("\n分组收益检验...")

def calculate_quintile_returns(data, factor_col, forward_days=20, n_groups=5):
    """
    计算因子分组收益
    """
    # 获取每月最后一个交易日
    data['year_month'] = data['trade_date'].dt.to_period('M')
    month_end_dates = data.groupby('year_month')['trade_date'].max().tolist()
    
    group_returns = {f'G{i+1}': [] for i in range(n_groups)}
    long_short_returns = []
    valid_dates = []
    
    print(f"计算{factor_col}分组收益，使用{len(month_end_dates[:12])}个月末日期...")
    
    for i, month_end in enumerate(month_end_dates[:12]):  # 前12个月
        if (i + 1) % 3 == 0:
            print(f"进度: {i + 1}/{min(12, len(month_end_dates))}")
        
        # 获取月末数据
        month_end_data = data[data['trade_date'] == month_end]
        
        if len(month_end_data) < 50:  # 至少需要50个股票
            continue
        
        # 按因子值分组
        month_end_data = month_end_data.dropna(subset=[factor_col])
        month_end_data['factor_quintile'] = pd.qcut(month_end_data[factor_col], 
                                                   q=n_groups, labels=False, duplicates='drop')
        
        # 计算各组未来收益
        group_future_returns = {}
        
        for group in range(n_groups):
            group_stocks = month_end_data[month_end_data['factor_quintile'] == group]['stock_code'].tolist()
            
            if len(group_stocks) == 0:
                continue
            
            group_returns_list = []
            group_weights_list = []
            
            for stock in group_stocks:
                stock_data = data[data['stock_code'] == stock]
                stock_data_sorted = stock_data.sort_values('trade_date')
                
                # 找到月末位置
                month_end_idx = stock_data_sorted[stock_data_sorted['trade_date'] == month_end].index
                if len(month_end_idx) == 0:
                    continue
                
                month_end_pos = stock_data_sorted.index.get_loc(month_end_idx[0])
                
                # 计算未来收益
                future_data = stock_data_sorted.iloc[month_end_pos+1:month_end_pos+1+forward_days]
                
                if len(future_data) >= forward_days * 0.8:
                    future_return = future_data['excess_return'].sum()
                    weight = month_end_data[month_end_data['stock_code'] == stock]['weight'].iloc[0]
                    
                    group_returns_list.append(future_return)
                    group_weights_list.append(weight)
            
            if len(group_returns_list) > 0:
                # 加权平均收益
                weights = np.array(group_weights_list)
                weights = weights / weights.sum()  # 标准化权重
                weighted_return = np.average(group_returns_list, weights=weights)
                group_future_returns[group] = weighted_return
        
        # 记录各组收益
        if len(group_future_returns) == n_groups:
            for group in range(n_groups):
                group_returns[f'G{group+1}'].append(group_future_returns[group])
            
            # 计算多空收益（G5 - G1）
            long_short_return = group_future_returns[n_groups-1] - group_future_returns[0]
            long_short_returns.append(long_short_return)
            valid_dates.append(month_end)
    
    return group_returns, long_short_returns, valid_dates

# 计算各因子的分组收益
quintile_results = {}

for factor in available_style_factors:
    if factor in merged_data.columns:
        group_returns, long_short_returns, valid_dates = calculate_quintile_returns(merged_data, factor)
        
        if len(long_short_returns) > 0:
            quintile_results[factor] = {
                'group_returns': group_returns,
                'long_short_returns': long_short_returns,
                'valid_dates': valid_dates
            }
            
            print(f"\n{factor} 分组收益分析:")
            
            # 各组平均收益
            for group, returns in group_returns.items():
                if len(returns) > 0:
                    avg_return = np.mean(returns)
                    print(f"  {group}组平均收益: {avg_return:.4f}")
            
            # 多空收益统计
            ls_mean = np.mean(long_short_returns)
            ls_std = np.std(long_short_returns)
            ls_t_stat = ls_mean / ls_std * np.sqrt(len(long_short_returns)) if ls_std > 0 else 0
            
            print(f"  多空收益均值: {ls_mean:.4f}")
            print(f"  多空收益标准差: {ls_std:.4f}")
            print(f"  多空收益t统计量: {ls_t_stat:.4f}")
            
            if abs(ls_t_stat) > 1.96:
                print(f"  ✅ 多空收益在5%水平下显著")
            elif abs(ls_t_stat) > 1.64:
                print(f"  ⚠️ 多空收益在10%水平下显著")
            else:
                print(f"  ❌ 多空收益不显著")

# 综合可视化分析
print("\n综合可视化分析...")

# 1. 因子收益时间序列
if len(available_style_factors) > 0:
    fig, axes = plt.subplots(len(available_style_factors), 1, figsize=(15, 6*len(available_style_factors)))
    if len(available_style_factors) == 1:
        axes = [axes]
    
    for i, factor in enumerate(available_style_factors):
        if factor in factor_returns.columns:
            factor_data = factor_returns[factor].dropna()
            
            # 因子收益时间序列
            axes[i].plot(factor_data.index, factor_data.values, alpha=0.7)
            axes[i].set_title(f'{factor} 因子收益时间序列')
            axes[i].set_xlabel('日期')
            axes[i].set_ylabel('因子收益')
            axes[i].axhline(0, color='red', linestyle='--', alpha=0.7)
            axes[i].tick_params(axis='x', rotation=45)
            
            # 添加统计信息
            mean_return = factor_data.mean()
            std_return = factor_data.std()
            axes[i].text(0.02, 0.98, f'均值: {mean_return:.6f}\\n标准差: {std_return:.6f}', 
                        transform=axes[i].transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

# 2. IC时间序列
if ic_results:
    fig, axes = plt.subplots(len(available_style_factors), 1, figsize=(15, 6*len(available_style_factors)))
    if len(available_style_factors) == 1:
        axes = [axes]
    
    for i, factor in enumerate(available_style_factors):
        if factor in ic_results and len(ic_results[factor]) > 0:
            ic_series = ic_results[factor]
            dates = ic_dates[:len(ic_series)]
            
            axes[i].plot(dates, ic_series, marker='o', alpha=0.7)
            axes[i].set_title(f'{factor} IC时间序列')
            axes[i].set_xlabel('日期')
            axes[i].set_ylabel('IC')
            axes[i].axhline(0, color='red', linestyle='--', alpha=0.7)
            axes[i].tick_params(axis='x', rotation=45)
            
            # 添加统计信息
            ic_mean = np.mean(ic_series)
            ic_std = np.std(ic_series)
            icir = ic_mean / ic_std if ic_std > 0 else 0
            axes[i].text(0.02, 0.98, f'IC均值: {ic_mean:.4f}\\nIC标准差: {ic_std:.4f}\\nICIR: {icir:.4f}', 
                        transform=axes[i].transAxes, verticalalignment='top',
                        bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
    
    plt.tight_layout()
    plt.show()

print("可视化分析完成")

# 生成模型有效性报告
print("\n生成模型有效性报告...")

# 收集所有结果
report_data = {
    'model_performance': {},
    'factor_significance': {},
    'ic_analysis': {},
    'quintile_analysis': {}
}

# 1. 模型整体表现
successful_results = regression_results[regression_results['success'] == True]
if len(successful_results) > 0:
    report_data['model_performance'] = {
        'success_rate': len(successful_results) / len(regression_results),
        'avg_r_squared': successful_results['r_squared'].mean(),
        'r_squared_std': successful_results['r_squared'].std(),
        'avg_condition_number': successful_results['condition_number'].mean(),
        'avg_sample_size': successful_results['n_samples'].mean()
    }
    
    if len(valid_cv_r2) > 0:
        report_data['model_performance']['avg_cv_r_squared'] = valid_cv_r2.mean()
        report_data['model_performance']['cv_r_squared_std'] = valid_cv_r2.std()
    
    if len(valid_student_r2) > 0:
        report_data['model_performance']['avg_studentized_r_squared'] = valid_student_r2.mean()
        report_data['model_performance']['studentized_r_squared_std'] = valid_student_r2.std()

# 2. 因子显著性
if factor_significance_results:
    report_data['factor_significance'] = factor_significance_results

# 3. IC分析
if ic_analysis:
    report_data['ic_analysis'] = ic_analysis

# 4. 分组分析
if quintile_results:
    quintile_summary = {}
    for factor, results in quintile_results.items():
        long_short_returns = results['long_short_returns']
        if len(long_short_returns) > 0:
            ls_mean = np.mean(long_short_returns)
            ls_std = np.std(long_short_returns)
            ls_sharpe = ls_mean / ls_std if ls_std > 0 else 0
            ls_t_stat = ls_mean / ls_std * np.sqrt(len(long_short_returns)) if ls_std > 0 else 0
            
            quintile_summary[factor] = {
                'long_short_mean': ls_mean,
                'long_short_std': ls_std,
                'long_short_sharpe': ls_sharpe,
                'long_short_t_stat': ls_t_stat,
                'n_periods': len(long_short_returns)
            }
    
    report_data['quintile_analysis'] = quintile_summary

# 保存报告
import json

# 转换numpy类型为Python原生类型
def convert_numpy_types(obj):
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj

report_data_clean = convert_numpy_types(report_data)

# 保存为JSON
with open('data/model_validation_report.json', 'w', encoding='utf-8') as f:
    json.dump(report_data_clean, f, indent=2, ensure_ascii=False)

print("模型有效性报告已保存到: data/model_validation_report.json")

# 关键发现与结论
print("\n" + "="*60)
print("模型有效性检验 - 关键发现与结论")
print("="*60)

# 1. 模型整体表现
print("\n1. 模型整体表现:")
if 'model_performance' in report_data and report_data['model_performance']:
    perf = report_data['model_performance']
    print(f"   ✓ 回归成功率: {perf['success_rate']:.1%}")
    print(f"   ✓ 平均R²: {perf['avg_r_squared']:.4f}")
    
    if 'avg_cv_r_squared' in perf:
        print(f"   ✓ 平均CV R²: {perf['avg_cv_r_squared']:.4f}")
    
    if 'avg_studentized_r_squared' in perf:
        print(f"   ✓ 平均学生化R²: {perf['avg_studentized_r_squared']:.4f}")
    
    print(f"   ✓ 平均样本数: {perf['avg_sample_size']:.0f}")
    
    # 评价模型解释力
    if perf['avg_r_squared'] > 0.15:
        print("   🎉 模型解释力优秀")
    elif perf['avg_r_squared'] > 0.08:
        print("   ✅ 模型解释力良好")
    else:
        print("   ⚠️ 模型解释力一般")
else:
    print("   ❌ 无法评估模型整体表现")

print("\n🎉 Barra模型构建与验证完成！")
print("="*60)

print(f"\n📁 主要输出文件:")
print(f"   - data/model_validation_report.json (验证报告)")
print(f"   - data/日度因子收益.csv (因子收益)")
print(f"   - data/因子暴露矩阵X.csv (因子暴露)")
print(f"   - data/被解释变量Y.csv (被解释变量)")

print(f"\n🚀 模型应用建议:")
print(f"   1. 基于因子收益构建风险模型")
print(f"   2. 开发多因子选股策略")
print(f"   3. 进行组合风险归因分析")
print(f"   4. 优化投资组合配置")