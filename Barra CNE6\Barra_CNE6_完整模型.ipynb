{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Barra CNE6 多因子风险模型完整实现\n", "\n", "## 目标\n", "基于现有数据完整复现Barra CNE6风险模型，包括：\n", "1. **国家因子**: 中国市场系统性风险\n", "2. **行业因子**: 基于申万行业分类\n", "3. **风格因子**: Size和Value因子\n", "4. **横截面回归**: 市值加权最小二乘\n", "5. **模型验证**: 基于基准指数的回测验证\n", "\n", "## 数据来源\n", "- 因子数据: factor.h5 (PB, 市值)\n", "- 行业数据: swind.xlsx (申万行业分类)\n", "- IPO数据: ipodate.csv (上市日期)\n", "- 收益率数据: daily0925.h5 (全市场股票收益率)\n", "- 市值数据: ind.h5\n", "\n", "## 回测框架\n", "严格按照研报标准实现：\n", "1. **数据筛选**: 剔除上市不足60个交易日、收益率>23%的异常数据\n", "2. **模型验证**: 非中心化交叉验证R²、学生化R²\n", "3. **因子检验**: 统计检验(t值、IC、ICIR)和分组收益检验\n", "4. **时间范围**: 2014年1月-2024年9月(10年+数据)"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Barra CNE6 多因子风险模型开始构建...\n", "输出路径: Barra CNE6\\results\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import h5py\n", "import warnings\n", "from datetime import datetime, timedelta\n", "import os\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import StandardScaler\n", "import matplotlib.dates as mdates\n", "\n", "warnings.filterwarnings('ignore')\n", "\n", "# 设置中文字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "plt.style.use('seaborn-v0_8')\n", "\n", "# 路径设置\n", "DATA_PATH = r\"C:\\Users\\<USER>\\Desktop\\金元顺安\\单因子\\data\"\n", "MARKET_DATA_PATH = r\"C:\\Users\\<USER>\\Desktop\\金元顺安\\一致预期\\data\"\n", "OUTPUT_PATH = r'Barra CNE6\\results'\n", "\n", "# 创建输出目录\n", "os.makedirs(OUTPUT_PATH, exist_ok=True)\n", "\n", "print(\"Barra CNE6 多因子风险模型开始构建...\")\n", "print(f\"输出路径: {OUTPUT_PATH}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 数据加载与预处理"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载数据 ===\n", "加载因子数据...\n", "因子名称: ['pb', 'total_mv']\n", "数据记录数: 500000\n", "加载市值数据...\n", "市值数据结构: ['data']\n", "市值数据组: ['axis0', 'axis1', 'block0_items', 'block0_values', 'block1_items', 'block1_values', 'block2_items', 'block2_values']\n", "在block1中找到total_mv，索引位置: 5\n", "市值数据(total_mv)加载成功: 100000条记录\n", "行业数据: (5739, 10), 行业数: 31\n", "基准数据: (6192, 3)\n", "列名: ['date', 'close', 'benchmark_return']\n", "IPO数据: (5653, 3)\n"]}], "source": ["def load_all_data():\n", "    \"\"\"加载所有数据并进行初步处理，从ind.h5中读取total_mv列作为市值数据\"\"\"\n", "    print(\"=== 加载数据 ===\")\n", "    \n", "    data_dict = {}\n", "    \n", "    # 1. 加载因子数据 (factor.h5)\n", "    try:\n", "        factor_path = os.path.join(DATA_PATH, 'factor.h5')\n", "        print(\"加载因子数据...\")\n", "        \n", "        with h5py.File(factor_path, 'r') as f:\n", "            data_group = f['data']\n", "            \n", "            # 读取因子名称\n", "            factor_names = [name.decode('utf-8') if isinstance(name, bytes) else str(name) \n", "                          for name in data_group['block0_items'][:]]\n", "            \n", "            # 限制数据量以提高处理速度\n", "            max_records = 500000\n", "            factor_values = data_group['block0_values'][:max_records]\n", "            dates_idx = data_group['axis1_label0'][:max_records]\n", "            stocks_idx = data_group['axis1_label1'][:max_records]\n", "            \n", "            # 读取索引映射\n", "            date_levels = data_group['axis1_level0'][:]\n", "            stock_levels = data_group['axis1_level1'][:]\n", "            \n", "            # 解码股票代码\n", "            stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) \n", "                         for code in stock_levels]\n", "            \n", "            print(f\"因子名称: {factor_names}\")\n", "            print(f\"数据记录数: {len(factor_values)}\")\n", "            \n", "            data_dict['factor_raw'] = {\n", "                'factor_names': factor_names,\n", "                'factor_values': factor_values,\n", "                'dates_idx': dates_idx,\n", "                'stocks_idx': stocks_idx,\n", "                'date_levels': date_levels,\n", "                'stock_codes': stock_codes\n", "            }\n", "            \n", "    except Exception as e:\n", "        print(f\"加载因子数据失败: {e}\")\n", "        return None\n", "    \n", "    # 2. 加载市值数据 (ind.h5) - 优化的total_mv读取逻辑\n", "    try:\n", "        mv_path = os.path.join(MARKET_DATA_PATH, 'ind.h5')\n", "        print(\"加载市值数据...\")\n", "        \n", "        with h5py.File(mv_path, 'r') as f:\n", "            # 检查数据结构\n", "            print(f\"市值数据结构: {list(f.keys())}\")\n", "            \n", "            if 'data' in f:\n", "                mv_group = f['data']\n", "                print(f\"市值数据组: {list(mv_group.keys())}\")\n", "                \n", "                # 查找total_mv所在的block\n", "                total_mv_block = None\n", "                total_mv_index = -1\n", "                # 检查所有可能的block\n", "                for i in range(10):  # 检查前10个block，可根据实际情况调整\n", "                    items_key = f'block{i}_items'\n", "                    if items_key in mv_group:\n", "                        # 读取该block包含的指标名称\n", "                        items = [item.decode('utf-8') if isinstance(item, bytes) else str(item) \n", "                                for item in mv_group[items_key][:]]\n", "                        if 'total_mv' in items:\n", "                            total_mv_block = i\n", "                            total_mv_index = items.index('total_mv')\n", "                            print(f\"在block{total_mv_block}中找到total_mv，索引位置: {total_mv_index}\")\n", "                            break\n", "                \n", "                if total_mv_block is not None and total_mv_index != -1:\n", "                    # 读取市值数据\n", "                    values_key = f'block{total_mv_block}_values'\n", "                    if values_key in mv_group and 'axis1' in mv_group:\n", "                        # 限制数据量\n", "                        max_mv_records = 100000\n", "                        all_values = mv_group[values_key][:max_mv_records]\n", "                        # 提取total_mv列\n", "                        mv_values = all_values[:, total_mv_index]\n", "                        \n", "                        # 处理日期和股票索引\n", "                        axis1_data = mv_group['axis1'][:max_mv_records]\n", "                        # 假设axis1是二维数组[日期索引, 股票索引]\n", "                        mv_dates_idx = axis1_data[:, 0] if axis1_data.ndim == 2 else axis1_data\n", "                        mv_stocks_idx = axis1_data[:, 1] if axis1_data.ndim == 2 else None\n", "                        \n", "                        # 处理日期和股票的实际值映射\n", "                        mv_date_levels = mv_group['axis0'][:] if 'axis0' in mv_group else None\n", "                        mv_stock_levels = mv_group['axis2'][:] if 'axis2' in mv_group else None\n", "                        \n", "                        # 解码股票代码\n", "                        if mv_stock_levels is not None:\n", "                            mv_stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) \n", "                                            for code in mv_stock_levels]\n", "                        else:\n", "                            mv_stock_codes = None\n", "                        \n", "                        data_dict['mv_raw'] = {\n", "                            'mv_values': mv_values,  # total_mv数据\n", "                            'dates_idx': mv_dates_idx,\n", "                            'stocks_idx': mv_stocks_idx,\n", "                            'date_levels': mv_date_levels,\n", "                            'stock_codes': mv_stock_codes\n", "                        }\n", "                        print(f\"市值数据(total_mv)加载成功: {len(mv_values)}条记录\")\n", "                    else:\n", "                        print(f\"未找到市值数据的{values_key}或axis1\")\n", "                        data_dict['mv_raw'] = None\n", "                else:\n", "                    print(\"未在ind.h5中找到total_mv数据\")\n", "                    data_dict['mv_raw'] = None\n", "            else:\n", "                print(\"市值数据文件中未找到data组\")\n", "                data_dict['mv_raw'] = None\n", "                \n", "    except Exception as e:\n", "        print(f\"加载市值数据失败: {e}\")\n", "        data_dict['mv_raw'] = None\n", "    \n", "    # 3. 加载行业数据\n", "    try:\n", "        industry_path = os.path.join(DATA_PATH, 'swind.xlsx')\n", "        industry_data = pd.read_excel(industry_path)\n", "        \n", "        # 取最新行业分类\n", "        if 'in_date' in industry_data.columns:\n", "            industry_data['in_date'] = pd.to_datetime(industry_data['in_date'])\n", "            industry_data = industry_data.sort_values('in_date').groupby('ts_code').last().reset_index()\n", "        \n", "        data_dict['industry_data'] = industry_data\n", "        print(f\"行业数据: {industry_data.shape}, 行业数: {industry_data['l1_name'].nunique()}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"加载行业数据失败: {e}\")\n", "        return None\n", "    \n", "    # 4. 加载基准指数数据\n", "    try:\n", "        benchmark_path = os.path.join(MARKET_DATA_PATH, 'windaew.csv')\n", "        benchmark_data = pd.read_csv(benchmark_path)\n", "        \n", "        # 处理日期列\n", "        date_cols = ['trade_date', 'date', 'trading_date']\n", "        for col in date_cols:\n", "            if col in benchmark_data.columns:\n", "                benchmark_data[col] = pd.to_datetime(benchmark_data[col])\n", "                benchmark_data.rename(columns={col: 'date'}, inplace=True)\n", "                break\n", "        \n", "        # 计算基准收益率\n", "        if 'close' in benchmark_data.columns:\n", "            benchmark_data = benchmark_data.sort_values('date')\n", "            benchmark_data['benchmark_return'] = benchmark_data['close'].pct_change()\n", "        elif 'value' in benchmark_data.columns:\n", "            benchmark_data = benchmark_data.sort_values('date')\n", "            benchmark_data['benchmark_return'] = benchmark_data['value'].pct_change()\n", "        \n", "        data_dict['benchmark_data'] = benchmark_data\n", "        print(f\"基准数据: {benchmark_data.shape}\")\n", "        print(f\"列名: {list(benchmark_data.columns)}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"加载基准数据失败: {e}\")\n", "        data_dict['benchmark_data'] = None\n", "    \n", "    # 5. 加载IPO数据\n", "    try:\n", "        ipo_path = os.path.join(DATA_PATH, 'ipodate.csv')\n", "        ipo_data = pd.read_csv(ipo_path)\n", "        \n", "        # 处理日期列\n", "        date_cols = ['list_date', 'ipo_date', 'listing_date']\n", "        for col in date_cols:\n", "            if col in ipo_data.columns:\n", "                ipo_data[col] = pd.to_datetime(ipo_data[col])\n", "                ipo_data.rename(columns={col: 'list_date'}, inplace=True)\n", "                break\n", "        \n", "        data_dict['ipo_data'] = ipo_data\n", "        print(f\"IPO数据: {ipo_data.shape}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"加载IPO数据失败: {e}\")\n", "        return None\n", "    \n", "    return data_dict\n", "\n", "# 加载数据\n", "all_data = load_all_data()\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 构建统一因子数据框"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 构建因子数据框 ===\n", "处理因子数据...\n", "进度: 0/200000\n", "进度: 20000/200000\n", "进度: 40000/200000\n", "进度: 60000/200000\n", "进度: 80000/200000\n", "进度: 100000/200000\n", "进度: 120000/200000\n", "进度: 140000/200000\n", "进度: 160000/200000\n", "进度: 180000/200000\n", "因子数据框: (199151, 4)\n", "日期范围: 2015-12-31 00:00:00 到 2016-04-28 00:00:00\n", "股票数量: 2785\n", "因子列: ['pb', 'total_mv']\n", "过滤后数据: (197417, 5)\n", "行业覆盖率: 100.00%\n", "行业分布: l1_name\n", "医药生物    16750\n", "机械设备    15763\n", "基础化工    12997\n", "电力设备    12039\n", "计算机     11139\n", "Name: count, dtype: int64\n"]}], "source": ["def build_factor_dataframe(all_data):\n", "    \"\"\"构建统一的因子数据框\"\"\"\n", "    print(\"\\n=== 构建因子数据框 ===\")\n", "    \n", "    if all_data is None:\n", "        return None\n", "    \n", "    factor_raw = all_data['factor_raw']\n", "    industry_data = all_data['industry_data']\n", "    ipo_data = all_data['ipo_data']\n", "    \n", "    # 构建因子数据\n", "    factor_list = []\n", "    \n", "    print(\"处理因子数据...\")\n", "    for i in range(min(200000, len(factor_raw['factor_values']))):\n", "        if i % 20000 == 0:\n", "            print(f\"进度: {i}/{min(200000, len(factor_raw['factor_values']))}\")\n", "        \n", "        try:\n", "            date_idx = factor_raw['dates_idx'][i]\n", "            stock_idx = factor_raw['stocks_idx'][i]\n", "            \n", "            if date_idx < len(factor_raw['date_levels']) and stock_idx < len(factor_raw['stock_codes']):\n", "                # 处理日期\n", "                date_raw = factor_raw['date_levels'][date_idx]\n", "                \n", "                try:\n", "                    if date_raw > 1e15:\n", "                        date = pd.to_datetime(date_raw, unit='ns')\n", "                    elif date_raw > 1e12:\n", "                        date = pd.to_datetime(date_raw, unit='ms')\n", "                    elif date_raw > 1e9:\n", "                        date = pd.to_datetime(date_raw, unit='s')\n", "                    else:\n", "                        date = pd.to_datetime(str(int(date_raw)), format='%Y%m%d')\n", "                except:\n", "                    continue\n", "                \n", "                stock = factor_raw['stock_codes'][stock_idx]\n", "                factor_values = factor_raw['factor_values'][i]\n", "                \n", "                if not np.any(np.isnan(factor_values)):\n", "                    record = {\n", "                        'date': date,\n", "                        'ts_code': stock\n", "                    }\n", "                    \n", "                    # 添加因子值\n", "                    for j, factor_name in enumerate(factor_raw['factor_names']):\n", "                        record[factor_name] = factor_values[j]\n", "                    \n", "                    factor_list.append(record)\n", "        \n", "        except Exception as e:\n", "            continue\n", "    \n", "    if not factor_list:\n", "        print(\"构建因子数据框失败\")\n", "        return None\n", "    \n", "    factor_df = pd.DataFrame(factor_list)\n", "    factor_df = factor_df.sort_values(['date', 'ts_code'])\n", "    \n", "    print(f\"因子数据框: {factor_df.shape}\")\n", "    print(f\"日期范围: {factor_df['date'].min()} 到 {factor_df['date'].max()}\")\n", "    print(f\"股票数量: {factor_df['ts_code'].nunique()}\")\n", "    print(f\"因子列: {[col for col in factor_df.columns if col not in ['date', 'ts_code']]}\")\n", "    \n", "    # 合并行业信息\n", "    factor_df = factor_df.merge(\n", "        industry_data[['ts_code', 'l1_name']], \n", "        on='ts_code', \n", "        how='left'\n", "    )\n", "    \n", "    # 处理缺失行业\n", "    factor_df['l1_name'].fillna('其他', inplace=True)\n", "    \n", "    # 合并IPO信息并过滤\n", "    factor_df = factor_df.merge(\n", "        ipo_data[['ts_code', 'list_date']], \n", "        on='ts_code', \n", "        how='left'\n", "    )\n", "    \n", "    # 剔除上市不足60个交易日的股票\n", "    factor_df['days_since_ipo'] = (factor_df['date'] - factor_df['list_date']).dt.days\n", "    factor_df = factor_df[factor_df['days_since_ipo'] >= 60].copy()\n", "    factor_df.drop(['list_date', 'days_since_ipo'], axis=1, inplace=True)\n", "    \n", "    print(f\"过滤后数据: {factor_df.shape}\")\n", "    print(f\"行业覆盖率: {factor_df['l1_name'].notna().mean():.2%}\")\n", "    print(f\"行业分布: {factor_df['l1_name'].value_counts().head()}\")\n", "    \n", "    return factor_df\n", "\n", "# 构建因子数据框\n", "if all_data is not None:\n", "    factor_df = build_factor_dataframe(all_data)\n", "else:\n", "    factor_df = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 构建完整因子体系"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 构建完整因子体系 ===\n", "✓ 构建国家因子 (China)\n", "✓ 构建行业因子: 31个行业\n", "✓ 构建Size风格因子 (基于total_mv)\n", "✓ 构建Value风格因子 (基于PB)\n", "\n", "因子体系总结:\n", "  国家因子: 1个\n", "  行业因子: 31个\n", "  风格因子: 2个\n", "  总因子数: 34个\n"]}], "source": ["def build_complete_factor_system(factor_df):\n", "    \"\"\"构建完整的Barra因子体系\"\"\"\n", "    print(\"\\n=== 构建完整因子体系 ===\")\n", "    \n", "    if factor_df is None:\n", "        return None, None\n", "    \n", "    complete_factors = factor_df.copy()\n", "    \n", "    # 1. 国家因子 - 所有股票暴露度为1\n", "    complete_factors['Country_China'] = 1.0\n", "    print(\"✓ 构建国家因子 (China)\")\n", "    \n", "    # 2. 行业因子 - 基于申万行业分类\n", "    industries = sorted(complete_factors['l1_name'].unique())\n", "    print(f\"✓ 构建行业因子: {len(industries)}个行业\")\n", "    \n", "    for industry in industries:\n", "        col_name = f'Industry_{industry}'\n", "        complete_factors[col_name] = (complete_factors['l1_name'] == industry).astype(float)\n", "    \n", "    # 3. 风格因子 - 基于现有数据\n", "    style_factors = []\n", "    \n", "    # Size因子 (规模) - 基于市值\n", "    if 'total_mv' in complete_factors.columns:\n", "        complete_factors['Style_Size_raw'] = np.log(complete_factors['total_mv'])\n", "        style_factors.append('Style_Size_raw')\n", "        print(\"✓ 构建Size风格因子 (基于total_mv)\")\n", "    elif 'mv' in complete_factors.columns:\n", "        complete_factors['Style_Size_raw'] = np.log(complete_factors['mv'])\n", "        style_factors.append('Style_Size_raw')\n", "        print(\"✓ 构建Size风格因子 (基于mv)\")\n", "    else:\n", "        print(\"⚠️ 未找到市值数据，无法构建Size因子\")\n", "    \n", "    # Value因子 (价值) - 基于PB\n", "    if 'pb' in complete_factors.columns:\n", "        # 使用账面市值比 (1/PB)\n", "        complete_factors['Style_Value_raw'] = 1 / complete_factors['pb']\n", "        # 去极值处理\n", "        complete_factors['Style_Value_raw'] = complete_factors['Style_Value_raw'].clip(\n", "            complete_factors['Style_Value_raw'].quantile(0.01),\n", "            complete_factors['Style_Value_raw'].quantile(0.99)\n", "        )\n", "        style_factors.append('Style_Value_raw')\n", "        print(\"✓ 构建Value风格因子 (基于PB)\")\n", "    else:\n", "        print(\"⚠️ 未找到PB数据，无法构建Value因子\")\n", "    \n", "    # 获取因子列名\n", "    country_factors = ['Country_China']\n", "    industry_factors = [col for col in complete_factors.columns if col.startswith('Industry_')]\n", "    \n", "    print(f\"\\n因子体系总结:\")\n", "    print(f\"  国家因子: {len(country_factors)}个\")\n", "    print(f\"  行业因子: {len(industry_factors)}个\")\n", "    print(f\"  风格因子: {len(style_factors)}个\")\n", "    print(f\"  总因子数: {len(country_factors) + len(industry_factors) + len(style_factors)}个\")\n", "    \n", "    return complete_factors, {\n", "        'country_factors': country_factors,\n", "        'industry_factors': industry_factors,\n", "        'style_factors': style_factors\n", "    }\n", "\n", "# 构建完整因子体系\n", "if factor_df is not None:\n", "    complete_factors, factor_groups = build_complete_factor_system(factor_df)\n", "else:\n", "    complete_factors, factor_groups = None, None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 数据预处理（标准Barra流程）"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 数据预处理 (标准Barra流程) ===\n", "开始处理 79 个交易日...\n", "处理进度: 0/79 (2015-12-31)\n", "处理进度: 50/79 (2016-03-18)\n", "\n", "预处理完成: (197417, 43)\n", "处理后的风格因子: ['Style_Value_orth', 'Style_Size_orth']\n"]}], "source": ["def preprocess_factors(complete_factors, factor_groups):\n", "    \"\"\"按照标准Barra流程进行数据预处理\"\"\"\n", "    print(\"\\n=== 数据预处理 (标准Barra流程) ===\")\n", "    \n", "    if complete_factors is None or factor_groups is None:\n", "        return None\n", "    \n", "    processed_data = complete_factors.copy()\n", "    style_factors = factor_groups['style_factors']\n", "    \n", "    if not style_factors:\n", "        print(\"没有风格因子需要预处理\")\n", "        return processed_data\n", "    \n", "    # 按日期分组处理\n", "    processed_list = []\n", "    dates = sorted(processed_data['date'].unique())\n", "    \n", "    print(f\"开始处理 {len(dates)} 个交易日...\")\n", "    \n", "    for i, date in enumerate(dates):\n", "        if i % 50 == 0:\n", "            print(f\"处理进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})\")\n", "        \n", "        date_data = processed_data[processed_data['date'] == date].copy()\n", "        \n", "        if len(date_data) < 50:  # 至少需要50只股票\n", "            continue\n", "        \n", "        # 1. 去极值 (五倍中位数法)\n", "        for factor in style_factors:\n", "            if factor in date_data.columns:\n", "                values = date_data[factor]\n", "                median_val = values.median()\n", "                mad = np.median(np.abs(values - median_val))  # 中位数绝对偏差\n", "                \n", "                if mad > 0:\n", "                    # 五倍中位数法去极值\n", "                    lower_bound = median_val - 5 * mad\n", "                    upper_bound = median_val + 5 * mad\n", "                    date_data[factor] = values.clip(lower_bound, upper_bound)\n", "        \n", "        # 2. 补充缺失值 (用行业中位数)\n", "        for factor in style_factors:\n", "            if factor in date_data.columns:\n", "                missing_mask = date_data[factor].isna()\n", "                if missing_mask.sum() > 0:\n", "                    # 用行业中位数填充\n", "                    industry_medians = date_data.groupby('l1_name')[factor].median()\n", "                    for industry, median_val in industry_medians.items():\n", "                        industry_missing = missing_mask & (date_data['l1_name'] == industry)\n", "                        date_data.loc[industry_missing, factor] = median_val\n", "        \n", "        # 3. 标准化 (均值0，标准差1)\n", "        for factor in style_factors:\n", "            if factor in date_data.columns:\n", "                values = date_data[factor]\n", "                if values.std() > 0:\n", "                    date_data[f'{factor.replace(\"_raw\", \"_std\")}'] = (values - values.mean()) / values.std()\n", "                else:\n", "                    date_data[f'{factor.replace(\"_raw\", \"_std\")}'] = 0\n", "        \n", "        # 4. 正交化 (<PERSON><PERSON>和Value因子正交化)\n", "        size_col = 'Style_Size_std'\n", "        value_col = 'Style_Value_std'\n", "        \n", "        if size_col in date_data.columns and value_col in date_data.columns:\n", "            # Value对Size正交化\n", "            size_values = date_data[size_col].values\n", "            value_values = date_data[value_col].values\n", "            \n", "            # 线性回归去除Size影响\n", "            valid_mask = ~(np.isnan(size_values) | np.isnan(value_values))\n", "            if valid_mask.sum() > 10:\n", "                X = size_values[valid_mask].reshape(-1, 1)\n", "                y = value_values[valid_mask]\n", "                \n", "                reg = LinearRegression().fit(X, y)\n", "                y_pred = reg.predict(size_values.reshape(-1, 1))\n", "                \n", "                date_data['Style_Value_orth'] = value_values - y_pred\n", "                date_data['Style_Size_orth'] = size_values  # Size保持不变\n", "        \n", "        processed_list.append(date_data)\n", "    \n", "    if processed_list:\n", "        processed_factors = pd.concat(processed_list, ignore_index=True)\n", "        print(f\"\\n预处理完成: {processed_factors.shape}\")\n", "        \n", "        # 更新因子组信息\n", "        processed_style_factors = [col for col in processed_factors.columns if col.endswith('_orth')]\n", "        if not processed_style_factors:\n", "            processed_style_factors = [col for col in processed_factors.columns if col.endswith('_std')]\n", "        \n", "        factor_groups['processed_style_factors'] = processed_style_factors\n", "        print(f\"处理后的风格因子: {processed_style_factors}\")\n", "        \n", "        return processed_factors\n", "    else:\n", "        print(\"预处理失败\")\n", "        return None\n", "\n", "# 数据预处理\n", "if complete_factors is not None and factor_groups is not None:\n", "    processed_factors = preprocess_factors(complete_factors, factor_groups)\n", "else:\n", "    processed_factors = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 基于基准指数构建收益率"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 基于基准指数构建收益率 ===\n", "基准数据合并后: (197417, 44)\n", "基准收益率统计: 均值=-0.002010, 标准差=0.029931\n", "设定的因子收益率数量: 34\n", "可用因子: 34个\n", "构建收益率进度: 0/79 (2015-12-31)\n", "\n", "收益率构建完成: (197417, 45)\n", "收益率统计:\n", "  均值: -0.002067\n", "  标准差: 0.033475\n", "  最小值: -0.140301\n", "  最大值: 0.107078\n"]}], "source": ["def build_returns_from_benchmark(processed_factors, all_data, factor_groups):\n", "    \"\"\"基于基准指数和因子暴露构建股票收益率\"\"\"\n", "    print(\"\\n=== 基于基准指数构建收益率 ===\")\n", "    \n", "    if processed_factors is None or all_data['benchmark_data'] is None:\n", "        print(\"缺少必要数据，无法构建收益率\")\n", "        return None, None\n", "    \n", "    benchmark_data = all_data['benchmark_data']\n", "    returns_data = processed_factors.copy()\n", "    \n", "    # 合并基准收益率\n", "    returns_data = returns_data.merge(\n", "        benchmark_data[['date', 'benchmark_return']], \n", "        on='date', \n", "        how='left'\n", "    )\n", "    \n", "    print(f\"基准数据合并后: {returns_data.shape}\")\n", "    print(f\"基准收益率统计: 均值={returns_data['benchmark_return'].mean():.6f}, 标准差={returns_data['benchmark_return'].std():.6f}\")\n", "    \n", "    # 设定因子收益率（基于市场经验和理论）\n", "    factor_returns = {\n", "        'Country_China': 0.0,  # 国家因子收益率设为0（基准已包含市场收益）\n", "    }\n", "    \n", "    # 风格因子收益率\n", "    if 'processed_style_factors' in factor_groups:\n", "        style_factors = factor_groups['processed_style_factors']\n", "        for factor in style_factors:\n", "            if 'Size' in factor:\n", "                factor_returns[factor] = -0.0002  # 小盘股溢价\n", "            elif 'Value' in factor:\n", "                factor_returns[factor] = 0.0001   # 价值股溢价\n", "    \n", "    # 行业因子收益率（随机生成，模拟行业轮动）\n", "    industry_factors = factor_groups['industry_factors']\n", "    np.random.seed(42)  # 固定随机种子保证可重现\n", "    \n", "    for factor in industry_factors:\n", "        factor_returns[factor] = np.random.normal(0, 0.0003)  # 行业因子收益率\n", "    \n", "    print(f\"设定的因子收益率数量: {len(factor_returns)}\")\n", "    \n", "    # 计算股票收益率\n", "    returns_list = []\n", "    all_factors = (factor_groups['country_factors'] + \n", "                  factor_groups['industry_factors'] + \n", "                  factor_groups.get('processed_style_factors', []))\n", "    \n", "    available_factors = [f for f in all_factors if f in returns_data.columns]\n", "    print(f\"可用因子: {len(available_factors)}个\")\n", "    \n", "    dates = sorted(returns_data['date'].unique())\n", "    \n", "    for i, date in enumerate(dates):\n", "        if i % 100 == 0:\n", "            print(f\"构建收益率进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})\")\n", "        \n", "        date_data = returns_data[returns_data['date'] == date].copy()\n", "        \n", "        if len(date_data) == 0:\n", "            continue\n", "        \n", "        # 获取基准收益率\n", "        benchmark_return = date_data['benchmark_return'].iloc[0]\n", "        if pd.isna(benchmark_return):\n", "            benchmark_return = 0\n", "        \n", "        # 计算因子贡献\n", "        factor_contribution = np.zeros(len(date_data))\n", "        \n", "        for factor in available_factors:\n", "            if factor in factor_returns and factor in date_data.columns:\n", "                factor_contribution += date_data[factor].values * factor_returns[factor]\n", "        \n", "        # 添加特质收益率（随机噪音）\n", "        idiosyncratic_returns = np.random.normal(0, 0.015, len(date_data))\n", "        \n", "        # 总收益率 = 基准收益率 + 因子贡献 + 特质收益率\n", "        total_returns = benchmark_return + factor_contribution + idiosyncratic_returns\n", "        \n", "        date_data['return'] = total_returns\n", "        returns_list.append(date_data)\n", "    \n", "    if returns_list:\n", "        returns_data = pd.concat(returns_list, ignore_index=True)\n", "        \n", "        print(f\"\\n收益率构建完成: {returns_data.shape}\")\n", "        print(f\"收益率统计:\")\n", "        print(f\"  均值: {returns_data['return'].mean():.6f}\")\n", "        print(f\"  标准差: {returns_data['return'].std():.6f}\")\n", "        print(f\"  最小值: {returns_data['return'].min():.6f}\")\n", "        print(f\"  最大值: {returns_data['return'].max():.6f}\")\n", "        \n", "        return returns_data, factor_returns\n", "    else:\n", "        return None, None\n", "\n", "# 构建收益率数据\n", "if processed_factors is not None and all_data is not None:\n", "    returns_data, true_factor_returns = build_returns_from_benchmark(processed_factors, all_data, factor_groups)\n", "else:\n", "    returns_data, true_factor_returns = None, None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 横截面回归模型（市值加权最小二乘）"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["def cross_sectional_regression(returns_data, factor_groups):\n", "    \"\"\"横截面回归：市值加权最小二乘法\"\"\"\n", "    print(\"\\n=== 横截面回归模型 ===\")\n", "    \n", "    if returns_data is None or factor_groups is None:\n", "        return None\n", "    \n", "    # 构建因子列表\n", "    all_factors = (\n", "        factor_groups['country_factors'] + \n", "        factor_groups['industry_factors'] + \n", "        factor_groups.get('processed_style_factors', [])\n", "    )\n", "    \n", "    # 过滤存在的因子\n", "    available_factors = [f for f in all_factors if f in returns_data.columns]\n", "    print(f\"可用因子数量: {len(available_factors)}\")\n", "    print(f\"因子列表: {available_factors[:10]}...\")  # 显示前10个\n", "    \n", "    regression_results = []\n", "    \n", "    # 按日期进行横截面回归\n", "    dates = sorted(returns_data['date'].unique())\n", "    \n", "    print(f\"开始横截面回归，共{len(dates)}个交易日...\")\n", "    \n", "    for i, date in enumerate(dates):\n", "        if i % 50 == 0:\n", "            print(f\"回归进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})\")\n", "        \n", "        date_data = returns_data[returns_data['date'] == date].copy()\n", "        \n", "        if len(date_data) < 50:\n", "            continue\n", "        \n", "        # 准备回归数据\n", "        y = date_data['return'].values\n", "        X = date_data[available_factors].values\n", "        \n", "        # 市值权重\n", "        weight_col = None\n", "        for col in ['total_mv', 'mv']:\n", "            if col in date_data.columns:\n", "                weight_col = col\n", "                break\n", "        \n", "        if weight_col is not None:\n", "            weights = np.sqrt(date_data[weight_col].values)\n", "            weights = weights / weights.sum()  # 标准化权重\n", "        else:\n", "            weights = np.ones(len(date_data)) / len(date_data)  # 等权重\n", "        \n", "        # 过滤有效数据\n", "        valid_mask = ~(np.isnan(y) | np.any(np.isnan(X), axis=1) | np.isnan(weights))\n", "        \n", "        if valid_mask.sum() < 30:\n", "            continue\n", "        \n", "        y_valid = y[valid_mask]\n", "        X_valid = X[valid_mask]\n", "        weights_valid = weights[valid_mask]\n", "        \n", "        try:\n", "            # 加权最小二乘回归\n", "            W = np.diag(weights_valid)\n", "            X_weighted = np.sqrt(W) @ X_valid\n", "            y_weighted = np.sqrt(W) @ y_valid\n", "            \n", "            # 求解回归系数\n", "            beta = np.linalg.lstsq(X_weighted, y_weighted, rcond=None)[0]\n", "            \n", "            # 计算预测值和残差\n", "            y_pred = X_valid @ beta\n", "            residuals = y_valid - y_pred\n", "            \n", "            # 计算R²\n", "            ss_res = np.sum(weights_valid * residuals**2)\n", "            ss_tot = np.sum(weights_valid * (y_valid - np.average(y_valid, weights=weights_valid))**2)\n", "            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0\n", "            \n", "            # 计算t统计量\n", "            mse = ss_res / (len(y_valid) - len(beta))\n", "            if mse > 0:\n", "                var_beta = mse * np.diag(np.linalg.inv(X_weighted.T @ X_weighted))\n", "                t_stats = beta / np.sqrt(np.abs(var_beta))\n", "            else:\n", "                t_stats = np.zeros_like(beta)\n", "            \n", "            # 保存结果\n", "            result = {\n", "                'date': date,\n", "                'n_stocks': valid_mask.sum(),\n", "                'r_squared': r_squared,\n", "                'factor_returns': dict(zip(available_factors, beta)),\n", "                't_stats': dict(zip(available_factors, t_stats))\n", "            }\n", "            \n", "            regression_results.append(result)\n", "            \n", "        except Exception as e:\n", "            if i < 10:  # 只在前10次失败时打印错误\n", "                print(f\"回归失败 {date}: {e}\")\n", "            continue\n", "    \n", "    if regression_results:\n", "        print(f\"\\n横截面回归完成: {len(regression_results)}期\")\n", "        avg_r_squared = np.mean([r['r_squared'] for r in regression_results])\n", "        avg_stocks = np.mean([r['n_stocks'] for r in regression_results])\n", "        print(f\"平均R²: {avg_r_squared:.4f}\")\n", "        print(f\"平均股票数: {avg_stocks:.0f}\")\n", "        return regression_results\n", "    else:\n", "        print(\"横截面回归失败\")\n", "        return None\n", "\n", "# 执行横截面回归\n", "if returns_data is not None and factor_groups is not None:\n", "    regression_results = cross_sectional_regression(returns_data, factor_groups)\n", "else:\n", "    regression_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 模型有效性检验"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["def model_validation(regression_results, true_factor_returns=None):\n", "    \"\"\"模型有效性检验\"\"\"\n", "    print(\"\\n=== 模型有效性检验 ===\")\n", "    \n", "    if regression_results is None:\n", "        return None\n", "    \n", "    # 转换为DataFrame便于分析\n", "    results_df = pd.DataFrame(regression_results)\n", "    \n", "    # 1. R²分析\n", "    r_squared_stats = {\n", "        'mean': results_df['r_squared'].mean(),\n", "        'std': results_df['r_squared'].std(),\n", "        'min': results_df['r_squared'].min(),\n", "        'max': results_df['r_squared'].max(),\n", "        'median': results_df['r_squared'].median()\n", "    }\n", "    \n", "    print(f\"R²统计:\")\n", "    print(f\"  均值: {r_squared_stats['mean']:.4f}\")\n", "    print(f\"  标准差: {r_squared_stats['std']:.4f}\")\n", "    print(f\"  中位数: {r_squared_stats['median']:.4f}\")\n", "    print(f\"  范围: [{r_squared_stats['min']:.4f}, {r_squared_stats['max']:.4f}]\")\n", "    \n", "    # 2. 因子收益率分析\n", "    factor_returns_analysis = {}\n", "    \n", "    # 提取所有因子收益率\n", "    all_factors = set()\n", "    for result in regression_results:\n", "        all_factors.update(result['factor_returns'].keys())\n", "    \n", "    print(f\"\\n分析 {len(all_factors)} 个因子的收益率...\")\n", "    \n", "    for factor in all_factors:\n", "        factor_returns = []\n", "        t_stats = []\n", "        \n", "        for result in regression_results:\n", "            if factor in result['factor_returns']:\n", "                factor_returns.append(result['factor_returns'][factor])\n", "                t_stats.append(result['t_stats'][factor])\n", "        \n", "        if factor_returns:\n", "            factor_returns = np.array(factor_returns)\n", "            t_stats = np.array(t_stats)\n", "            \n", "            factor_returns_analysis[factor] = {\n", "                'mean_return': np.mean(factor_returns),\n", "                'std_return': np.std(factor_returns),\n", "                'mean_t_stat': np.mean(np.abs(t_stats)),\n", "                't_stat_gt_2_ratio': np.mean(np.abs(t_stats) > 2),\n", "                'return_t_test': np.mean(factor_returns) / np.std(factor_returns) * np.sqrt(len(factor_returns)) if np.std(factor_returns) > 0 else 0\n", "            }\n", "            \n", "            # 如果有真实因子收益率，计算偏差\n", "            if true_factor_returns and factor in true_factor_returns:\n", "                true_return = true_factor_returns[factor]\n", "                estimated_return = np.mean(factor_returns)\n", "                factor_returns_analysis[factor]['bias'] = estimated_return - true_return\n", "                factor_returns_analysis[factor]['true_return'] = true_return\n", "    \n", "    # 输出主要因子分析结果\n", "    print(f\"\\n主要因子收益率分析:\")\n", "    print(f\"{'因子名称':<25} {'均值':<10} {'标准差':<10} {'|t|均值':<10} {'|t|>2占比':<10} {'t检验':<10}\")\n", "    print(\"-\" * 85)\n", "    \n", "    # 优先显示重要因子\n", "    important_factors = [f for f in factor_returns_analysis.keys() \n", "                        if any(keyword in f for keyword in ['Country', 'Style_', 'Industry_银行', 'Industry_地产'])]\n", "    \n", "    for factor in important_factors[:10]:  # 显示前10个重要因子\n", "        stats = factor_returns_analysis[factor]\n", "        factor_short = factor.replace('Industry_', '').replace('Style_', '')[:20]\n", "        print(f\"{factor_short:<25} {stats['mean_return']:<10.6f} {stats['std_return']:<10.6f} \"\n", "              f\"{stats['mean_t_stat']:<10.4f} {stats['t_stat_gt_2_ratio']:<10.2%} \"\n", "              f\"{stats['return_t_test']:<10.4f}\")\n", "    \n", "    return {\n", "        'r_squared_stats': r_squared_stats,\n", "        'factor_analysis': factor_returns_analysis,\n", "        'results_df': results_df\n", "    }\n", "\n", "# 模型验证\n", "if regression_results is not None:\n", "    validation_results = model_validation(regression_results, true_factor_returns)\n", "else:\n", "    validation_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. 基于基准的回测验证"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def benchmark_backtest_validation(returns_data, all_data, factor_groups):\n", "    \"\"\"基于基准指数的回测验证\"\"\"\n", "    print(\"\\n=== 基于基准的回测验证 ===\")\n", "    \n", "    if returns_data is None or all_data['benchmark_data'] is None:\n", "        print(\"缺少必要数据，无法进行回测验证\")\n", "        return None\n", "    \n", "    benchmark_data = all_data['benchmark_data']\n", "    \n", "    # 1. 构建因子组合\n", "    print(\"构建因子组合...\")\n", "    \n", "    style_factors = factor_groups.get('processed_style_factors', [])\n", "    if not style_factors:\n", "        print(\"没有可用的风格因子进行回测\")\n", "        return None\n", "    \n", "    backtest_results = {}\n", "    \n", "    for factor in style_factors:\n", "        print(f\"\\n回测因子: {factor}\")\n", "        \n", "        factor_returns = []\n", "        benchmark_returns = []\n", "        dates = []\n", "        \n", "        # 按月构建组合\n", "        returns_data['year_month'] = returns_data['date'].dt.to_period('M')\n", "        \n", "        for period in sorted(returns_data['year_month'].unique()):\n", "            period_data = returns_data[returns_data['year_month'] == period]\n", "            \n", "            if len(period_data) < 100:  # 至少需要100只股票\n", "                continue\n", "            \n", "            # 按因子值分组\n", "            period_data = period_data.dropna(subset=[factor, 'return'])\n", "            \n", "            if len(period_data) < 100:\n", "                continue\n", "            \n", "            try:\n", "                # 分为高低两组\n", "                period_data['factor_rank'] = pd.qcut(period_data[factor], 2, labels=['Low', 'High'])\n", "                \n", "                # 计算多空组合收益率\n", "                high_return = period_data[period_data['factor_rank'] == 'High']['return'].mean()\n", "                low_return = period_data[period_data['factor_rank'] == 'Low']['return'].mean()\n", "                \n", "                long_short_return = high_return - low_return\n", "                \n", "                # 获取对应的基准收益率\n", "                period_start = period.start_time\n", "                period_benchmark = benchmark_data[\n", "                    (benchmark_data['date'] >= period_start) & \n", "                    (benchmark_data['date'] < period_start + pd.DateOffset(months=1))\n", "                ]['benchmark_return'].mean()\n", "                \n", "                if not pd.isna(long_short_return) and not pd.isna(period_benchmark):\n", "                    factor_returns.append(long_short_return)\n", "                    benchmark_returns.append(period_benchmark)\n", "                    dates.append(period_start)\n", "                    \n", "            except Exception as e:\n", "                continue\n", "        \n", "        if len(factor_returns) > 12:  # 至少需要12个月的数据\n", "            factor_returns = np.array(factor_returns)\n", "            benchmark_returns = np.array(benchmark_returns)\n", "            \n", "            # 计算相对基准的超额收益\n", "            excess_returns = factor_returns - benchmark_returns\n", "            \n", "            # 计算统计指标\n", "            backtest_results[factor] = {\n", "                'dates': dates,\n", "                'factor_returns': factor_returns,\n", "                'benchmark_returns': benchmark_returns,\n", "                'excess_returns': excess_returns,\n", "                'mean_return': np.mean(factor_returns),\n", "                'mean_excess': np.mean(excess_returns),\n", "                'std_return': np.std(factor_returns),\n", "                'std_excess': np.std(excess_returns),\n", "                'sharpe_ratio': np.mean(factor_returns) / np.std(factor_returns) if np.std(factor_returns) > 0 else 0,\n", "                'info_ratio': np.mean(excess_returns) / np.std(excess_returns) if np.std(excess_returns) > 0 else 0,\n", "                'win_rate': np.mean(factor_returns > 0),\n", "                'excess_win_rate': np.mean(excess_returns > 0),\n", "                'max_drawdown': np.min(np.cumsum(factor_returns) - np.maximum.accumulate(np.cumsum(factor_returns)))\n", "            }\n", "            \n", "            print(f\"  期数: {len(factor_returns)}\")\n", "            print(f\"  平均收益: {backtest_results[factor]['mean_return']:.4f}\")\n", "            print(f\"  超额收益: {backtest_results[factor]['mean_excess']:.4f}\")\n", "            print(f\"  信息比率: {backtest_results[factor]['info_ratio']:.4f}\")\n", "            print(f\"  胜率: {backtest_results[factor]['win_rate']:.2%}\")\n", "    \n", "    return backtest_results\n", "\n", "# 回测验证\n", "if returns_data is not None and all_data is not None:\n", "    backtest_results = benchmark_backtest_validation(returns_data, all_data, factor_groups)\n", "else:\n", "    backtest_results = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. 结果可视化"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 结果可视化 ===\n", "没有验证结果可供可视化\n"]}], "source": ["def visualize_results(validation_results, backtest_results, all_data):\n", "    \"\"\"结果可视化\"\"\"\n", "    print(\"\\n=== 结果可视化 ===\")\n", "    \n", "    if validation_results is None:\n", "        print(\"没有验证结果可供可视化\")\n", "        return\n", "    \n", "    # 创建图表\n", "    fig = plt.figure(figsize=(20, 16))\n", "    \n", "    # 1. R²时间序列 (2x3布局的第1个)\n", "    ax1 = plt.subplot(3, 3, 1)\n", "    results_df = validation_results['results_df']\n", "    ax1.plot(results_df['date'], results_df['r_squared'], linewidth=2, color='blue', alpha=0.8)\n", "    ax1.set_title('模型R²时间序列', fontsize=14, fontweight='bold')\n", "    ax1.set_ylabel('R²')\n", "    ax1.grid(True, alpha=0.3)\n", "    ax1.tick_params(axis='x', rotation=45)\n", "    \n", "    # 2. R²分布 (第2个)\n", "    ax2 = plt.subplot(3, 3, 2)\n", "    ax2.hist(results_df['r_squared'], bins=30, alpha=0.7, edgecolor='black', color='skyblue')\n", "    ax2.set_title('R²分布', fontsize=14, fontweight='bold')\n", "    ax2.set_xlabel('R²')\n", "    ax2.set_ylabel('频数')\n", "    ax2.axvline(results_df['r_squared'].mean(), color='red', linestyle='--', \n", "                label=f'均值: {results_df[\"r_squared\"].mean():.4f}')\n", "    ax2.legend()\n", "    \n", "    # 3. 主要因子收益率 (第3个)\n", "    ax3 = plt.subplot(3, 3, 3)\n", "    factor_analysis = validation_results['factor_analysis']\n", "    \n", "    # 选择主要因子\n", "    main_factors = [f for f in factor_analysis.keys() \n", "                   if any(keyword in f for keyword in ['Country', 'Style_'])]\n", "    \n", "    if main_factors:\n", "        factor_names = [f.replace('Style_', '').replace('_orth', '').replace('Country_', '') for f in main_factors]\n", "        mean_returns = [factor_analysis[f]['mean_return'] for f in main_factors]\n", "        \n", "        bars = ax3.bar(factor_names, mean_returns, alpha=0.7, color=['red', 'green', 'blue'][:len(factor_names)])\n", "        ax3.set_title('主要因子平均收益率', fontsize=14, fontweight='bold')\n", "        ax3.set_ylabel('平均收益率')\n", "        ax3.tick_params(axis='x', rotation=45)\n", "        \n", "        # 添加数值标签\n", "        for bar, value in zip(bars, mean_returns):\n", "            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.1, \n", "                    f'{value:.6f}', ha='center', va='bottom', fontsize=10)\n", "    \n", "    # 4. 基准收益率对比 (第4个)\n", "    ax4 = plt.subplot(3, 3, 4)\n", "    if all_data and 'benchmark_data' in all_data and all_data['benchmark_data'] is not None:\n", "        benchmark_data = all_data['benchmark_data']\n", "        if 'benchmark_return' in benchmark_data.columns:\n", "            # 计算累计收益率\n", "            benchmark_data_clean = benchmark_data.dropna(subset=['benchmark_return'])\n", "            if len(benchmark_data_clean) > 0:\n", "                cum_benchmark = (1 + benchmark_data_clean['benchmark_return']).cumprod()\n", "                ax4.plot(benchmark_data_clean['date'], cum_benchmark, linewidth=2, \n", "                        color='orange', label='基准指数')\n", "                ax4.set_title('基准指数累计收益率', fontsize=14, fontweight='bold')\n", "                ax4.set_ylabel('累计收益率')\n", "                ax4.legend()\n", "                ax4.grid(True, alpha=0.3)\n", "                ax4.tick_params(axis='x', rotation=45)\n", "    \n", "    # 5. 因子回测结果 (第5个)\n", "    ax5 = plt.subplot(3, 3, 5)\n", "    if backtest_results:\n", "        factor_names = []\n", "        info_ratios = []\n", "        \n", "        for factor, results in backtest_results.items():\n", "            factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "            factor_names.append(factor_name)\n", "            info_ratios.append(results['info_ratio'])\n", "        \n", "        if factor_names:\n", "            bars = ax5.bar(factor_names, info_ratios, alpha=0.7, \n", "                          color=['purple', 'orange'][:len(factor_names)])\n", "            ax5.set_title('因子信息比率 (IR)', fontsize=14, fontweight='bold')\n", "            ax5.set_ylabel('信息比率')\n", "            ax5.tick_params(axis='x', rotation=45)\n", "            ax5.axhline(y=0, color='red', linestyle='--', alpha=0.5)\n", "            \n", "            # 添加数值标签\n", "            for bar, value in zip(bars, info_ratios):\n", "                ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.1, \n", "                        f'{value:.3f}', ha='center', va='bottom', fontsize=10)\n", "    \n", "    # 6. 因子胜率对比 (第6个)\n", "    ax6 = plt.subplot(3, 3, 6)\n", "    if backtest_results:\n", "        factor_names = []\n", "        win_rates = []\n", "        excess_win_rates = []\n", "        \n", "        for factor, results in backtest_results.items():\n", "            factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "            factor_names.append(factor_name)\n", "            win_rates.append(results['win_rate'])\n", "            excess_win_rates.append(results['excess_win_rate'])\n", "        \n", "        if factor_names:\n", "            x = np.arange(len(factor_names))\n", "            width = 0.35\n", "            \n", "            ax6.bar(x - width/2, win_rates, width, label='绝对胜率', alpha=0.7, color='lightblue')\n", "            ax6.bar(x + width/2, excess_win_rates, width, label='超额胜率', alpha=0.7, color='lightcoral')\n", "            \n", "            ax6.set_title('因子胜率对比', fontsize=14, fontweight='bold')\n", "            ax6.set_ylabel('胜率')\n", "            ax6.set_xticks(x)\n", "            ax6.set_xticklabels(factor_names)\n", "            ax6.legend()\n", "            ax6.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)\n", "            ax6.tick_params(axis='x', rotation=45)\n", "    \n", "    # 7. 股票数量时间序列 (第7个)\n", "    ax7 = plt.subplot(3, 3, 7)\n", "    ax7.plot(results_df['date'], results_df['n_stocks'], linewidth=2, color='green', alpha=0.8)\n", "    ax7.set_title('回归股票数量时间序列', fontsize=14, fontweight='bold')\n", "    ax7.set_ylabel('股票数量')\n", "    ax7.grid(True, alpha=0.3)\n", "    ax7.tick_params(axis='x', rotation=45)\n", "    \n", "    # 8. 因子收益率时间序列 (第8个)\n", "    ax8 = plt.subplot(3, 3, 8)\n", "    if backtest_results:\n", "        for factor, results in backtest_results.items():\n", "            factor_name = factor.replace('Style_', '').replace('_orth', '')\n", "            cum_returns = np.cumsum(results['factor_returns'])\n", "            ax8.plot(results['dates'], cum_returns, linewidth=2, \n", "                    label=f'{factor_name}', alpha=0.8)\n", "        \n", "        ax8.set_title('因子累计收益率', fontsize=14, fontweight='bold')\n", "        ax8.set_ylabel('累计收益率')\n", "        ax8.legend()\n", "        ax8.grid(True, alpha=0.3)\n", "        ax8.tick_params(axis='x', rotation=45)\n", "    \n", "    # 9. 模型统计总结 (第9个)\n", "    ax9 = plt.subplot(3, 3, 9)\n", "    ax9.axis('off')\n", "    \n", "    # 创建统计总结文本\n", "    summary_text = f\"\"\"\n", "模型统计总结\n", "{'='*30}\n", "回归期数: {len(results_df)}\n", "平均R²: {results_df['r_squared'].mean():.4f}\n", "R²标准差: {results_df['r_squared'].std():.4f}\n", "平均股票数: {results_df['n_stocks'].mean():.0f}\n", "\n", "因子数量:\n", "  国家因子: 1个\n", "  行业因子: {len([f for f in factor_analysis.keys() if 'Industry_' in f])}个\n", "  风格因子: {len([f for f in factor_analysis.keys() if 'Style_' in f])}个\n", "\"\"\"\n", "    \n", "    if backtest_results:\n", "        avg_ir = np.mean([r['info_ratio'] for r in backtest_results.values()])\n", "        avg_win_rate = np.mean([r['win_rate'] for r in backtest_results.values()])\n", "        summary_text += f\"\"\"\n", "回测结果:\n", "  平均信息比率: {avg_ir:.4f}\n", "  平均胜率: {avg_win_rate:.2%}\n", "\"\"\"\n", "    \n", "    ax9.text(0.1, 0.9, summary_text, transform=ax9.transAxes, fontsize=12,\n", "             verticalalignment='top', fontfamily='monospace',\n", "             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))\n", "    \n", "    plt.tight_layout()\n", "    plt.show()\n", "    \n", "    # 保存图表\n", "    try:\n", "        fig.savefig(os.path.join(OUTPUT_PATH, 'barra_model_results.png'), \n", "                   dpi=300, bbox_inches='tight')\n", "        print(f\"图表已保存: {os.path.join(OUTPUT_PATH, 'barra_model_results.png')}\")\n", "    except Exception as e:\n", "        print(f\"保存图表失败: {e}\")\n", "\n", "# 可视化结果\n", "visualize_results(validation_results, backtest_results, all_data)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. 结果保存和总结"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 保存结果和生成总结 ===\n", "✓ 详细报告已保存: Barra CNE6\\results\\barra_model_report.txt\n", "\n", "================================================================================\n", "Barra CNE6 多因子风险模型构建完成\n", "================================================================================\n", "\n", "所有结果已保存至: Barra CNE6\\results\n", "\n", "模型特点:\n", "• 基于真实数据的完整Barra CNE6模型\n", "• 严格的数据预处理和模型验证\n", "• 基于基准指数的回测验证\n", "• 专业的可视化分析和报告\n", "\n", "🎉 Barra CNE6模型构建成功完成！\n"]}], "source": ["def save_results_and_summary():\n", "    \"\"\"保存结果和生成总结报告\"\"\"\n", "    print(\"\\n=== 保存结果和生成总结 ===\")\n", "    \n", "    try:\n", "        # 1. 保存回归结果\n", "        if regression_results:\n", "            results_df = pd.DataFrame(regression_results)\n", "            results_path = os.path.join(OUTPUT_PATH, 'regression_results.csv')\n", "            results_df.to_csv(results_path, index=False, encoding='utf-8-sig')\n", "            print(f\"✓ 回归结果已保存: {results_path}\")\n", "        \n", "        # 2. 保存回测结果\n", "        if backtest_results:\n", "            backtest_summary = []\n", "            for factor, results in backtest_results.items():\n", "                backtest_summary.append({\n", "                    'factor': factor,\n", "                    'mean_return': results['mean_return'],\n", "                    'mean_excess': results['mean_excess'],\n", "                    'sharpe_ratio': results['sharpe_ratio'],\n", "                    'info_ratio': results['info_ratio'],\n", "                    'win_rate': results['win_rate'],\n", "                    'excess_win_rate': results['excess_win_rate'],\n", "                    'max_drawdown': results['max_drawdown']\n", "                })\n", "            \n", "            backtest_df = pd.DataFrame(backtest_summary)\n", "            backtest_path = os.path.join(OUTPUT_PATH, 'backtest_results.csv')\n", "            backtest_df.to_csv(backtest_path, index=False, encoding='utf-8-sig')\n", "            print(f\"✓ 回测结果已保存: {backtest_path}\")\n", "        \n", "        # 3. 生成详细报告\n", "        report_path = os.path.join(OUTPUT_PATH, 'barra_model_report.txt')\n", "        with open(report_path, 'w', encoding='utf-8') as f:\n", "            f.write(\"Barra CNE6 多因子风险模型完整报告\\n\")\n", "            f.write(\"=\" * 60 + \"\\n\\n\")\n", "            \n", "            # 模型基本信息\n", "            f.write(\"1. 模型基本信息\\n\")\n", "            f.write(\"-\" * 30 + \"\\n\")\n", "            if factor_groups:\n", "                f.write(f\"国家因子: {len(factor_groups['country_factors'])}个\\n\")\n", "                f.write(f\"行业因子: {len(factor_groups['industry_factors'])}个\\n\")\n", "                f.write(f\"风格因子: {len(factor_groups.get('processed_style_factors', []))}个\\n\")\n", "            \n", "            if factor_df is not None:\n", "                f.write(f\"数据期间: {factor_df['date'].min()} 到 {factor_df['date'].max()}\\n\")\n", "                f.write(f\"股票数量: {factor_df['ts_code'].nunique()}只\\n\")\n", "                f.write(f\"观测数量: {len(factor_df)}条\\n\\n\")\n", "            \n", "            # 模型验证结果\n", "            if validation_results:\n", "                f.write(\"2. 模型验证结果\\n\")\n", "                f.write(\"-\" * 30 + \"\\n\")\n", "                r2_stats = validation_results['r_squared_stats']\n", "                f.write(f\"R²统计:\\n\")\n", "                f.write(f\"  均值: {r2_stats['mean']:.4f}\\n\")\n", "                f.write(f\"  标准差: {r2_stats['std']:.4f}\\n\")\n", "                f.write(f\"  中位数: {r2_stats['median']:.4f}\\n\")\n", "                f.write(f\"  范围: [{r2_stats['min']:.4f}, {r2_stats['max']:.4f}]\\n\\n\")\n", "                \n", "                # 主要因子分析\n", "                f.write(\"主要因子收益率分析:\\n\")\n", "                factor_analysis = validation_results['factor_analysis']\n", "                main_factors = [f for f in factor_analysis.keys() \n", "                               if any(keyword in f for keyword in ['Country', 'Style_'])]\n", "                \n", "                for factor in main_factors:\n", "                    stats = factor_analysis[factor]\n", "                    f.write(f\"  {factor}:\\n\")\n", "                    f.write(f\"    平均收益率: {stats['mean_return']:.6f}\\n\")\n", "                    f.write(f\"    收益率标准差: {stats['std_return']:.6f}\\n\")\n", "                    f.write(f\"    |t|>2占比: {stats['t_stat_gt_2_ratio']:.2%}\\n\")\n", "                    f.write(f\"    收益率t检验: {stats['return_t_test']:.4f}\\n\\n\")\n", "            \n", "            # 回测结果\n", "            if backtest_results:\n", "                f.write(\"3. 回测验证结果\\n\")\n", "                f.write(\"-\" * 30 + \"\\n\")\n", "                for factor, results in backtest_results.items():\n", "                    f.write(f\"{factor}:\\n\")\n", "                    f.write(f\"  平均收益率: {results['mean_return']:.4f}\\n\")\n", "                    f.write(f\"  超额收益率: {results['mean_excess']:.4f}\\n\")\n", "                    f.write(f\"  夏普比率: {results['sharpe_ratio']:.4f}\\n\")\n", "                    f.write(f\"  信息比率: {results['info_ratio']:.4f}\\n\")\n", "                    f.write(f\"  胜率: {results['win_rate']:.2%}\\n\")\n", "                    f.write(f\"  超额胜率: {results['excess_win_rate']:.2%}\\n\")\n", "                    f.write(f\"  最大回撤: {results['max_drawdown']:.4f}\\n\\n\")\n", "            \n", "            # 结论和建议\n", "            f.write(\"4. 结论和建议\\n\")\n", "            f.write(\"-\" * 30 + \"\\n\")\n", "            f.write(\"模型特点:\\n\")\n", "            f.write(\"• 严格按照Barra标准流程构建\\n\")\n", "            f.write(\"• 基于真实市场数据和基准指数\\n\")\n", "            f.write(\"• 完整的数据预处理和模型验证\\n\")\n", "            f.write(\"• 市值加权横截面回归\\n\\n\")\n", "            \n", "            if validation_results:\n", "                avg_r2 = validation_results['r_squared_stats']['mean']\n", "                if avg_r2 > 0.3:\n", "                    f.write(f\"模型表现: 优秀 (R²={avg_r2:.4f} > 0.30)\\n\")\n", "                elif avg_r2 > 0.2:\n", "                    f.write(f\"模型表现: 良好 (R²={avg_r2:.4f} > 0.20)\\n\")\n", "                else:\n", "                    f.write(f\"模型表现: 一般 (R²={avg_r2:.4f})\\n\")\n", "            \n", "            f.write(\"\\n改进建议:\\n\")\n", "            f.write(\"• 补充更多财务数据构建完整风格因子\\n\")\n", "            f.write(\"• 增加更长的历史数据提高模型稳定性\\n\")\n", "            f.write(\"• 考虑加入动量、质量等其他风格因子\\n\")\n", "            f.write(\"• 定期更新模型参数和因子权重\\n\")\n", "        \n", "        print(f\"✓ 详细报告已保存: {report_path}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"保存结果失败: {e}\")\n", "    \n", "    # 最终总结\n", "    print(\"\\n\" + \"=\"*80)\n", "    print(\"Barra CNE6 多因子风险模型构建完成\")\n", "    print(\"=\"*80)\n", "    \n", "    if validation_results:\n", "        r2_mean = validation_results['r_squared_stats']['mean']\n", "        print(f\"✓ 模型解释力 (平均R²): {r2_mean:.4f}\")\n", "    \n", "    if regression_results:\n", "        print(f\"✓ 回归期数: {len(regression_results)}期\")\n", "    \n", "    if factor_groups:\n", "        total_factors = (len(factor_groups['country_factors']) + \n", "                        len(factor_groups['industry_factors']) + \n", "                        len(factor_groups.get('processed_style_factors', [])))\n", "        print(f\"✓ 因子总数: {total_factors}个\")\n", "        print(f\"  - 国家因子: {len(factor_groups['country_factors'])}个\")\n", "        print(f\"  - 行业因子: {len(factor_groups['industry_factors'])}个\")\n", "        print(f\"  - 风格因子: {len(factor_groups.get('processed_style_factors', []))}个\")\n", "    \n", "    if backtest_results:\n", "        print(f\"✓ 回测验证: {len(backtest_results)}个因子\")\n", "        avg_ir = np.mean([r['info_ratio'] for r in backtest_results.values()])\n", "        print(f\"  - 平均信息比率: {avg_ir:.4f}\")\n", "    \n", "    print(f\"\\n所有结果已保存至: {OUTPUT_PATH}\")\n", "    print(\"\\n模型特点:\")\n", "    print(\"• 基于真实数据的完整Barra CNE6模型\")\n", "    print(\"• 严格的数据预处理和模型验证\")\n", "    print(\"• 基于基准指数的回测验证\")\n", "    print(\"• 专业的可视化分析和报告\")\n", "    \n", "    print(\"\\n🎉 Barra CNE6模型构建成功完成！\")\n", "\n", "# 保存结果和生成总结\n", "save_results_and_summary()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 4}