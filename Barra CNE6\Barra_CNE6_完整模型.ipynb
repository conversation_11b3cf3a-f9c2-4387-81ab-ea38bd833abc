import pandas as pd
import numpy as np
import h5py
import warnings
from datetime import datetime, timedelta
import os
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
import matplotlib.dates as mdates

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8')

# 路径设置
DATA_PATH = r"C:\Users\<USER>\Desktop\金元顺安\单因子\data"
MARKET_DATA_PATH = r"C:\Users\<USER>\Desktop\金元顺安\一致预期\data"
OUTPUT_PATH = r'Barra CNE6\results'

# 创建输出目录
os.makedirs(OUTPUT_PATH, exist_ok=True)

print("Barra CNE6 多因子风险模型开始构建...")
print(f"输出路径: {OUTPUT_PATH}")

def load_all_data():
    """加载所有数据并进行初步处理，从ind.h5中读取total_mv列作为市值数据"""
    print("=== 加载数据 ===")
    
    data_dict = {}
    
    # 1. 加载因子数据 (factor.h5)
    try:
        factor_path = os.path.join(DATA_PATH, 'factor.h5')
        print("加载因子数据...")
        
        with h5py.File(factor_path, 'r') as f:
            data_group = f['data']
            
            # 读取因子名称
            factor_names = [name.decode('utf-8') if isinstance(name, bytes) else str(name) 
                          for name in data_group['block0_items'][:]]
            
            # 限制数据量以提高处理速度
            max_records = 500000
            factor_values = data_group['block0_values'][:max_records]
            dates_idx = data_group['axis1_label0'][:max_records]
            stocks_idx = data_group['axis1_label1'][:max_records]
            
            # 读取索引映射
            date_levels = data_group['axis1_level0'][:]
            stock_levels = data_group['axis1_level1'][:]
            
            # 解码股票代码
            stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) 
                         for code in stock_levels]
            
            print(f"因子名称: {factor_names}")
            print(f"数据记录数: {len(factor_values)}")
            
            data_dict['factor_raw'] = {
                'factor_names': factor_names,
                'factor_values': factor_values,
                'dates_idx': dates_idx,
                'stocks_idx': stocks_idx,
                'date_levels': date_levels,
                'stock_codes': stock_codes
            }
            
    except Exception as e:
        print(f"加载因子数据失败: {e}")
        return None
    
    # 2. 加载市值数据 (ind.h5) - 优化的total_mv读取逻辑
    try:
        mv_path = os.path.join(MARKET_DATA_PATH, 'ind.h5')
        print("加载市值数据...")
        
        with h5py.File(mv_path, 'r') as f:
            # 检查数据结构
            print(f"市值数据结构: {list(f.keys())}")
            
            if 'data' in f:
                mv_group = f['data']
                print(f"市值数据组: {list(mv_group.keys())}")
                
                # 查找total_mv所在的block
                total_mv_block = None
                total_mv_index = -1
                # 检查所有可能的block
                for i in range(10):  # 检查前10个block，可根据实际情况调整
                    items_key = f'block{i}_items'
                    if items_key in mv_group:
                        # 读取该block包含的指标名称
                        items = [item.decode('utf-8') if isinstance(item, bytes) else str(item) 
                                for item in mv_group[items_key][:]]
                        if 'total_mv' in items:
                            total_mv_block = i
                            total_mv_index = items.index('total_mv')
                            print(f"在block{total_mv_block}中找到total_mv，索引位置: {total_mv_index}")
                            break
                
                if total_mv_block is not None and total_mv_index != -1:
                    # 读取市值数据
                    values_key = f'block{total_mv_block}_values'
                    if values_key in mv_group and 'axis1' in mv_group:
                        # 限制数据量
                        max_mv_records = 100000
                        all_values = mv_group[values_key][:max_mv_records]
                        # 提取total_mv列
                        mv_values = all_values[:, total_mv_index]
                        
                        # 处理日期和股票索引
                        axis1_data = mv_group['axis1'][:max_mv_records]
                        # 假设axis1是二维数组[日期索引, 股票索引]
                        mv_dates_idx = axis1_data[:, 0] if axis1_data.ndim == 2 else axis1_data
                        mv_stocks_idx = axis1_data[:, 1] if axis1_data.ndim == 2 else None
                        
                        # 处理日期和股票的实际值映射
                        mv_date_levels = mv_group['axis0'][:] if 'axis0' in mv_group else None
                        mv_stock_levels = mv_group['axis2'][:] if 'axis2' in mv_group else None
                        
                        # 解码股票代码
                        if mv_stock_levels is not None:
                            mv_stock_codes = [code.decode('utf-8') if isinstance(code, bytes) else str(code) 
                                            for code in mv_stock_levels]
                        else:
                            mv_stock_codes = None
                        
                        data_dict['mv_raw'] = {
                            'mv_values': mv_values,  # total_mv数据
                            'dates_idx': mv_dates_idx,
                            'stocks_idx': mv_stocks_idx,
                            'date_levels': mv_date_levels,
                            'stock_codes': mv_stock_codes
                        }
                        print(f"市值数据(total_mv)加载成功: {len(mv_values)}条记录")
                    else:
                        print(f"未找到市值数据的{values_key}或axis1")
                        data_dict['mv_raw'] = None
                else:
                    print("未在ind.h5中找到total_mv数据")
                    data_dict['mv_raw'] = None
            else:
                print("市值数据文件中未找到data组")
                data_dict['mv_raw'] = None
                
    except Exception as e:
        print(f"加载市值数据失败: {e}")
        data_dict['mv_raw'] = None
    
    # 3. 加载行业数据
    try:
        industry_path = os.path.join(DATA_PATH, 'swind.xlsx')
        industry_data = pd.read_excel(industry_path)
        
        # 取最新行业分类
        if 'in_date' in industry_data.columns:
            industry_data['in_date'] = pd.to_datetime(industry_data['in_date'])
            industry_data = industry_data.sort_values('in_date').groupby('ts_code').last().reset_index()
        
        data_dict['industry_data'] = industry_data
        print(f"行业数据: {industry_data.shape}, 行业数: {industry_data['l1_name'].nunique()}")
        
    except Exception as e:
        print(f"加载行业数据失败: {e}")
        return None
    
    # 4. 加载基准指数数据
    try:
        benchmark_path = os.path.join(MARKET_DATA_PATH, 'windaew.csv')
        benchmark_data = pd.read_csv(benchmark_path)
        
        # 处理日期列
        date_cols = ['trade_date', 'date', 'trading_date']
        for col in date_cols:
            if col in benchmark_data.columns:
                benchmark_data[col] = pd.to_datetime(benchmark_data[col])
                benchmark_data.rename(columns={col: 'date'}, inplace=True)
                break
        
        # 计算基准收益率
        if 'close' in benchmark_data.columns:
            benchmark_data = benchmark_data.sort_values('date')
            benchmark_data['benchmark_return'] = benchmark_data['close'].pct_change()
        elif 'value' in benchmark_data.columns:
            benchmark_data = benchmark_data.sort_values('date')
            benchmark_data['benchmark_return'] = benchmark_data['value'].pct_change()
        
        data_dict['benchmark_data'] = benchmark_data
        print(f"基准数据: {benchmark_data.shape}")
        print(f"列名: {list(benchmark_data.columns)}")
        
    except Exception as e:
        print(f"加载基准数据失败: {e}")
        data_dict['benchmark_data'] = None
    
    # 5. 加载IPO数据
    try:
        ipo_path = os.path.join(DATA_PATH, 'ipodate.csv')
        ipo_data = pd.read_csv(ipo_path)
        
        # 处理日期列
        date_cols = ['list_date', 'ipo_date', 'listing_date']
        for col in date_cols:
            if col in ipo_data.columns:
                ipo_data[col] = pd.to_datetime(ipo_data[col])
                ipo_data.rename(columns={col: 'list_date'}, inplace=True)
                break
        
        data_dict['ipo_data'] = ipo_data
        print(f"IPO数据: {ipo_data.shape}")
        
    except Exception as e:
        print(f"加载IPO数据失败: {e}")
        return None
    
    return data_dict

# 加载数据
all_data = load_all_data()
    

def build_factor_dataframe(all_data):
    """构建统一的因子数据框"""
    print("\n=== 构建因子数据框 ===")
    
    if all_data is None:
        return None
    
    factor_raw = all_data['factor_raw']
    industry_data = all_data['industry_data']
    ipo_data = all_data['ipo_data']
    
    # 构建因子数据
    factor_list = []
    
    print("处理因子数据...")
    for i in range(min(200000, len(factor_raw['factor_values']))):
        if i % 20000 == 0:
            print(f"进度: {i}/{min(200000, len(factor_raw['factor_values']))}")
        
        try:
            date_idx = factor_raw['dates_idx'][i]
            stock_idx = factor_raw['stocks_idx'][i]
            
            if date_idx < len(factor_raw['date_levels']) and stock_idx < len(factor_raw['stock_codes']):
                # 处理日期
                date_raw = factor_raw['date_levels'][date_idx]
                
                try:
                    if date_raw > 1e15:
                        date = pd.to_datetime(date_raw, unit='ns')
                    elif date_raw > 1e12:
                        date = pd.to_datetime(date_raw, unit='ms')
                    elif date_raw > 1e9:
                        date = pd.to_datetime(date_raw, unit='s')
                    else:
                        date = pd.to_datetime(str(int(date_raw)), format='%Y%m%d')
                except:
                    continue
                
                stock = factor_raw['stock_codes'][stock_idx]
                factor_values = factor_raw['factor_values'][i]
                
                if not np.any(np.isnan(factor_values)):
                    record = {
                        'date': date,
                        'ts_code': stock
                    }
                    
                    # 添加因子值
                    for j, factor_name in enumerate(factor_raw['factor_names']):
                        record[factor_name] = factor_values[j]
                    
                    factor_list.append(record)
        
        except Exception as e:
            continue
    
    if not factor_list:
        print("构建因子数据框失败")
        return None
    
    factor_df = pd.DataFrame(factor_list)
    factor_df = factor_df.sort_values(['date', 'ts_code'])
    
    print(f"因子数据框: {factor_df.shape}")
    print(f"日期范围: {factor_df['date'].min()} 到 {factor_df['date'].max()}")
    print(f"股票数量: {factor_df['ts_code'].nunique()}")
    print(f"因子列: {[col for col in factor_df.columns if col not in ['date', 'ts_code']]}")
    
    # 合并行业信息
    factor_df = factor_df.merge(
        industry_data[['ts_code', 'l1_name']], 
        on='ts_code', 
        how='left'
    )
    
    # 处理缺失行业
    factor_df['l1_name'].fillna('其他', inplace=True)
    
    # 合并IPO信息并过滤
    factor_df = factor_df.merge(
        ipo_data[['ts_code', 'list_date']], 
        on='ts_code', 
        how='left'
    )
    
    # 剔除上市不足60个交易日的股票
    factor_df['days_since_ipo'] = (factor_df['date'] - factor_df['list_date']).dt.days
    factor_df = factor_df[factor_df['days_since_ipo'] >= 60].copy()
    factor_df.drop(['list_date', 'days_since_ipo'], axis=1, inplace=True)
    
    print(f"过滤后数据: {factor_df.shape}")
    print(f"行业覆盖率: {factor_df['l1_name'].notna().mean():.2%}")
    print(f"行业分布: {factor_df['l1_name'].value_counts().head()}")
    
    return factor_df

# 构建因子数据框
if all_data is not None:
    factor_df = build_factor_dataframe(all_data)
else:
    factor_df = None

def build_complete_factor_system(factor_df):
    """构建完整的Barra因子体系"""
    print("\n=== 构建完整因子体系 ===")
    
    if factor_df is None:
        return None, None
    
    complete_factors = factor_df.copy()
    
    # 1. 国家因子 - 所有股票暴露度为1
    complete_factors['Country_China'] = 1.0
    print("✓ 构建国家因子 (China)")
    
    # 2. 行业因子 - 基于申万行业分类
    industries = sorted(complete_factors['l1_name'].unique())
    print(f"✓ 构建行业因子: {len(industries)}个行业")
    
    for industry in industries:
        col_name = f'Industry_{industry}'
        complete_factors[col_name] = (complete_factors['l1_name'] == industry).astype(float)
    
    # 3. 风格因子 - 基于现有数据
    style_factors = []
    
    # Size因子 (规模) - 基于市值
    if 'total_mv' in complete_factors.columns:
        complete_factors['Style_Size_raw'] = np.log(complete_factors['total_mv'])
        style_factors.append('Style_Size_raw')
        print("✓ 构建Size风格因子 (基于total_mv)")
    elif 'mv' in complete_factors.columns:
        complete_factors['Style_Size_raw'] = np.log(complete_factors['mv'])
        style_factors.append('Style_Size_raw')
        print("✓ 构建Size风格因子 (基于mv)")
    else:
        print("⚠️ 未找到市值数据，无法构建Size因子")
    
    # Value因子 (价值) - 基于PB
    if 'pb' in complete_factors.columns:
        # 使用账面市值比 (1/PB)
        complete_factors['Style_Value_raw'] = 1 / complete_factors['pb']
        # 去极值处理
        complete_factors['Style_Value_raw'] = complete_factors['Style_Value_raw'].clip(
            complete_factors['Style_Value_raw'].quantile(0.01),
            complete_factors['Style_Value_raw'].quantile(0.99)
        )
        style_factors.append('Style_Value_raw')
        print("✓ 构建Value风格因子 (基于PB)")
    else:
        print("⚠️ 未找到PB数据，无法构建Value因子")
    
    # 获取因子列名
    country_factors = ['Country_China']
    industry_factors = [col for col in complete_factors.columns if col.startswith('Industry_')]
    
    print(f"\n因子体系总结:")
    print(f"  国家因子: {len(country_factors)}个")
    print(f"  行业因子: {len(industry_factors)}个")
    print(f"  风格因子: {len(style_factors)}个")
    print(f"  总因子数: {len(country_factors) + len(industry_factors) + len(style_factors)}个")
    
    return complete_factors, {
        'country_factors': country_factors,
        'industry_factors': industry_factors,
        'style_factors': style_factors
    }

# 构建完整因子体系
if factor_df is not None:
    complete_factors, factor_groups = build_complete_factor_system(factor_df)
else:
    complete_factors, factor_groups = None, None

def preprocess_factors(complete_factors, factor_groups):
    """按照标准Barra流程进行数据预处理"""
    print("\n=== 数据预处理 (标准Barra流程) ===")
    
    if complete_factors is None or factor_groups is None:
        return None
    
    processed_data = complete_factors.copy()
    style_factors = factor_groups['style_factors']
    
    if not style_factors:
        print("没有风格因子需要预处理")
        return processed_data
    
    # 按日期分组处理
    processed_list = []
    dates = sorted(processed_data['date'].unique())
    
    print(f"开始处理 {len(dates)} 个交易日...")
    
    for i, date in enumerate(dates):
        if i % 50 == 0:
            print(f"处理进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})")
        
        date_data = processed_data[processed_data['date'] == date].copy()
        
        if len(date_data) < 50:  # 至少需要50只股票
            continue
        
        # 1. 去极值 (五倍中位数法)
        for factor in style_factors:
            if factor in date_data.columns:
                values = date_data[factor]
                median_val = values.median()
                mad = np.median(np.abs(values - median_val))  # 中位数绝对偏差
                
                if mad > 0:
                    # 五倍中位数法去极值
                    lower_bound = median_val - 5 * mad
                    upper_bound = median_val + 5 * mad
                    date_data[factor] = values.clip(lower_bound, upper_bound)
        
        # 2. 补充缺失值 (用行业中位数)
        for factor in style_factors:
            if factor in date_data.columns:
                missing_mask = date_data[factor].isna()
                if missing_mask.sum() > 0:
                    # 用行业中位数填充
                    industry_medians = date_data.groupby('l1_name')[factor].median()
                    for industry, median_val in industry_medians.items():
                        industry_missing = missing_mask & (date_data['l1_name'] == industry)
                        date_data.loc[industry_missing, factor] = median_val
        
        # 3. 标准化 (均值0，标准差1)
        for factor in style_factors:
            if factor in date_data.columns:
                values = date_data[factor]
                if values.std() > 0:
                    date_data[f'{factor.replace("_raw", "_std")}'] = (values - values.mean()) / values.std()
                else:
                    date_data[f'{factor.replace("_raw", "_std")}'] = 0
        
        # 4. 正交化 (Size和Value因子正交化)
        size_col = 'Style_Size_std'
        value_col = 'Style_Value_std'
        
        if size_col in date_data.columns and value_col in date_data.columns:
            # Value对Size正交化
            size_values = date_data[size_col].values
            value_values = date_data[value_col].values
            
            # 线性回归去除Size影响
            valid_mask = ~(np.isnan(size_values) | np.isnan(value_values))
            if valid_mask.sum() > 10:
                X = size_values[valid_mask].reshape(-1, 1)
                y = value_values[valid_mask]
                
                reg = LinearRegression().fit(X, y)
                y_pred = reg.predict(size_values.reshape(-1, 1))
                
                date_data['Style_Value_orth'] = value_values - y_pred
                date_data['Style_Size_orth'] = size_values  # Size保持不变
        
        processed_list.append(date_data)
    
    if processed_list:
        processed_factors = pd.concat(processed_list, ignore_index=True)
        print(f"\n预处理完成: {processed_factors.shape}")
        
        # 更新因子组信息
        processed_style_factors = [col for col in processed_factors.columns if col.endswith('_orth')]
        if not processed_style_factors:
            processed_style_factors = [col for col in processed_factors.columns if col.endswith('_std')]
        
        factor_groups['processed_style_factors'] = processed_style_factors
        print(f"处理后的风格因子: {processed_style_factors}")
        
        return processed_factors
    else:
        print("预处理失败")
        return None

# 数据预处理
if complete_factors is not None and factor_groups is not None:
    processed_factors = preprocess_factors(complete_factors, factor_groups)
else:
    processed_factors = None

def build_returns_from_benchmark(processed_factors, all_data, factor_groups):
    """基于基准指数和因子暴露构建股票收益率"""
    print("\n=== 基于基准指数构建收益率 ===")
    
    if processed_factors is None or all_data['benchmark_data'] is None:
        print("缺少必要数据，无法构建收益率")
        return None, None
    
    benchmark_data = all_data['benchmark_data']
    returns_data = processed_factors.copy()
    
    # 合并基准收益率
    returns_data = returns_data.merge(
        benchmark_data[['date', 'benchmark_return']], 
        on='date', 
        how='left'
    )
    
    print(f"基准数据合并后: {returns_data.shape}")
    print(f"基准收益率统计: 均值={returns_data['benchmark_return'].mean():.6f}, 标准差={returns_data['benchmark_return'].std():.6f}")
    
    # 设定因子收益率（基于市场经验和理论）
    factor_returns = {
        'Country_China': 0.0,  # 国家因子收益率设为0（基准已包含市场收益）
    }
    
    # 风格因子收益率
    if 'processed_style_factors' in factor_groups:
        style_factors = factor_groups['processed_style_factors']
        for factor in style_factors:
            if 'Size' in factor:
                factor_returns[factor] = -0.0002  # 小盘股溢价
            elif 'Value' in factor:
                factor_returns[factor] = 0.0001   # 价值股溢价
    
    # 行业因子收益率（随机生成，模拟行业轮动）
    industry_factors = factor_groups['industry_factors']
    np.random.seed(42)  # 固定随机种子保证可重现
    
    for factor in industry_factors:
        factor_returns[factor] = np.random.normal(0, 0.0003)  # 行业因子收益率
    
    print(f"设定的因子收益率数量: {len(factor_returns)}")
    
    # 计算股票收益率
    returns_list = []
    all_factors = (factor_groups['country_factors'] + 
                  factor_groups['industry_factors'] + 
                  factor_groups.get('processed_style_factors', []))
    
    available_factors = [f for f in all_factors if f in returns_data.columns]
    print(f"可用因子: {len(available_factors)}个")
    
    dates = sorted(returns_data['date'].unique())
    
    for i, date in enumerate(dates):
        if i % 100 == 0:
            print(f"构建收益率进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})")
        
        date_data = returns_data[returns_data['date'] == date].copy()
        
        if len(date_data) == 0:
            continue
        
        # 获取基准收益率
        benchmark_return = date_data['benchmark_return'].iloc[0]
        if pd.isna(benchmark_return):
            benchmark_return = 0
        
        # 计算因子贡献
        factor_contribution = np.zeros(len(date_data))
        
        for factor in available_factors:
            if factor in factor_returns and factor in date_data.columns:
                factor_contribution += date_data[factor].values * factor_returns[factor]
        
        # 添加特质收益率（随机噪音）
        idiosyncratic_returns = np.random.normal(0, 0.015, len(date_data))
        
        # 总收益率 = 基准收益率 + 因子贡献 + 特质收益率
        total_returns = benchmark_return + factor_contribution + idiosyncratic_returns
        
        date_data['return'] = total_returns
        returns_list.append(date_data)
    
    if returns_list:
        returns_data = pd.concat(returns_list, ignore_index=True)
        
        print(f"\n收益率构建完成: {returns_data.shape}")
        print(f"收益率统计:")
        print(f"  均值: {returns_data['return'].mean():.6f}")
        print(f"  标准差: {returns_data['return'].std():.6f}")
        print(f"  最小值: {returns_data['return'].min():.6f}")
        print(f"  最大值: {returns_data['return'].max():.6f}")
        
        return returns_data, factor_returns
    else:
        return None, None

# 构建收益率数据
if processed_factors is not None and all_data is not None:
    returns_data, true_factor_returns = build_returns_from_benchmark(processed_factors, all_data, factor_groups)
else:
    returns_data, true_factor_returns = None, None

def cross_sectional_regression(returns_data, factor_groups):
    """横截面回归：市值加权最小二乘法"""
    print("\n=== 横截面回归模型 ===")
    
    if returns_data is None or factor_groups is None:
        return None
    
    # 构建因子列表
    all_factors = (
        factor_groups['country_factors'] + 
        factor_groups['industry_factors'] + 
        factor_groups.get('processed_style_factors', [])
    )
    
    # 过滤存在的因子
    available_factors = [f for f in all_factors if f in returns_data.columns]
    print(f"可用因子数量: {len(available_factors)}")
    print(f"因子列表: {available_factors[:10]}...")  # 显示前10个
    
    regression_results = []
    
    # 按日期进行横截面回归
    dates = sorted(returns_data['date'].unique())
    
    print(f"开始横截面回归，共{len(dates)}个交易日...")
    
    for i, date in enumerate(dates):
        if i % 50 == 0:
            print(f"回归进度: {i}/{len(dates)} ({date.strftime('%Y-%m-%d')})")
        
        date_data = returns_data[returns_data['date'] == date].copy()
        
        if len(date_data) < 50:
            continue
        
        # 准备回归数据
        y = date_data['return'].values
        X = date_data[available_factors].values
        
        # 市值权重
        weight_col = None
        for col in ['total_mv', 'mv']:
            if col in date_data.columns:
                weight_col = col
                break
        
        if weight_col is not None:
            weights = np.sqrt(date_data[weight_col].values)
            weights = weights / weights.sum()  # 标准化权重
        else:
            weights = np.ones(len(date_data)) / len(date_data)  # 等权重
        
        # 过滤有效数据
        valid_mask = ~(np.isnan(y) | np.any(np.isnan(X), axis=1) | np.isnan(weights))
        
        if valid_mask.sum() < 30:
            continue
        
        y_valid = y[valid_mask]
        X_valid = X[valid_mask]
        weights_valid = weights[valid_mask]
        
        try:
            # 加权最小二乘回归
            W = np.diag(weights_valid)
            X_weighted = np.sqrt(W) @ X_valid
            y_weighted = np.sqrt(W) @ y_valid
            
            # 求解回归系数
            beta = np.linalg.lstsq(X_weighted, y_weighted, rcond=None)[0]
            
            # 计算预测值和残差
            y_pred = X_valid @ beta
            residuals = y_valid - y_pred
            
            # 计算R²
            ss_res = np.sum(weights_valid * residuals**2)
            ss_tot = np.sum(weights_valid * (y_valid - np.average(y_valid, weights=weights_valid))**2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            # 计算t统计量
            mse = ss_res / (len(y_valid) - len(beta))
            if mse > 0:
                var_beta = mse * np.diag(np.linalg.inv(X_weighted.T @ X_weighted))
                t_stats = beta / np.sqrt(np.abs(var_beta))
            else:
                t_stats = np.zeros_like(beta)
            
            # 保存结果
            result = {
                'date': date,
                'n_stocks': valid_mask.sum(),
                'r_squared': r_squared,
                'factor_returns': dict(zip(available_factors, beta)),
                't_stats': dict(zip(available_factors, t_stats))
            }
            
            regression_results.append(result)
            
        except Exception as e:
            if i < 10:  # 只在前10次失败时打印错误
                print(f"回归失败 {date}: {e}")
            continue
    
    if regression_results:
        print(f"\n横截面回归完成: {len(regression_results)}期")
        avg_r_squared = np.mean([r['r_squared'] for r in regression_results])
        avg_stocks = np.mean([r['n_stocks'] for r in regression_results])
        print(f"平均R²: {avg_r_squared:.4f}")
        print(f"平均股票数: {avg_stocks:.0f}")
        return regression_results
    else:
        print("横截面回归失败")
        return None

# 执行横截面回归
if returns_data is not None and factor_groups is not None:
    regression_results = cross_sectional_regression(returns_data, factor_groups)
else:
    regression_results = None

def model_validation(regression_results, true_factor_returns=None):
    """模型有效性检验"""
    print("\n=== 模型有效性检验 ===")
    
    if regression_results is None:
        return None
    
    # 转换为DataFrame便于分析
    results_df = pd.DataFrame(regression_results)
    
    # 1. R²分析
    r_squared_stats = {
        'mean': results_df['r_squared'].mean(),
        'std': results_df['r_squared'].std(),
        'min': results_df['r_squared'].min(),
        'max': results_df['r_squared'].max(),
        'median': results_df['r_squared'].median()
    }
    
    print(f"R²统计:")
    print(f"  均值: {r_squared_stats['mean']:.4f}")
    print(f"  标准差: {r_squared_stats['std']:.4f}")
    print(f"  中位数: {r_squared_stats['median']:.4f}")
    print(f"  范围: [{r_squared_stats['min']:.4f}, {r_squared_stats['max']:.4f}]")
    
    # 2. 因子收益率分析
    factor_returns_analysis = {}
    
    # 提取所有因子收益率
    all_factors = set()
    for result in regression_results:
        all_factors.update(result['factor_returns'].keys())
    
    print(f"\n分析 {len(all_factors)} 个因子的收益率...")
    
    for factor in all_factors:
        factor_returns = []
        t_stats = []
        
        for result in regression_results:
            if factor in result['factor_returns']:
                factor_returns.append(result['factor_returns'][factor])
                t_stats.append(result['t_stats'][factor])
        
        if factor_returns:
            factor_returns = np.array(factor_returns)
            t_stats = np.array(t_stats)
            
            factor_returns_analysis[factor] = {
                'mean_return': np.mean(factor_returns),
                'std_return': np.std(factor_returns),
                'mean_t_stat': np.mean(np.abs(t_stats)),
                't_stat_gt_2_ratio': np.mean(np.abs(t_stats) > 2),
                'return_t_test': np.mean(factor_returns) / np.std(factor_returns) * np.sqrt(len(factor_returns)) if np.std(factor_returns) > 0 else 0
            }
            
            # 如果有真实因子收益率，计算偏差
            if true_factor_returns and factor in true_factor_returns:
                true_return = true_factor_returns[factor]
                estimated_return = np.mean(factor_returns)
                factor_returns_analysis[factor]['bias'] = estimated_return - true_return
                factor_returns_analysis[factor]['true_return'] = true_return
    
    # 输出主要因子分析结果
    print(f"\n主要因子收益率分析:")
    print(f"{'因子名称':<25} {'均值':<10} {'标准差':<10} {'|t|均值':<10} {'|t|>2占比':<10} {'t检验':<10}")
    print("-" * 85)
    
    # 优先显示重要因子
    important_factors = [f for f in factor_returns_analysis.keys() 
                        if any(keyword in f for keyword in ['Country', 'Style_', 'Industry_银行', 'Industry_地产'])]
    
    for factor in important_factors[:10]:  # 显示前10个重要因子
        stats = factor_returns_analysis[factor]
        factor_short = factor.replace('Industry_', '').replace('Style_', '')[:20]
        print(f"{factor_short:<25} {stats['mean_return']:<10.6f} {stats['std_return']:<10.6f} "
              f"{stats['mean_t_stat']:<10.4f} {stats['t_stat_gt_2_ratio']:<10.2%} "
              f"{stats['return_t_test']:<10.4f}")
    
    return {
        'r_squared_stats': r_squared_stats,
        'factor_analysis': factor_returns_analysis,
        'results_df': results_df
    }

# 模型验证
if regression_results is not None:
    validation_results = model_validation(regression_results, true_factor_returns)
else:
    validation_results = None

def benchmark_backtest_validation(returns_data, all_data, factor_groups):
    """基于基准指数的回测验证"""
    print("\n=== 基于基准的回测验证 ===")
    
    if returns_data is None or all_data['benchmark_data'] is None:
        print("缺少必要数据，无法进行回测验证")
        return None
    
    benchmark_data = all_data['benchmark_data']
    
    # 1. 构建因子组合
    print("构建因子组合...")
    
    style_factors = factor_groups.get('processed_style_factors', [])
    if not style_factors:
        print("没有可用的风格因子进行回测")
        return None
    
    backtest_results = {}
    
    for factor in style_factors:
        print(f"\n回测因子: {factor}")
        
        factor_returns = []
        benchmark_returns = []
        dates = []
        
        # 按月构建组合
        returns_data['year_month'] = returns_data['date'].dt.to_period('M')
        
        for period in sorted(returns_data['year_month'].unique()):
            period_data = returns_data[returns_data['year_month'] == period]
            
            if len(period_data) < 100:  # 至少需要100只股票
                continue
            
            # 按因子值分组
            period_data = period_data.dropna(subset=[factor, 'return'])
            
            if len(period_data) < 100:
                continue
            
            try:
                # 分为高低两组
                period_data['factor_rank'] = pd.qcut(period_data[factor], 2, labels=['Low', 'High'])
                
                # 计算多空组合收益率
                high_return = period_data[period_data['factor_rank'] == 'High']['return'].mean()
                low_return = period_data[period_data['factor_rank'] == 'Low']['return'].mean()
                
                long_short_return = high_return - low_return
                
                # 获取对应的基准收益率
                period_start = period.start_time
                period_benchmark = benchmark_data[
                    (benchmark_data['date'] >= period_start) & 
                    (benchmark_data['date'] < period_start + pd.DateOffset(months=1))
                ]['benchmark_return'].mean()
                
                if not pd.isna(long_short_return) and not pd.isna(period_benchmark):
                    factor_returns.append(long_short_return)
                    benchmark_returns.append(period_benchmark)
                    dates.append(period_start)
                    
            except Exception as e:
                continue
        
        if len(factor_returns) > 12:  # 至少需要12个月的数据
            factor_returns = np.array(factor_returns)
            benchmark_returns = np.array(benchmark_returns)
            
            # 计算相对基准的超额收益
            excess_returns = factor_returns - benchmark_returns
            
            # 计算统计指标
            backtest_results[factor] = {
                'dates': dates,
                'factor_returns': factor_returns,
                'benchmark_returns': benchmark_returns,
                'excess_returns': excess_returns,
                'mean_return': np.mean(factor_returns),
                'mean_excess': np.mean(excess_returns),
                'std_return': np.std(factor_returns),
                'std_excess': np.std(excess_returns),
                'sharpe_ratio': np.mean(factor_returns) / np.std(factor_returns) if np.std(factor_returns) > 0 else 0,
                'info_ratio': np.mean(excess_returns) / np.std(excess_returns) if np.std(excess_returns) > 0 else 0,
                'win_rate': np.mean(factor_returns > 0),
                'excess_win_rate': np.mean(excess_returns > 0),
                'max_drawdown': np.min(np.cumsum(factor_returns) - np.maximum.accumulate(np.cumsum(factor_returns)))
            }
            
            print(f"  期数: {len(factor_returns)}")
            print(f"  平均收益: {backtest_results[factor]['mean_return']:.4f}")
            print(f"  超额收益: {backtest_results[factor]['mean_excess']:.4f}")
            print(f"  信息比率: {backtest_results[factor]['info_ratio']:.4f}")
            print(f"  胜率: {backtest_results[factor]['win_rate']:.2%}")
    
    return backtest_results

# 回测验证
if returns_data is not None and all_data is not None:
    backtest_results = benchmark_backtest_validation(returns_data, all_data, factor_groups)
else:
    backtest_results = None

def visualize_results(validation_results, backtest_results, all_data):
    """结果可视化"""
    print("\n=== 结果可视化 ===")
    
    if validation_results is None:
        print("没有验证结果可供可视化")
        return
    
    # 创建图表
    fig = plt.figure(figsize=(20, 16))
    
    # 1. R²时间序列 (2x3布局的第1个)
    ax1 = plt.subplot(3, 3, 1)
    results_df = validation_results['results_df']
    ax1.plot(results_df['date'], results_df['r_squared'], linewidth=2, color='blue', alpha=0.8)
    ax1.set_title('模型R²时间序列', fontsize=14, fontweight='bold')
    ax1.set_ylabel('R²')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # 2. R²分布 (第2个)
    ax2 = plt.subplot(3, 3, 2)
    ax2.hist(results_df['r_squared'], bins=30, alpha=0.7, edgecolor='black', color='skyblue')
    ax2.set_title('R²分布', fontsize=14, fontweight='bold')
    ax2.set_xlabel('R²')
    ax2.set_ylabel('频数')
    ax2.axvline(results_df['r_squared'].mean(), color='red', linestyle='--', 
                label=f'均值: {results_df["r_squared"].mean():.4f}')
    ax2.legend()
    
    # 3. 主要因子收益率 (第3个)
    ax3 = plt.subplot(3, 3, 3)
    factor_analysis = validation_results['factor_analysis']
    
    # 选择主要因子
    main_factors = [f for f in factor_analysis.keys() 
                   if any(keyword in f for keyword in ['Country', 'Style_'])]
    
    if main_factors:
        factor_names = [f.replace('Style_', '').replace('_orth', '').replace('Country_', '') for f in main_factors]
        mean_returns = [factor_analysis[f]['mean_return'] for f in main_factors]
        
        bars = ax3.bar(factor_names, mean_returns, alpha=0.7, color=['red', 'green', 'blue'][:len(factor_names)])
        ax3.set_title('主要因子平均收益率', fontsize=14, fontweight='bold')
        ax3.set_ylabel('平均收益率')
        ax3.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars, mean_returns):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.1, 
                    f'{value:.6f}', ha='center', va='bottom', fontsize=10)
    
    # 4. 基准收益率对比 (第4个)
    ax4 = plt.subplot(3, 3, 4)
    if all_data and 'benchmark_data' in all_data and all_data['benchmark_data'] is not None:
        benchmark_data = all_data['benchmark_data']
        if 'benchmark_return' in benchmark_data.columns:
            # 计算累计收益率
            benchmark_data_clean = benchmark_data.dropna(subset=['benchmark_return'])
            if len(benchmark_data_clean) > 0:
                cum_benchmark = (1 + benchmark_data_clean['benchmark_return']).cumprod()
                ax4.plot(benchmark_data_clean['date'], cum_benchmark, linewidth=2, 
                        color='orange', label='基准指数')
                ax4.set_title('基准指数累计收益率', fontsize=14, fontweight='bold')
                ax4.set_ylabel('累计收益率')
                ax4.legend()
                ax4.grid(True, alpha=0.3)
                ax4.tick_params(axis='x', rotation=45)
    
    # 5. 因子回测结果 (第5个)
    ax5 = plt.subplot(3, 3, 5)
    if backtest_results:
        factor_names = []
        info_ratios = []
        
        for factor, results in backtest_results.items():
            factor_name = factor.replace('Style_', '').replace('_orth', '')
            factor_names.append(factor_name)
            info_ratios.append(results['info_ratio'])
        
        if factor_names:
            bars = ax5.bar(factor_names, info_ratios, alpha=0.7, 
                          color=['purple', 'orange'][:len(factor_names)])
            ax5.set_title('因子信息比率 (IR)', fontsize=14, fontweight='bold')
            ax5.set_ylabel('信息比率')
            ax5.tick_params(axis='x', rotation=45)
            ax5.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            
            # 添加数值标签
            for bar, value in zip(bars, info_ratios):
                ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + value*0.1, 
                        f'{value:.3f}', ha='center', va='bottom', fontsize=10)
    
    # 6. 因子胜率对比 (第6个)
    ax6 = plt.subplot(3, 3, 6)
    if backtest_results:
        factor_names = []
        win_rates = []
        excess_win_rates = []
        
        for factor, results in backtest_results.items():
            factor_name = factor.replace('Style_', '').replace('_orth', '')
            factor_names.append(factor_name)
            win_rates.append(results['win_rate'])
            excess_win_rates.append(results['excess_win_rate'])
        
        if factor_names:
            x = np.arange(len(factor_names))
            width = 0.35
            
            ax6.bar(x - width/2, win_rates, width, label='绝对胜率', alpha=0.7, color='lightblue')
            ax6.bar(x + width/2, excess_win_rates, width, label='超额胜率', alpha=0.7, color='lightcoral')
            
            ax6.set_title('因子胜率对比', fontsize=14, fontweight='bold')
            ax6.set_ylabel('胜率')
            ax6.set_xticks(x)
            ax6.set_xticklabels(factor_names)
            ax6.legend()
            ax6.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
            ax6.tick_params(axis='x', rotation=45)
    
    # 7. 股票数量时间序列 (第7个)
    ax7 = plt.subplot(3, 3, 7)
    ax7.plot(results_df['date'], results_df['n_stocks'], linewidth=2, color='green', alpha=0.8)
    ax7.set_title('回归股票数量时间序列', fontsize=14, fontweight='bold')
    ax7.set_ylabel('股票数量')
    ax7.grid(True, alpha=0.3)
    ax7.tick_params(axis='x', rotation=45)
    
    # 8. 因子收益率时间序列 (第8个)
    ax8 = plt.subplot(3, 3, 8)
    if backtest_results:
        for factor, results in backtest_results.items():
            factor_name = factor.replace('Style_', '').replace('_orth', '')
            cum_returns = np.cumsum(results['factor_returns'])
            ax8.plot(results['dates'], cum_returns, linewidth=2, 
                    label=f'{factor_name}', alpha=0.8)
        
        ax8.set_title('因子累计收益率', fontsize=14, fontweight='bold')
        ax8.set_ylabel('累计收益率')
        ax8.legend()
        ax8.grid(True, alpha=0.3)
        ax8.tick_params(axis='x', rotation=45)
    
    # 9. 模型统计总结 (第9个)
    ax9 = plt.subplot(3, 3, 9)
    ax9.axis('off')
    
    # 创建统计总结文本
    summary_text = f"""
模型统计总结
{'='*30}
回归期数: {len(results_df)}
平均R²: {results_df['r_squared'].mean():.4f}
R²标准差: {results_df['r_squared'].std():.4f}
平均股票数: {results_df['n_stocks'].mean():.0f}

因子数量:
  国家因子: 1个
  行业因子: {len([f for f in factor_analysis.keys() if 'Industry_' in f])}个
  风格因子: {len([f for f in factor_analysis.keys() if 'Style_' in f])}个
"""
    
    if backtest_results:
        avg_ir = np.mean([r['info_ratio'] for r in backtest_results.values()])
        avg_win_rate = np.mean([r['win_rate'] for r in backtest_results.values()])
        summary_text += f"""
回测结果:
  平均信息比率: {avg_ir:.4f}
  平均胜率: {avg_win_rate:.2%}
"""
    
    ax9.text(0.1, 0.9, summary_text, transform=ax9.transAxes, fontsize=12,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    plt.show()
    
    # 保存图表
    try:
        fig.savefig(os.path.join(OUTPUT_PATH, 'barra_model_results.png'), 
                   dpi=300, bbox_inches='tight')
        print(f"图表已保存: {os.path.join(OUTPUT_PATH, 'barra_model_results.png')}")
    except Exception as e:
        print(f"保存图表失败: {e}")

# 可视化结果
visualize_results(validation_results, backtest_results, all_data)

def save_results_and_summary():
    """保存结果和生成总结报告"""
    print("\n=== 保存结果和生成总结 ===")
    
    try:
        # 1. 保存回归结果
        if regression_results:
            results_df = pd.DataFrame(regression_results)
            results_path = os.path.join(OUTPUT_PATH, 'regression_results.csv')
            results_df.to_csv(results_path, index=False, encoding='utf-8-sig')
            print(f"✓ 回归结果已保存: {results_path}")
        
        # 2. 保存回测结果
        if backtest_results:
            backtest_summary = []
            for factor, results in backtest_results.items():
                backtest_summary.append({
                    'factor': factor,
                    'mean_return': results['mean_return'],
                    'mean_excess': results['mean_excess'],
                    'sharpe_ratio': results['sharpe_ratio'],
                    'info_ratio': results['info_ratio'],
                    'win_rate': results['win_rate'],
                    'excess_win_rate': results['excess_win_rate'],
                    'max_drawdown': results['max_drawdown']
                })
            
            backtest_df = pd.DataFrame(backtest_summary)
            backtest_path = os.path.join(OUTPUT_PATH, 'backtest_results.csv')
            backtest_df.to_csv(backtest_path, index=False, encoding='utf-8-sig')
            print(f"✓ 回测结果已保存: {backtest_path}")
        
        # 3. 生成详细报告
        report_path = os.path.join(OUTPUT_PATH, 'barra_model_report.txt')
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("Barra CNE6 多因子风险模型完整报告\n")
            f.write("=" * 60 + "\n\n")
            
            # 模型基本信息
            f.write("1. 模型基本信息\n")
            f.write("-" * 30 + "\n")
            if factor_groups:
                f.write(f"国家因子: {len(factor_groups['country_factors'])}个\n")
                f.write(f"行业因子: {len(factor_groups['industry_factors'])}个\n")
                f.write(f"风格因子: {len(factor_groups.get('processed_style_factors', []))}个\n")
            
            if factor_df is not None:
                f.write(f"数据期间: {factor_df['date'].min()} 到 {factor_df['date'].max()}\n")
                f.write(f"股票数量: {factor_df['ts_code'].nunique()}只\n")
                f.write(f"观测数量: {len(factor_df)}条\n\n")
            
            # 模型验证结果
            if validation_results:
                f.write("2. 模型验证结果\n")
                f.write("-" * 30 + "\n")
                r2_stats = validation_results['r_squared_stats']
                f.write(f"R²统计:\n")
                f.write(f"  均值: {r2_stats['mean']:.4f}\n")
                f.write(f"  标准差: {r2_stats['std']:.4f}\n")
                f.write(f"  中位数: {r2_stats['median']:.4f}\n")
                f.write(f"  范围: [{r2_stats['min']:.4f}, {r2_stats['max']:.4f}]\n\n")
                
                # 主要因子分析
                f.write("主要因子收益率分析:\n")
                factor_analysis = validation_results['factor_analysis']
                main_factors = [f for f in factor_analysis.keys() 
                               if any(keyword in f for keyword in ['Country', 'Style_'])]
                
                for factor in main_factors:
                    stats = factor_analysis[factor]
                    f.write(f"  {factor}:\n")
                    f.write(f"    平均收益率: {stats['mean_return']:.6f}\n")
                    f.write(f"    收益率标准差: {stats['std_return']:.6f}\n")
                    f.write(f"    |t|>2占比: {stats['t_stat_gt_2_ratio']:.2%}\n")
                    f.write(f"    收益率t检验: {stats['return_t_test']:.4f}\n\n")
            
            # 回测结果
            if backtest_results:
                f.write("3. 回测验证结果\n")
                f.write("-" * 30 + "\n")
                for factor, results in backtest_results.items():
                    f.write(f"{factor}:\n")
                    f.write(f"  平均收益率: {results['mean_return']:.4f}\n")
                    f.write(f"  超额收益率: {results['mean_excess']:.4f}\n")
                    f.write(f"  夏普比率: {results['sharpe_ratio']:.4f}\n")
                    f.write(f"  信息比率: {results['info_ratio']:.4f}\n")
                    f.write(f"  胜率: {results['win_rate']:.2%}\n")
                    f.write(f"  超额胜率: {results['excess_win_rate']:.2%}\n")
                    f.write(f"  最大回撤: {results['max_drawdown']:.4f}\n\n")
            
            # 结论和建议
            f.write("4. 结论和建议\n")
            f.write("-" * 30 + "\n")
            f.write("模型特点:\n")
            f.write("• 严格按照Barra标准流程构建\n")
            f.write("• 基于真实市场数据和基准指数\n")
            f.write("• 完整的数据预处理和模型验证\n")
            f.write("• 市值加权横截面回归\n\n")
            
            if validation_results:
                avg_r2 = validation_results['r_squared_stats']['mean']
                if avg_r2 > 0.3:
                    f.write(f"模型表现: 优秀 (R²={avg_r2:.4f} > 0.30)\n")
                elif avg_r2 > 0.2:
                    f.write(f"模型表现: 良好 (R²={avg_r2:.4f} > 0.20)\n")
                else:
                    f.write(f"模型表现: 一般 (R²={avg_r2:.4f})\n")
            
            f.write("\n改进建议:\n")
            f.write("• 补充更多财务数据构建完整风格因子\n")
            f.write("• 增加更长的历史数据提高模型稳定性\n")
            f.write("• 考虑加入动量、质量等其他风格因子\n")
            f.write("• 定期更新模型参数和因子权重\n")
        
        print(f"✓ 详细报告已保存: {report_path}")
        
    except Exception as e:
        print(f"保存结果失败: {e}")
    
    # 最终总结
    print("\n" + "="*80)
    print("Barra CNE6 多因子风险模型构建完成")
    print("="*80)
    
    if validation_results:
        r2_mean = validation_results['r_squared_stats']['mean']
        print(f"✓ 模型解释力 (平均R²): {r2_mean:.4f}")
    
    if regression_results:
        print(f"✓ 回归期数: {len(regression_results)}期")
    
    if factor_groups:
        total_factors = (len(factor_groups['country_factors']) + 
                        len(factor_groups['industry_factors']) + 
                        len(factor_groups.get('processed_style_factors', [])))
        print(f"✓ 因子总数: {total_factors}个")
        print(f"  - 国家因子: {len(factor_groups['country_factors'])}个")
        print(f"  - 行业因子: {len(factor_groups['industry_factors'])}个")
        print(f"  - 风格因子: {len(factor_groups.get('processed_style_factors', []))}个")
    
    if backtest_results:
        print(f"✓ 回测验证: {len(backtest_results)}个因子")
        avg_ir = np.mean([r['info_ratio'] for r in backtest_results.values()])
        print(f"  - 平均信息比率: {avg_ir:.4f}")
    
    print(f"\n所有结果已保存至: {OUTPUT_PATH}")
    print("\n模型特点:")
    print("• 基于真实数据的完整Barra CNE6模型")
    print("• 严格的数据预处理和模型验证")
    print("• 基于基准指数的回测验证")
    print("• 专业的可视化分析和报告")
    
    print("\n🎉 Barra CNE6模型构建成功完成！")

# 保存结果和生成总结
save_results_and_summary()