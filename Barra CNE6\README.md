# Barra CNE6 风险模型复现项目

## 项目概述

本项目旨在复现Barra CNE6长期投资风险模型，基于西部证券研报《Barra（CNE+6）长期投资风险模型的复现及应用》进行实现。

## Barra CNE6 模型框架

### 模型结构
```
股票收益率 = 因子暴露度 × 因子收益率 + 特质收益率
r_i = X_i^T × f + u_i
```

### 因子体系
1. **风格因子 (6个)**:
   - Size (规模): 市值相关指标
   - Value (价值): 估值相关指标  
   - Profitability (盈利): 盈利能力指标
   - Growth (成长): 成长性指标
   - Leverage (杠杆): 财务杠杆指标
   - Liquidity (流动性): 交易活跃度指标

2. **行业因子**: 基于中信一级行业分类

3. **国家因子**: 中国市场因子

## 项目进度

### ✅ 已完成

#### 01_数据预处理.ipynb
- [x] 加载和清洗原始数据
- [x] 构建统一的数据格式
- [x] 剔除上市不足60个交易日的股票
- [x] 合并行业信息
- [x] 数据质量检查和验证

#### 02_风格因子构建.ipynb  
- [x] 基于现有数据构建部分风格因子
- [x] 因子标准化和去极值处理
- [x] 行业中性化处理
- [x] 因子质量分析和可视化

#### 03_行业因子构建.ipynb
- [x] 构建行业因子矩阵
- [x] 计算行业权重
- [x] 行业因子有效性验证
- [x] 行业因子可视化

### 🔄 进行中

#### 04_因子收益率计算.ipynb (待创建)
- [ ] 股票收益率计算
- [ ] 因子收益率回归
- [ ] 加权最小二乘法实现
- [ ] 因子收益率时间序列分析

#### 05_协方差矩阵估计.ipynb (待创建)
- [ ] 因子协方差矩阵估计
- [ ] 时间衰减权重处理
- [ ] 协方差矩阵调整和平滑

#### 06_特质风险模型.ipynb (待创建)
- [ ] 特质风险估计
- [ ] 异方差性处理
- [ ] 特质风险预测

#### 07_风险模型验证.ipynb (待创建)
- [ ] 模型有效性验证
- [ ] 风险预测准确性测试
- [ ] 组合优化应用示例

## 数据文件说明

### 现有数据
```
C:\Users\<USER>\Desktop\金元顺安\单因子\data\
├── swind.xlsx              # 行业分类数据 ✅
├── daily0925.h5            # 股票日度数据 ⚠️ (需要解析)
├── adjfactor.hd5           # 复权因子 ⚠️ (需要解析)
├── factor.h5               # 因子数据 (PB, 市值) ✅
├── ipodate.csv             # IPO日期数据 ✅
└── ...

C:\Users\<USER>\Desktop\金元顺安\一致预期\data\
├── ind.h5                  # 市值数据 ⚠️ (需要解析)
└── ...
```

### 已处理数据
```
C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\
├── processed_data\
│   ├── factor_data_processed.h5    # 预处理后的因子数据
│   ├── factor_data_processed.csv
│   └── data_summary.txt
├── style_factors\
│   ├── style_factors_complete.h5   # 风格因子数据
│   ├── style_factors_only.h5
│   └── style_factors_summary.txt
└── industry_factors\
    ├── industry_factor_matrix.h5   # 行业因子矩阵
    ├── industry_weights.h5         # 行业权重
    └── industry_factors_summary.txt
```

## 缺失数据分析

### 🔴 关键缺失数据

1. **股票收益率数据**
   - 需要: 日度股票收益率
   - 用途: 因子收益率回归的因变量
   - 来源: daily0925.h5 (需要解析) + adjfactor.hd5

2. **完整的风格因子原始数据**
   - 已有: PB (价值), total_mv (规模)
   - 缺少: 盈利、成长、杠杆、流动性相关指标
   - 需要: 财务数据、交易数据、分析师数据

3. **交易数据**
   - 需要: 成交量、成交额、换手率
   - 用途: 构建流动性因子
   - 来源: daily0925.h5 (需要解析)

4. **财务数据**
   - 需要: 资产负债表、利润表、现金流量表
   - 用途: 构建盈利、成长、杠杆因子
   - 状态: 缺失

### 🟡 次要缺失数据

1. **基准指数数据**
   - 需要: 中证800、沪深300等指数数据
   - 用途: 模型验证和基准比较

2. **分析师数据**
   - 需要: 盈利预测、评级数据
   - 用途: 增强成长因子

3. **宏观数据**
   - 需要: 利率、汇率等
   - 用途: 市场环境分析

## 技术实现要点

### 因子构建方法
1. **标准化**: 横截面Z-score标准化
2. **去极值**: 3倍标准差截尾
3. **中性化**: 行业中性化处理
4. **正交化**: 因子间正交化

### 风险模型估计
1. **因子收益率**: 加权最小二乘法回归
2. **协方差矩阵**: 指数加权移动平均
3. **特质风险**: GARCH类模型
4. **风险调整**: Newey-West调整

## 使用说明

### 环境要求
```bash
pip install pandas numpy h5py openpyxl matplotlib seaborn jupyter
```

### 运行步骤
1. 确保数据文件路径正确
2. 按顺序运行notebook文件:
   ```
   01_数据预处理.ipynb
   02_风格因子构建.ipynb  
   03_行业因子构建.ipynb
   04_因子收益率计算.ipynb (待创建)
   05_协方差矩阵估计.ipynb (待创建)
   06_特质风险模型.ipynb (待创建)
   07_风险模型验证.ipynb (待创建)
   ```

### 数据路径配置
在每个notebook中修改以下路径:
```python
DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data'
MARKET_DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data'
```

## 下一步计划

### 立即需要
1. **解析现有HDF5文件**: daily0925.h5, adjfactor.hd5, ind.h5
2. **计算股票收益率**: 基于价格和复权因子
3. **创建04_因子收益率计算.ipynb**

### 中期目标
1. **补充财务数据**: 获取完整的财务指标
2. **构建完整风格因子**: 实现全部6个风格因子
3. **完成风险模型**: 实现完整的Barra CNE6模型

### 长期目标
1. **模型验证**: 多维度验证模型有效性
2. **实际应用**: 组合优化和风险管理
3. **模型改进**: 基于中国市场特点的改进

## 联系信息

如有问题或需要协助，请参考:
- 西部证券研报: `20250122-西部证券-西部证券风险模型及组合优化系列（1）.pdf`
- Barra官方文档: `Barra US Equity Model.v4.pdf`

---

**注意**: 本项目仅用于学习和研究目的，实际投资决策请谨慎考虑。
