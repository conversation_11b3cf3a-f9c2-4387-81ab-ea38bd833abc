# 简化版Barra模型构建项目

本项目按照您的要求，完成了简化版的Barra模型构建，分布在6个Jupyter notebook中实现。

## 项目结构

```
Barra CNE6/
├── 01_数据准备与检查.ipynb          # 数据加载、预处理、筛选有效股票样本
├── 02_因子数据处理.ipynb            # 缺失值处理、极端值处理、因子正交化、标准化
├── 03_构建因子暴露矩阵.ipynb        # 创建行业因子哑变量、国家因子、整合因子暴露矩阵
├── 04_计算被解释变量.ipynb          # 计算超额收益率、处理极端值、计算回归权重
├── 05_横截面回归模型估计.ipynb      # 设置约束条件、执行每日横截面回归
├── 06_模型有效性检验.ipynb          # 模型解释力检验、因子有效性检验、IC分析
├── data/                            # 数据目录
│   ├── merged_data_871.h5          # 原始股票数据+因子数据
│   ├── swind.xlsx                  # 行业分类数据
│   ├── ipodate.csv                 # 上市退市日期数据
│   ├── index.csv                   # 沪深300指数数据
│   ├── ind.h5                      # 市值数据
│   └── ...                         # 处理后的中间数据文件
└── README.md                       # 项目说明文档
```

## 实现功能

### 1. 数据准备与检查 (01_数据准备与检查.ipynb)
- ✅ 加载股票数据、行业分类、上市退市日期、沪深300指数、市值数据
- ✅ 数据格式统一（日期格式标准化）
- ✅ 筛选有效股票样本：
  - 剔除收益率绝对值大于23%的异常数据
  - 剔除上市未满6个月的股票
  - 剔除交易日数不足的股票
- ✅ 数据整合与存储

### 2. 因子数据处理 (02_因子数据处理.ipynb)
- ✅ 缺失值处理：用行业均值填充缺失率低于10%的字段
- ✅ 极端值处理：对PB因子使用五倍中位数法处理极端值
- ✅ 因子正交化处理：
  - 计算市值因子与PB因子的Pearson相关系数
  - 当相关系数绝对值>0.4时执行正交化
- ✅ 因子标准化：按交易日期对因子进行标准化

### 3. 构建因子暴露矩阵 (03_构建因子暴露矩阵.ipynb)
- ✅ 创建行业因子哑变量：根据申万行业分类创建哑变量
- ✅ 创建国家因子：所有A股股票国家因子暴露值均为1
- ✅ 整合因子暴露矩阵：
  - 国家因子（1列）
  - 行业因子（31列）
  - 标准化市值因子（1列）
  - 标准化PB因子（1列）
- ✅ 保存因子暴露矩阵X.csv

### 4. 计算被解释变量 (04_计算被解释变量.ipynb)
- ✅ 计算个股相对超额收益率：个股收益率 - 沪深300收益率
- ✅ 处理超额收益率极端值：对绝对值超过20%的极端值进行处理
- ✅ 计算回归权重：采用市值平方根加权法
- ✅ 保存被解释变量Y.csv

### 5. 横截面回归模型估计 (05_横截面回归模型估计.ipynb)
- ✅ 设置回归约束条件：实施行业中性约束
- ✅ 执行每日横截面回归：
  - 使用加权最小二乘法
  - 最小化加权残差平方和
  - 记录每日因子收益、R²和残差
- ✅ 保存日度因子收益.csv

### 6. 模型有效性检验 (06_模型有效性检验.ipynb)
- ✅ 模型整体解释力检验：
  - 计算非中心化交叉验证R²
  - 分析R²时间序列和分布
- ✅ 单个因子有效性检验：
  - 因子收益显著性检验（t检验）
  - 计算t值绝对值大于1.64的频率
- ✅ 信息系数(IC)分析：
  - 计算因子暴露与未来收益的相关性
  - 计算IC均值、标准差和ICIR
- ✅ 分组收益检验：
  - 按因子暴露值分为5组
  - 计算各组未来收益和G5-G1收益差
- ✅ 检验结果汇总和可视化

## 数据输出

项目生成以下关键输出文件：

### 主要结果文件
- `因子暴露矩阵X.csv` - 完整的因子暴露矩阵
- `被解释变量Y.csv` - 超额收益率和权重数据
- `日度因子收益.csv` - 每日因子收益估计结果

### 分析结果文件
- `模型整体指标.csv` - 模型R²等整体表现指标
- `因子有效性汇总.csv` - 各因子的显著性检验结果
- `IC分析结果.csv` - 信息系数分析结果
- `分组收益分析.csv` - 因子分组收益检验结果

### 中间数据文件
- `processed_data_step1.h5` - 第一步处理后的数据
- `processed_data_step2.h5` - 第二步处理后的数据
- `processed_data_step3.h5` - 第三步处理后的数据
- `processed_data_step4.h5` - 第四步处理后的数据

## 模型特点

1. **简化设计**：相比完整的Barra CNE6模型，本版本专注于核心功能
2. **模块化实现**：每个notebook专注于特定功能，便于理解和维护
3. **完整流程**：从数据准备到模型验证的完整实现
4. **可视化分析**：包含丰富的图表分析和统计检验
5. **结果验证**：多维度的模型有效性检验

## 使用说明

1. 按顺序运行6个notebook：01 → 02 → 03 → 04 → 05 → 06
2. 每个notebook都会生成中间结果文件，供后续步骤使用
3. 最终的模型有效性报告在第6个notebook中生成
4. 所有输出文件保存在`data/`目录下

## 技术要求

- Python 3.8+
- pandas, numpy, scipy, scikit-learn
- matplotlib, seaborn（用于可视化）
- h5py（用于HDF5文件处理）

## 注意事项

- 本项目为简化版实现，主要用于学习和理解Barra模型原理
- 实际生产环境中需要更多的数据验证和模型优化
- 建议在运行前确保有足够的内存和计算资源
