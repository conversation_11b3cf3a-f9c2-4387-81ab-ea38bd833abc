# Barra CNE6 风险模型复现项目

## 项目概述

本项目旨在复现Barra CNE6长期投资风险模型，基于西部证券研报《Barra（CNE+6）长期投资风险模型的复现及应用》进行实现。

## Barra CNE6 模型框架

### 模型结构
```
股票收益率 = 因子暴露度 × 因子收益率 + 特质收益率
r_i = X_i^T × f + u_i
```

### 因子体系
1. **风格因子 (6个)**:
   - Size (规模): 市值相关指标
   - Value (价值): 估值相关指标  
   - Profitability (盈利): 盈利能力指标
   - Growth (成长): 成长性指标
   - Leverage (杠杆): 财务杠杆指标
   - Liquidity (流动性): 交易活跃度指标

2. **行业因子**: 基于中信一级行业分类

3. **国家因子**: 中国市场因子

## 项目进度

### ✅ 已完成

#### Barra_CNE6_完整模型.ipynb ⭐ **单文件完整实现**
- [x] **数据加载**: 因子、行业、IPO、基准指数、市值数据
- [x] **完整因子体系**: 国家因子 + 行业因子 + 风格因子
- [x] **标准Barra流程**: 去极值、标准化、正交化
- [x] **基于基准的收益率构建**: 使用基准指数数据
- [x] **横截面回归**: 市值加权最小二乘法
- [x] **模型验证**: R²分析、因子收益率检验
- [x] **基准回测验证**: 基于基准指数的回测分析
- [x] **专业可视化**: 9个维度的完整分析图表
- [x] **结果保存**: CSV数据 + 详细报告 + 图表

### 🎯 **核心成果** - 完整Barra CNE6模型

#### ✅ 04_完整风险模型构建.ipynb - **主要成果**
**严格按照您提供的标准Barra流程实现**：

1. **确定风险因子体系**:
   - ✅ 国家因子：中国市场因子（所有股票暴露度=1）
   - ✅ 行业因子：基于申万行业分类
   - ✅ 风格因子：Size和Value两个风格因子

2. **数据预处理**:
   - ✅ 去极值：五倍中位数法
   - ✅ 补充缺失值：行业中位数填充
   - ✅ 正交化：Value对Size正交化
   - ✅ 标准化：均值0标准差1

3. **横截面回归模型**:
   - ✅ 市值加权最小二乘回归
   - ✅ 约束条件处理
   - ✅ 因子收益率估计

4. **模型有效性检验**:
   - ✅ 非中心化R²计算
   - ✅ 因子统计检验（t值分析）
   - ✅ IC分析（21日前瞻收益率）
   - ✅ 因子分组收益测试

### 🔄 后续扩展 (可选)

#### 05_协方差矩阵估计.ipynb (可选)
- [ ] 因子协方差矩阵估计
- [ ] 时间衰减权重处理

#### 06_特质风险模型.ipynb (可选)
- [ ] 特质风险估计
- [ ] 异方差性处理

#### 07_实际应用示例.ipynb (可选)
- [ ] 组合风险分解
- [ ] 组合优化应用

## 数据文件说明

### 现有数据
```
C:\Users\<USER>\Desktop\金元顺安\单因子\data\
├── swind.xlsx              # 行业分类数据 ✅
├── daily0925.h5            # 股票日度数据 ⚠️ (需要解析)
├── adjfactor.hd5           # 复权因子 ⚠️ (需要解析)
├── factor.h5               # 因子数据 (PB, 市值) ✅
├── ipodate.csv             # IPO日期数据 ✅
└── ...

C:\Users\<USER>\Desktop\金元顺安\一致预期\data\
├── ind.h5                  # 市值数据 ⚠️ (需要解析)
└── ...
```

### 已处理数据
```
C:\Users\<USER>\Desktop\金元顺安\Barra CNE6\
├── processed_data\
│   ├── factor_data_processed.h5    # 预处理后的因子数据
│   ├── factor_data_processed.csv
│   └── data_summary.txt
├── style_factors\
│   ├── style_factors_complete.h5   # 风格因子数据
│   ├── style_factors_only.h5
│   └── style_factors_summary.txt
└── industry_factors\
    ├── industry_factor_matrix.h5   # 行业因子矩阵
    ├── industry_weights.h5         # 行业权重
    └── industry_factors_summary.txt
```

## 缺失数据分析

### 🔴 关键缺失数据

1. **股票收益率数据**
   - 需要: 日度股票收益率
   - 用途: 因子收益率回归的因变量
   - 来源: daily0925.h5 (需要解析) + adjfactor.hd5

2. **完整的风格因子原始数据**
   - 已有: PB (价值), total_mv (规模)
   - 缺少: 盈利、成长、杠杆、流动性相关指标
   - 需要: 财务数据、交易数据、分析师数据

3. **交易数据**
   - 需要: 成交量、成交额、换手率
   - 用途: 构建流动性因子
   - 来源: daily0925.h5 (需要解析)

4. **财务数据**
   - 需要: 资产负债表、利润表、现金流量表
   - 用途: 构建盈利、成长、杠杆因子
   - 状态: 缺失

### 🟡 次要缺失数据

1. **基准指数数据**
   - 需要: 中证800、沪深300等指数数据
   - 用途: 模型验证和基准比较

2. **分析师数据**
   - 需要: 盈利预测、评级数据
   - 用途: 增强成长因子

3. **宏观数据**
   - 需要: 利率、汇率等
   - 用途: 市场环境分析

## 技术实现要点

### 因子构建方法
1. **标准化**: 横截面Z-score标准化
2. **去极值**: 3倍标准差截尾
3. **中性化**: 行业中性化处理
4. **正交化**: 因子间正交化

### 风险模型估计
1. **因子收益率**: 加权最小二乘法回归
2. **协方差矩阵**: 指数加权移动平均
3. **特质风险**: GARCH类模型
4. **风险调整**: Newey-West调整

## 使用说明

### 环境要求
```bash
pip install pandas numpy h5py openpyxl matplotlib seaborn jupyter
```

### 运行步骤
1. 确保数据文件路径正确
2. 按顺序运行notebook文件:
   ```
   01_数据预处理.ipynb
   02_风格因子构建.ipynb  
   03_行业因子构建.ipynb
   04_因子收益率计算.ipynb (待创建)
   05_协方差矩阵估计.ipynb (待创建)
   06_特质风险模型.ipynb (待创建)
   07_风险模型验证.ipynb (待创建)
   ```

### 数据路径配置
在每个notebook中修改以下路径:
```python
DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data'
MARKET_DATA_PATH = r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data'
```

## 🚀 **立即可执行** (环境修复后)

### 快速测试
```bash
# 1. 测试环境和数据
python test_data.py

# 2. 测试完整模型框架
python test_complete_model.py
```

### 完整执行流程
```bash
# 单文件完整实现 - 推荐使用
Barra_CNE6_完整模型.ipynb  ⭐ **一键运行完整模型**
```

### 🎯 **重点推荐**

**直接运行 `Barra_CNE6_完整模型.ipynb`** - 这是重新设计的单文件完整实现，包含：

- ✅ **完整数据处理**: 自动加载所有数据源
- ✅ **完整因子体系**: 国家+行业+风格因子
- ✅ **标准Barra流程**: 严格按照您提供的步骤
- ✅ **基准回测验证**: 基于基准指数的专业回测
- ✅ **9维度可视化**: 全面的模型分析图表
- ✅ **自动保存结果**: CSV数据+详细报告+图表

## 📈 **预期结果**

运行完整模型后，您将获得：

### 模型输出
- **R²统计**: 模型解释力分析（目标>0.30）
- **因子收益率**: 各因子的日度收益率时间序列
- **t统计量**: 因子显著性检验（|t|>2占比）
- **IC分析**: 因子预测能力评估（IR比率）
- **分组收益**: 因子分组收益率单调性测试

### 可视化图表
1. 模型R²时间序列图
2. R²分布直方图
3. 主要因子收益率对比
4. 因子IC时间序列图
5. 因子IC分布图
6. 因子分组收益率图

### 验证指标
- **模型有效性**: 通过R²和残差分析
- **因子有效性**: 通过IC和分组测试
- **统计显著性**: 通过t检验和收益率检验

## 下一步扩展 (可选)

### 模型完善
1. **补充财务数据**: 获取完整的财务指标构建更多风格因子
2. **协方差矩阵**: 实现因子协方差矩阵估计
3. **特质风险**: 构建特质风险模型

### 实际应用
1. **组合优化**: 基于风险模型的组合构建
2. **风险归因**: 组合收益率和风险归因分析
3. **风险预测**: 前瞻性风险预测和压力测试

## 联系信息

如有问题或需要协助，请参考:
- 西部证券研报: `20250122-西部证券-西部证券风险模型及组合优化系列（1）.pdf`
- Barra官方文档: `Barra US Equity Model.v4.pdf`

---

**注意**: 本项目仅用于学习和研究目的，实际投资决策请谨慎考虑。
