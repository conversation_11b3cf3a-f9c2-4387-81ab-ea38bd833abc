"""
测试数据文件的可用性和结构
"""

import os
import sys

def check_file_exists():
    """检查文件是否存在"""
    print("=== 检查数据文件存在性 ===")
    
    files_to_check = [
        r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\swind.xlsx',
        r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\daily0925.h5',
        r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\adjfactor.hd5',
        r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\factor.h5',
        r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\ipodate.csv',
        r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data\ind.h5'
    ]
    
    existing_files = []
    missing_files = []
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✓ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"✗ {file_path}")
    
    print(f"\n存在的文件: {len(existing_files)}")
    print(f"缺失的文件: {len(missing_files)}")
    
    return existing_files, missing_files

def check_basic_data():
    """检查基本数据文件"""
    print("\n=== 检查基本数据文件 ===")
    
    try:
        # 尝试导入pandas
        import pandas as pd
        print("✓ pandas 导入成功")
        
        # 检查行业数据
        try:
            industry_path = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\swind.xlsx'
            if os.path.exists(industry_path):
                industry = pd.read_excel(industry_path)
                print(f"✓ 行业数据: {industry.shape}")
                print(f"  列名: {list(industry.columns)}")
                if len(industry) > 0:
                    print(f"  样本: {industry.iloc[0].to_dict()}")
            else:
                print("✗ 行业数据文件不存在")
        except Exception as e:
            print(f"✗ 行业数据读取失败: {e}")
        
        # 检查IPO数据
        try:
            ipo_path = r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\ipodate.csv'
            if os.path.exists(ipo_path):
                ipo = pd.read_csv(ipo_path)
                print(f"✓ IPO数据: {ipo.shape}")
                print(f"  列名: {list(ipo.columns)}")
                if len(ipo) > 0:
                    print(f"  样本: {ipo.iloc[0].to_dict()}")
            else:
                print("✗ IPO数据文件不存在")
        except Exception as e:
            print(f"✗ IPO数据读取失败: {e}")
            
    except ImportError as e:
        print(f"✗ pandas 导入失败: {e}")
        return False
    
    return True

def check_h5_files():
    """检查HDF5文件"""
    print("\n=== 检查HDF5文件 ===")
    
    try:
        import h5py
        print("✓ h5py 导入成功")
        
        h5_files = [
            r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\factor.h5',
            r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\daily0925.h5',
            r'C:\Users\<USER>\Desktop\金元顺安\单因子\data\adjfactor.hd5',
            r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data\ind.h5'
        ]
        
        for file_path in h5_files:
            if os.path.exists(file_path):
                try:
                    with h5py.File(file_path, 'r') as f:
                        keys = list(f.keys())
                        print(f"✓ {os.path.basename(file_path)}: 根级别键 = {keys}")
                except Exception as e:
                    print(f"✗ {os.path.basename(file_path)}: 读取失败 - {e}")
            else:
                print(f"✗ {os.path.basename(file_path)}: 文件不存在")
                
    except ImportError as e:
        print(f"✗ h5py 导入失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("Barra CNE6 数据检查工具")
    print("=" * 50)
    
    # 检查文件存在性
    existing_files, missing_files = check_file_exists()
    
    # 检查基本数据
    basic_ok = check_basic_data()
    
    # 检查HDF5文件
    h5_ok = check_h5_files()
    
    # 总结
    print("\n=== 检查总结 ===")
    print(f"文件存在性: {len(existing_files)}/6 个文件存在")
    print(f"基本数据读取: {'成功' if basic_ok else '失败'}")
    print(f"HDF5文件读取: {'成功' if h5_ok else '失败'}")
    
    if len(existing_files) == 6 and basic_ok:
        print("\n✓ 数据检查通过，可以开始运行Barra CNE6复现")
        print("建议运行顺序:")
        print("1. 01_数据预处理.ipynb")
        print("2. 02_风格因子构建.ipynb")
        print("3. 03_行业因子构建.ipynb")
    else:
        print("\n✗ 数据检查未完全通过")
        if missing_files:
            print("缺失文件:")
            for file in missing_files:
                print(f"  - {file}")
        if not basic_ok:
            print("- 基本数据读取失败，可能需要安装或修复pandas")
        if not h5_ok:
            print("- HDF5文件读取失败，可能需要安装或修复h5py")

if __name__ == "__main__":
    main()
