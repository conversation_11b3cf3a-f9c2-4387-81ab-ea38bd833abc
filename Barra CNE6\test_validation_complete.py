#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试完整版模型有效性检验
验证数据时间范围和关键功能
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def test_data_range():
    """测试数据时间范围"""
    print("=== 测试数据时间范围 ===")
    
    try:
        # 检查原始数据
        merged_data = pd.read_hdf('data/processed_data_step4.h5', key='data')
        merged_data['trade_date'] = pd.to_datetime(merged_data['trade_date'])
        
        print(f"原始数据时间范围: {merged_data['trade_date'].min()} 到 {merged_data['trade_date'].max()}")
        print(f"原始数据年份: {sorted(merged_data['trade_date'].dt.year.unique())}")
        
        # 筛选2016-2022年数据
        start_date = pd.Timestamp('2016-01-01')
        end_date = pd.Timestamp('2022-12-31')
        
        filtered_data = merged_data[(merged_data['trade_date'] >= start_date) & 
                                   (merged_data['trade_date'] <= end_date)]
        
        print(f"筛选后数据时间范围: {start_date} 到 {end_date}")
        print(f"筛选后数据形状: {filtered_data.shape}")
        print(f"筛选后年份: {sorted(filtered_data['trade_date'].dt.year.unique())}")
        
        # 检查每年的数据量
        yearly_counts = filtered_data.groupby(filtered_data['trade_date'].dt.year).size()
        print(f"\n每年数据量:")
        for year, count in yearly_counts.items():
            print(f"  {year}: {count:,}")
        
        return True
        
    except Exception as e:
        print(f"数据范围测试失败: {e}")
        return False

def test_factor_availability():
    """测试因子可用性"""
    print("\n=== 测试因子可用性 ===")
    
    try:
        merged_data = pd.read_hdf('data/processed_data_step4.h5', key='data')
        
        # 识别因子列
        all_columns = merged_data.columns.tolist()
        industry_factors = [col for col in all_columns if col.startswith('ind_')]
        style_factors = ['size_std', 'pb_std']
        available_style_factors = [col for col in style_factors if col in all_columns]
        
        print(f"总列数: {len(all_columns)}")
        print(f"行业因子数: {len(industry_factors)}")
        print(f"可用风格因子: {available_style_factors}")
        
        # 检查因子数据质量
        for factor in available_style_factors:
            factor_data = merged_data[factor]
            print(f"\n{factor} 数据质量:")
            print(f"  缺失值: {factor_data.isnull().sum()}")
            print(f"  唯一值数: {factor_data.nunique()}")
            print(f"  数值范围: [{factor_data.min():.4f}, {factor_data.max():.4f}]")
        
        return len(available_style_factors) > 0
        
    except Exception as e:
        print(f"因子可用性测试失败: {e}")
        return False

def test_regression_results():
    """测试回归结果可用性"""
    print("\n=== 测试回归结果可用性 ===")
    
    try:
        # 检查回归结果文件
        regression_results = pd.read_hdf('data/regression_results.h5', key='data')
        regression_results['trade_date'] = pd.to_datetime(regression_results['trade_date'])
        
        print(f"回归结果数量: {len(regression_results)}")
        print(f"成功回归数量: {regression_results['success'].sum()}")
        print(f"成功率: {regression_results['success'].mean():.2%}")
        
        # 检查时间范围
        print(f"回归结果时间范围: {regression_results['trade_date'].min()} 到 {regression_results['trade_date'].max()}")
        
        # 检查因子收益文件
        factor_returns = pd.read_hdf('data/factor_returns.h5', key='data')
        factor_returns.index = pd.to_datetime(factor_returns.index)
        
        print(f"因子收益数量: {len(factor_returns)}")
        print(f"因子收益列: {factor_returns.columns.tolist()}")
        print(f"因子收益时间范围: {factor_returns.index.min()} 到 {factor_returns.index.max()}")
        
        return True
        
    except Exception as e:
        print(f"回归结果测试失败: {e}")
        return False

def test_rolling_calculation():
    """测试滚动计算功能"""
    print("\n=== 测试滚动计算功能 ===")
    
    try:
        # 加载数据
        merged_data = pd.read_hdf('data/processed_data_step4.h5', key='data')
        merged_data['trade_date'] = pd.to_datetime(merged_data['trade_date'])
        
        # 筛选时间范围
        start_date = pd.Timestamp('2016-01-01')
        end_date = pd.Timestamp('2022-12-31')
        merged_data = merged_data[(merged_data['trade_date'] >= start_date) & 
                                 (merged_data['trade_date'] <= end_date)]
        
        # 获取交易日期
        trading_dates = sorted(merged_data['trade_date'].unique())
        print(f"交易日期数量: {len(trading_dates)}")
        
        # 测试小窗口滚动计算
        window_days = 50  # 使用小窗口测试
        test_results = []
        
        for i, current_date in enumerate(trading_dates[:100]):  # 只测试前100个日期
            if i < window_days - 1:
                continue
                
            window_dates = trading_dates[i - window_days + 1:i + 1]
            window_data = merged_data[merged_data['trade_date'].isin(window_dates)]
            
            if len(window_data) > 0:
                test_results.append({
                    'date': current_date,
                    'window_size': len(window_dates),
                    'data_points': len(window_data)
                })
        
        print(f"测试滚动计算成功，生成 {len(test_results)} 个数据点")
        if len(test_results) > 0:
            print(f"示例结果: {test_results[0]}")
        
        return len(test_results) > 0
        
    except Exception as e:
        print(f"滚动计算测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试完整版模型有效性检验...")
    
    tests = [
        ("数据时间范围", test_data_range),
        ("因子可用性", test_factor_availability),
        ("回归结果", test_regression_results),
        ("滚动计算", test_rolling_calculation)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"{test_name}测试出错: {e}")
            results[test_name] = False
    
    # 总结测试结果
    print(f"\n{'='*50}")
    print("测试结果总结:")
    print(f"{'='*50}")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    
    if all_passed:
        print(f"\n🎉 所有测试通过！可以运行完整版模型检验。")
        print(f"\n建议运行顺序:")
        print(f"1. 运行 06_模型有效性检验_完整版.ipynb")
        print(f"2. 检查生成的图表和统计表格")
        print(f"3. 分析模型有效性结果")
    else:
        print(f"\n⚠️ 部分测试失败，请检查数据和前序步骤。")
    
    return all_passed

if __name__ == "__main__":
    main()
