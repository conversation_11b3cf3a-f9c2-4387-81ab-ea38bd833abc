# Barra模型运行指南

## 🚀 快速开始

### 1. 数据准备
确保以下数据文件存在于 `data/` 目录下：
- `factor.h5` - 原始因子数据（PB、市值）
- `merged_data_871.h5` - 股票收益数据
- `swind.xlsx` - 行业分类数据
- `ipodate.csv` - 上市退市日期
- `index.csv` - 沪深300指数数据

### 2. 运行顺序

按以下顺序依次运行notebook：

#### 第1步：数据准备与检查
```
01_数据准备与检查_新版.ipynb
```
- 加载原始因子数据
- 数据格式统一
- 筛选有效股票样本
- 输出：`processed_data_step1.h5`

#### 第2步：因子数据处理
```
02_因子数据处理_新版.ipynb
```
- 处理原始因子数据
- 因子标准化
- 计算权重
- 输出：`processed_data_step2.h5`

#### 第3步：构建因子暴露矩阵
```
03_构建因子暴露矩阵_新版.ipynb
```
- 创建行业因子哑变量
- 处理多重共线性问题
- 输出：`因子暴露矩阵X.csv`, `processed_data_step3.h5`

#### 第4步：计算被解释变量
```
04_计算被解释变量_新版.ipynb
```
- 计算超额收益率
- 处理极端值
- 数据质量检查
- 输出：`被解释变量Y.csv`, `processed_data_step4.h5`

#### 第5步：横截面回归模型估计
```
05_横截面回归模型估计_最终版.ipynb
```
- 执行每日横截面回归
- 处理矩阵条件数问题
- 输出：`日度因子收益.csv`, `回归结果详细.csv`

#### 第6步：模型有效性检验
```
06_模型有效性检验.ipynb
```
- 模型解释力检验
- 因子有效性检验
- IC分析和分组收益检验
- 输出：`model_validation_report.json`

## ⚠️ 注意事项

### 数据要求
1. **使用原始因子数据**：必须使用 `factor.h5` 中的原始数据，不要使用预处理的标准化数据
2. **数据完整性**：确保所有必要的数据文件都存在且格式正确
3. **内存要求**：建议至少8GB内存，数据量较大时可能需要更多

### 常见问题解决

#### 1. 回归失败问题
- **症状**：第5步显示"LinearRegression does not accept missing values"
- **原因**：数据中存在NaN值或权重计算失败
- **解决**：检查前4步的数据处理是否正确，确保无缺失值

#### 2. 矩阵条件数过大
- **症状**：回归结果显示条件数 > 1e12
- **原因**：多重共线性问题
- **解决**：已在第3步中处理，移除了一个行业因子作为基准

#### 3. 权重计算失败
- **症状**：权重为NaN或负值
- **原因**：使用了标准化后的负值市值数据
- **解决**：确保使用原始市值数据计算权重

### 性能优化建议

1. **并行计算**：第5步和第6步的计算较为耗时，可以考虑减少样本日期进行测试
2. **内存管理**：及时清理不需要的变量，避免内存溢出
3. **分批处理**：对于大数据集，可以分批处理交叉验证等计算密集型任务

## 📊 预期结果

### 模型性能指标
- **回归成功率**：应达到95%以上
- **平均R²**：通常在0.08-0.15之间
- **条件数**：应小于1e12
- **因子显著性**：主要风格因子应通过显著性检验

### 输出文件检查
运行完成后，检查以下关键输出：
- `日度因子收益.csv` - 因子收益时间序列
- `因子暴露矩阵X.csv` - 完整的因子暴露矩阵
- `被解释变量Y.csv` - 超额收益率和权重
- `model_validation_report.json` - 模型验证报告

## 🔧 故障排除

### 如果遇到问题：
1. **检查数据路径**：确保所有数据文件路径正确
2. **重新运行前序步骤**：问题通常出现在数据处理环节
3. **查看错误信息**：仔细阅读错误提示，通常会指出具体问题
4. **检查内存使用**：大数据集可能导致内存不足

### 调试技巧：
1. **逐步运行**：不要一次性运行整个notebook，逐个cell执行
2. **检查中间结果**：验证每步的输出是否符合预期
3. **数据抽样**：可以先用小样本测试流程是否正确

## ✅ 验证清单

运行完成后，请检查：
- [ ] 所有6个notebook都成功运行
- [ ] 生成了所有预期的输出文件
- [ ] 回归成功率 > 95%
- [ ] 因子收益序列无异常值
- [ ] 模型验证报告生成成功

---

**如有问题，请检查各步骤的输出和错误信息，大部分问题都可以通过重新运行前序步骤解决。**
