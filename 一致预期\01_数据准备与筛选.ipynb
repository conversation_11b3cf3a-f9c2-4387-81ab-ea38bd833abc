{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 01_数据准备与筛选\n", "\n", "本notebook负责：\n", "1. 加载原始数据\n", "2. 数据清洗和预处理\n", "3. 样本池筛选\n", "4. 保存清洗后的数据供后续分析使用"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境设置完成\n"]}], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import warnings\n", "import os\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"环境设置完成\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在加载数据文件...\n", "分析师预测数据加载成功，形状: (3164356, 32)\n", "行业分类数据加载成功，形状: (7827, 10)\n", "IPO数据加载成功，形状: (5653, 3)\n", "日频数据加载成功，形状: (13020585, 9)\n", "列名: ['open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount']\n", "索引: ['trade_date', 'ts_code']\n", "数据加载完成\n"]}], "source": ["# 加载数据文件\n", "print(\"正在加载数据文件...\")\n", "\n", "try:\n", "    df_forecast = pd.read_feather('data/rpt_forecast_stk.feather')\n", "    print(f\"分析师预测数据加载成功，形状: {df_forecast.shape}\")\n", "except Exception as e:\n", "    print(f\"加载分析师预测数据失败: {e}\")\n", "\n", "try:\n", "    df_industry = pd.read_excel('data/swind.xlsx')\n", "    print(f\"行业分类数据加载成功，形状: {df_industry.shape}\")\n", "except Exception as e:\n", "    print(f\"加载行业分类数据失败: {e}\")\n", "\n", "try:\n", "    df_ipo = pd.read_csv('data/ipodate.csv')\n", "    print(f\"IPO数据加载成功，形状: {df_ipo.shape}\")\n", "except Exception as e:\n", "    print(f\"加载IPO数据失败: {e}\")\n", "    df_ipo = None\n", "\n", "# 4. 加载日频数据（用于收益率筛选）\n", "try:\n", "    df_daily = pd.read_hdf('data/daily0925.h5', 'data')\n", "    print(f\"日频数据加载成功，形状: {df_daily.shape}\")\n", "    print(f\"列名: {list(df_daily.columns)}\")\n", "    print(f\"索引: {df_daily.index.names}\")\n", "except Exception as e:\n", "    print(f\"加载日频数据失败: {e}\")\n", "    df_daily = None\n", "\n", "# 5. 加载复权因子数据\n", "try:\n", "    df_adjfactor = pd.read_hdf('data/adjfactor.hd5', 'data')\n", "    print(f\"复权因子数据加载成功，形状: {df_adjfactor.shape}\")\n", "    print(f\"列名: {list(df_adjfactor.columns)}\")\n", "    print(f\"索引: {df_adjfactor.index.names}\")\n", "except Exception as e:\n", "    print(f\"加载复权因子数据失败: {e}\")\n", "    df_adjfactor = None\n", "\n", "print(\"数据加载完成\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据预处理 ===\n", "预处理完成，数据形状: (3164356, 32)\n"]}], "source": ["# 数据预处理\n", "print(\"=== 数据预处理 ===\")\n", "\n", "# 字段映射\n", "forecast_mapping = {\n", "    'S_INFO_WINDCODE': 'stock_code',\n", "    'author_name': 'analyst_id',\n", "    'organ_id': 'institution_id',\n", "    'create_date': 'create_date',\n", "    'entrytime': 'entry_date',\n", "    'forecast_np': 'forecast_profit',\n", "    'report_year': 'forecast_year'\n", "}\n", "\n", "df_forecast_clean = df_forecast.copy()\n", "df_forecast_clean = df_forecast_clean.rename(columns=forecast_mapping)\n", "\n", "# 数据类型转换\n", "df_forecast_clean['create_date'] = pd.to_datetime(df_forecast_clean['create_date'].astype(str), format='%Y%m%d')\n", "df_forecast_clean['entry_date'] = pd.to_datetime(df_forecast_clean['entry_date'])\n", "\n", "print(f\"预处理完成，数据形状: {df_forecast_clean.shape}\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 行业分类数据预处理 ===\n", "行业分类数据预处理完成，形状: (7827, 5)\n", "一级行业数量: 31\n", "\n", "=== 处理后的行业数据样本 ===\n", "  stock_code       name    in_date out_date industry_l1\n", "0  000001.<PERSON><PERSON>       平安银行 1991-04-03      NaT          银行\n", "1  000002.SZ        万科A 1991-01-29      NaT         房地产\n", "2  000003.SZ  PT金田A(退市) 1991-04-15      NaT          综合\n", "3  000004.SZ      *ST国华 1989-12-23      NaT         房地产\n", "4  000004.SZ      *ST国华 2008-12-31      NaT        医药生物\n"]}], "source": ["# 行业分类数据预处理\n", "print(\"=== 行业分类数据预处理 ===\")\n", "\n", "# 统一股票代码格式\n", "df_industry_clean = df_industry.copy()\n", "df_industry_clean['stock_code'] = df_industry_clean['ts_code']\n", "\n", "# 选择需要的列\n", "industry_cols = ['stock_code', 'name', 'in_date', 'out_date', 'l1_name']\n", "df_industry_clean = df_industry_clean[industry_cols].copy()\n", "df_industry_clean = df_industry_clean.rename(columns={'l1_name': 'industry_l1'})\n", "\n", "# 转换日期格式\n", "df_industry_clean['in_date'] = pd.to_datetime(df_industry_clean['in_date'].astype(str), format='%Y%m%d')\n", "df_industry_clean['out_date'] = pd.to_datetime(df_industry_clean['out_date'].astype(str), format='%Y%m%d', errors='coerce')\n", "\n", "print(f\"行业分类数据预处理完成，形状: {df_industry_clean.shape}\")\n", "print(f\"一级行业数量: {df_industry_clean['industry_l1'].nunique()}\")\n", "\n", "# 查看处理后的数据\n", "print(\"\\n=== 处理后的行业数据样本 ===\")\n", "print(df_industry_clean.head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 样本池筛选\n", "\n", "根据研报要求，需要排除：\n", "1. 上市不满60交易日的股票\n", "2. ST/ST摘帽不满60交易日的股票  \n", "3. 简评文章、港股报告等无效报告类型"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 报告类型筛选 ===\n", "原始数据报告类型分布:\n", "report_type\n", "21      826386\n", "22      261103\n", "23      140032\n", "24       51338\n", "25     1854178\n", "26       19860\n", "98        1439\n", "110       9998\n", "124          3\n", "125         19\n", "Name: count, dtype: int64\n", "\n", "筛选前数据量: 3164356\n", "筛选后数据量: 3133037\n", "保留比例: 99.01%\n", "\n", "筛选后报告类型分布:\n", "report_type\n", "21     826386\n", "22     261103\n", "23     140032\n", "24      51338\n", "25    1854178\n", "Name: count, dtype: int64\n"]}], "source": ["# 报告类型筛选\n", "print(\"=== 报告类型筛选 ===\")\n", "print(\"原始数据报告类型分布:\")\n", "report_type_dist = df_forecast_clean['report_type'].value_counts().sort_index()\n", "print(report_type_dist)\n", "\n", "# 根据研报要求，保留有效的报告类型\n", "# 研报提到保留：个股报告、深度报告、调研报告、点评报告、新股研究\n", "# 剔除：简评文章、港股报告\n", "# 根据数据分布，保留主要的报告类型：21, 22, 23, 24, 25\n", "valid_report_types = [21, 22, 23, 24, 25]  # 保留主要报告类型\n", "\n", "df_forecast_filtered = df_forecast_clean[df_forecast_clean['report_type'].isin(valid_report_types)].copy()\n", "\n", "print(f\"\\n筛选前数据量: {len(df_forecast_clean)}\")\n", "print(f\"筛选后数据量: {len(df_forecast_filtered)}\")\n", "print(f\"保留比例: {len(df_forecast_filtered) / len(df_forecast_clean) * 100:.2f}%\")\n", "\n", "print(\"\\n筛选后报告类型分布:\")\n", "print(df_forecast_filtered['report_type'].value_counts().sort_index())"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 上市时间筛选 ===\n", "成功匹配IPO信息的记录数: 3128053\n", "IPO信息匹配率: 99.84%\n", "\n", "上市时间筛选结果:\n", "筛选前: 3133037\n", "筛选后: 3112638\n", "剔除数量: 20399\n", "剔除比例: 0.65%\n"]}], "source": ["# 上市时间筛选（剔除上市不满60交易日的股票）\n", "print(\"=== 上市时间筛选 ===\")\n", "\n", "if df_ipo is not None and df_daily is not None:\n", "    # 处理IPO数据的日期格式\n", "    df_ipo['list_date'] = pd.to_datetime(df_ipo['list_date'], format='%Y/%m/%d', errors='coerce')\n", "    df_ipo['delist_date'] = pd.to_datetime(df_ipo['delist_date'], format='%Y/%m/%d', errors='coerce')\n", "    \n", "    # 获取交易日历\n", "    trade_dates = pd.to_datetime(df_daily.index.get_level_values('trade_date').unique()).sort_values()\n", "    print(f\"交易日历范围: {trade_dates.min()} 到 {trade_dates.max()}\")\n", "    \n", "    # 合并IPO数据\n", "    df_forecast_with_ipo = df_forecast_filtered.merge(\n", "        df_ipo[['ts_code', 'list_date']], \n", "        left_on='stock_code', \n", "        right_on='ts_code', \n", "        how='left'\n", "    )\n", "    \n", "    print(f\"成功匹配IPO信息的记录数: {df_forecast_with_ipo['list_date'].notna().sum()}\")\n", "    print(f\"IPO信息匹配率: {df_forecast_with_ipo['list_date'].notna().sum() / len(df_forecast_with_ipo) * 100:.2f}%\")\n", "    \n", "    # 计算上市交易日数\n", "    def calculate_trading_days(list_date, create_date, trade_dates):\n", "        if pd.isna(list_date) or pd.isna(create_date):\n", "            return None\n", "        # 找到上市日期之后且在创建日期之前的交易日\n", "        valid_dates = trade_dates[(trade_dates >= list_date) & (trade_dates <= create_date)]\n", "        return len(valid_dates) - 1  # 减1是因为上市当天不算\n", "    \n", "    print(\"正在计算交易日数...\")\n", "    df_forecast_with_ipo['trading_days_since_ipo'] = df_forecast_with_ipo.apply(\n", "        lambda row: calculate_trading_days(row['list_date'], row['create_date'], trade_dates),\n", "        axis=1\n", "    )\n", "    \n", "    # 筛选上市满60个交易日的股票\n", "    before_ipo_filter = len(df_forecast_with_ipo)\n", "    \n", "    # 保留上市满60个交易日的记录，或者没有IPO信息的记录（可能是老股票）\n", "    df_forecast_with_ipo = df_forecast_with_ipo[\n", "        (df_forecast_with_ipo['trading_days_since_ipo'] >= 60) | \n", "        (df_forecast_with_ipo['list_date'].isna())\n", "    ]\n", "    \n", "    after_ipo_filter = len(df_forecast_with_ipo)\n", "    \n", "    print(f\"\\n上市时间筛选结果（按交易日计算）:\")\n", "    print(f\"筛选前: {before_ipo_filter}\")\n", "    print(f\"筛选后: {after_ipo_filter}\")\n", "    print(f\"剔除数量: {before_ipo_filter - after_ipo_filter}\")\n", "    print(f\"剔除比例: {(before_ipo_filter - after_ipo_filter) / before_ipo_filter * 100:.2f}%\")\n", "    \n", "    df_forecast_filtered = df_forecast_with_ipo.copy()\n", "    \n", "else:\n", "    print(\"IPO数据或日频数据未加载，跳过上市时间筛选\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ST状态筛选 ===\n", "识别到的ST股票数量: 286\n", "ST股票样本: ['000004.SZ' '000005.SZ' '000013.SZ' '000023.SZ' '000040.SZ' '000046.SZ'\n", " '000047.SZ' '000150.SZ' '000405.SZ' '000412.SZ']\n", "预测数据中涉及ST股票的记录数: 73828\n", "\n", "ST状态筛选结果:\n", "筛选前: 3112638\n", "筛选后: 3038810\n", "剔除数量: 73828\n", "剔除比例: 2.37%\n"]}], "source": ["# ST状态筛选（剔除ST股票）\n", "print(\"=== ST状态筛选 ===\")\n", "\n", "# 从行业数据中识别ST股票\n", "st_stocks = df_industry_clean[df_industry_clean['name'].str.contains('ST|\\\\*ST', na=False, regex=True)]['stock_code'].unique()\n", "print(f\"识别到的ST股票数量: {len(st_stocks)}\")\n", "\n", "if len(st_stocks) > 0:\n", "    print(f\"ST股票样本: {st_stocks[:10]}\")\n", "    \n", "    # 统计预测数据中涉及的ST股票\n", "    st_records_count = df_forecast_filtered[df_forecast_filtered['stock_code'].isin(st_stocks)].shape[0]\n", "    print(f\"预测数据中涉及ST股票的记录数: {st_records_count}\")\n", "    \n", "    # 剔除ST股票的预测记录\n", "    before_st_filter = len(df_forecast_filtered)\n", "    df_forecast_filtered = df_forecast_filtered[~df_forecast_filtered['stock_code'].isin(st_stocks)]\n", "    after_st_filter = len(df_forecast_filtered)\n", "    \n", "    print(f\"\\nST状态筛选结果:\")\n", "    print(f\"筛选前: {before_st_filter}\")\n", "    print(f\"筛选后: {after_st_filter}\")\n", "    print(f\"剔除数量: {before_st_filter - after_st_filter}\")\n", "    print(f\"剔除比例: {(before_st_filter - after_st_filter) / before_st_filter * 100:.2f}%\")\n", "else:\n", "    print(\"未识别到ST股票\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 日收益率异常筛选\n", "\n", "剔除日收益率绝对值大于30%的股票相关数据。这类股票通常存在：\n", "- 停牌复牌导致的异常波动\n", "- 重大事件冲击\n", "- 数据质量问题\n", "- 流动性极差的小盘股\n", "\n", "剔除这些股票有助于提高分析师预测数据的质量和稳定性。"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 日收益率异常筛选 ===\n", "处理日频数据...\n", "识别异常日收益率记录...\n", "发现异常日收益率记录数: 8,564\n", "涉及股票数: 3,565\n", "异常记录样本:\n", "  2009-01-05 00:00:00 600242.SH: -26.48%\n", "  2009-01-23 00:00:00 000668.SZ: -27.02%\n", "  2009-02-27 00:00:00 600892.SH: 56.70%\n", "  2009-03-04 00:00:00 600800.SH: 46.72%\n", "  2009-04-08 00:00:00 000892.SZ: 70.52%\n", "  2009-04-09 00:00:00 000536.SZ: 73.67%\n", "  2009-04-17 00:00:00 600187.SH: 1004.08%\n", "  2009-04-30 00:00:00 000918.SZ: 83.40%\n", "  2009-04-30 00:00:00 000925.SZ: -26.04%\n", "  2009-06-05 00:00:00 000506.SZ: 93.69%\n", "异常日期-股票组合数: 8,564\n", "\n", "匹配到需要剔除的预测记录数: 138\n", "被剔除的预测记录已保存到 processed_data/removed_extreme_return_records.csv\n", "成功剔除 138 条预测记录\n", "异常日收益率记录已保存到 processed_data/extreme_return_dates.csv\n", "\n", "日收益率异常筛选结果:\n", "筛选前: 3,038,810\n", "筛选后: 3,038,672\n", "剔除数量: 138\n", "剔除比例: 0.00%\n"]}], "source": ["# 日收益率异常筛选（剔除特定日期日收益率大于30%的预测数据）\n", "print(\"=== 日收益率异常筛选 ===\")\n", "\n", "before_return_filter = len(df_forecast_filtered)\n", "\n", "if df_daily is not None:\n", "    try:\n", "        print(\"处理日频数据...\")\n", "        \n", "        # 确保日频数据有pct_chg字段\n", "        if 'pct_chg' not in df_daily.columns:\n", "            print(\"计算日收益率...\")\n", "            df_daily_sorted = df_daily.sort_index()\n", "            df_daily['pct_chg'] = df_daily_sorted.groupby('ts_code')['close'].pct_change() * 100\n", "        \n", "        # 重置索引以便操作\n", "        df_daily_reset = df_daily.reset_index()\n", "        \n", "        # 找出日收益率绝对值大于30%的记录\n", "        print(\"识别异常日收益率记录...\")\n", "        extreme_mask = df_daily_reset['pct_chg'].abs() > 21\n", "        extreme_records = df_daily_reset[extreme_mask].copy()\n", "        \n", "        print(f\"发现异常日收益率记录数: {len(extreme_records):,}\")\n", "        \n", "        if len(extreme_records) > 0:\n", "            print(f\"涉及股票数: {extreme_records['ts_code'].nunique():,}\")\n", "            \n", "            # 显示异常记录样本\n", "            print(\"异常记录样本:\")\n", "            sample_records = extreme_records[['trade_date', 'ts_code', 'pct_chg']].head(10)\n", "            for _, row in sample_records.iterrows():\n", "                print(f\"  {row['trade_date']} {row['ts_code']}: {row['pct_chg']:.2f}%\")\n", "            \n", "            # 转换日期格式以便匹配\n", "            extreme_records['trade_date_str'] = pd.to_datetime(extreme_records['trade_date']).dt.strftime('%Y-%m-%d')\n", "            \n", "            # 创建异常日期-股票的组合键\n", "            extreme_records['date_stock_key'] = extreme_records['trade_date_str'] + '_' + extreme_records['ts_code']\n", "            extreme_keys = set(extreme_records['date_stock_key'])\n", "            \n", "            print(f\"异常日期-股票组合数: {len(extreme_keys):,}\")\n", "            \n", "            # 在预测数据中创建对应的键\n", "            df_forecast_filtered['forecast_date_str'] = df_forecast_filtered['create_date'].dt.strftime('%Y-%m-%d')\n", "            df_forecast_filtered['forecast_date_stock_key'] = df_forecast_filtered['forecast_date_str'] + '_' + df_forecast_filtered['stock_code']\n", "            \n", "            # 找出需要剔除的预测记录\n", "            mask_to_remove = df_forecast_filtered['forecast_date_stock_key'].isin(extreme_keys)\n", "            records_to_remove = mask_to_remove.sum()\n", "            \n", "            print(f\"\\n匹配到需要剔除的预测记录数: {records_to_remove:,}\")\n", "            \n", "            if records_to_remove > 0:\n", "                # 保存被剔除的记录\n", "                removed_records = df_forecast_filtered[mask_to_remove][[\n", "                    'stock_code', 'create_date', 'analyst_id', 'forecast_profit', 'forecast_year'\n", "                ]].copy()\n", "                removed_records.to_csv('processed_data/removed_extreme_return_records.csv', \n", "                                     index=False, encoding='utf-8-sig')\n", "                print(f\"被剔除的预测记录已保存到 processed_data/removed_extreme_return_records.csv\")\n", "                \n", "                # 执行剔除\n", "                df_forecast_filtered = df_forecast_filtered[~mask_to_remove].copy()\n", "                print(f\"成功剔除 {records_to_remove:,} 条预测记录\")\n", "            \n", "            # 清理临时列\n", "            df_forecast_filtered = df_forecast_filtered.drop(['forecast_date_str', 'forecast_date_stock_key'], axis=1)\n", "            \n", "            # 保存异常日期记录\n", "            extreme_summary = extreme_records[['trade_date', 'ts_code', 'pct_chg']].copy()\n", "            extreme_summary.to_csv('processed_data/extreme_return_dates.csv', \n", "                                 index=False, encoding='utf-8-sig')\n", "            print(f\"异常日收益率记录已保存到 processed_data/extreme_return_dates.csv\")\n", "            \n", "        else:\n", "            print(\"未发现异常日收益率记录\")\n", "            \n", "    except Exception as e:\n", "        print(f\"日收益率筛选过程中出现错误: {e}\")\n", "        print(\"跳过日收益率筛选\")\n", "        \n", "else:\n", "    print(\"日频数据未加载，跳过日收益率异常筛选\")\n", "\n", "after_return_filter = len(df_forecast_filtered)\n", "\n", "print(f\"\\n日收益率异常筛选结果:\")\n", "print(f\"筛选前: {before_return_filter:,}\")\n", "print(f\"筛选后: {after_return_filter:,}\")\n", "print(f\"剔除数量: {before_return_filter - after_return_filter:,}\")\n", "if before_return_filter > 0:\n", "    print(f\"剔除比例: {(before_return_filter - after_return_filter) / before_return_filter * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据质量筛选 ===\n", "筛选前各字段缺失情况:\n", "stock_code: 0 (0.00%)\n", "create_date: 0 (0.00%)\n", "forecast_profit: 78479 (2.58%)\n", "analyst_id: 56 (0.00%)\n", "forecast_year: 7111 (0.23%)\n", "\n", "关键字段缺失筛选:\n", "筛选前: 3038672\n", "筛选后: 2960138\n", "剔除数量: 78534\n", "\n", "=== 异常值筛选 ===\n", "剔除净利润为0的记录: 239 条\n", "极端值筛选（1%-99%分位数）:\n", "下界: 2.20e+07, 上界: 7.68e+10\n", "剔除极端值记录: 59131 条\n", "\n", "数据质量筛选总结果:\n", "筛选前: 3038672\n", "筛选后: 2900768\n", "总剔除比例: 4.54%\n"]}], "source": ["# 数据质量筛选\n", "print(\"=== 数据质量筛选 ===\")\n", "\n", "# 1. 剔除关键字段缺失的数据\n", "print(\"筛选前各字段缺失情况:\")\n", "key_fields = ['stock_code', 'create_date', 'forecast_profit', 'analyst_id', 'forecast_year']\n", "for field in key_fields:\n", "    missing_count = df_forecast_filtered[field].isna().sum()\n", "    missing_pct = missing_count / len(df_forecast_filtered) * 100\n", "    print(f\"{field}: {missing_count} ({missing_pct:.2f}%)\")\n", "\n", "# 剔除关键字段缺失的记录\n", "before_missing_filter = len(df_forecast_filtered)\n", "df_forecast_filtered = df_forecast_filtered.dropna(subset=['stock_code', 'create_date', 'forecast_profit', 'analyst_id'])\n", "after_missing_filter = len(df_forecast_filtered)\n", "\n", "print(f\"\\n关键字段缺失筛选:\")\n", "print(f\"筛选前: {before_missing_filter}\")\n", "print(f\"筛选后: {after_missing_filter}\")\n", "print(f\"剔除数量: {before_missing_filter - after_missing_filter}\")\n", "\n", "# 2. 剔除异常的预测净利润值\n", "print(f\"\\n=== 异常值筛选 ===\")\n", "before_outlier_filter = len(df_forecast_filtered)\n", "\n", "# 剔除预测净利润为0的记录\n", "df_forecast_filtered = df_forecast_filtered[df_forecast_filtered['forecast_profit'] != 0]\n", "print(f\"剔除净利润为0的记录: {before_outlier_filter - len(df_forecast_filtered)} 条\")\n", "\n", "# 剔除极端值（使用分位数方法，更稳健）\n", "q1 = df_forecast_filtered['forecast_profit'].quantile(0.01)\n", "q99 = df_forecast_filtered['forecast_profit'].quantile(0.99)\n", "\n", "before_extreme_filter = len(df_forecast_filtered)\n", "df_forecast_filtered = df_forecast_filtered[\n", "    (df_forecast_filtered['forecast_profit'] >= q1) & \n", "    (df_forecast_filtered['forecast_profit'] <= q99)\n", "]\n", "after_extreme_filter = len(df_forecast_filtered)\n", "\n", "print(f\"极端值筛选（1%-99%分位数）:\")\n", "print(f\"下界: {q1:.2e}, 上界: {q99:.2e}\")\n", "print(f\"剔除极端值记录: {before_extreme_filter - after_extreme_filter} 条\")\n", "\n", "print(f\"\\n数据质量筛选总结果:\")\n", "print(f\"筛选前: {before_missing_filter}\")\n", "print(f\"筛选后: {len(df_forecast_filtered)}\")\n", "print(f\"总剔除比例: {(before_missing_filter - len(df_forecast_filtered)) / before_missing_filter * 100:.2f}%\")\n", "\n", "# 保存数据质量筛选后的数量\n", "after_missing_filter = len(df_forecast_filtered)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 样本池筛选总结 ===\n", "          筛选步骤      数据量    剔除数量  剔除比例(%)  累计保留比例(%)\n", "0      1. 原始数据  3164356       0     0.00     100.00\n", "1    2. 报告类型筛选  3133037   31319     0.99      99.01\n", "2    3. 上市时间筛选  3112638   20399     0.65      98.37\n", "3    4. ST状态筛选  3038810   73828     2.37      96.03\n", "4  5. 日收益率异常筛选  3038672     138     0.00      96.03\n", "5    6. 数据质量筛选  2900768  137904     4.54      91.67\n", "6      7. 最终样本  2900768       0     0.00      91.67\n", "\n", "=== 最终样本概况 ===\n", "最终数据量: 2,900,768\n", "涉及股票数量: 4,960\n", "涉及分析师数量: 50,719\n", "涉及机构数量: 154\n", "时间跨度: 2012-01-01 到 2025-07-13\n", "数据保留率: 91.67%\n"]}], "source": ["# 筛选总结\n", "print(\"=== 样本池筛选总结 ===\")\n", "\n", "# 创建筛选汇总表\n", "filtering_summary = pd.DataFrame({\n", "    '筛选步骤': [\n", "        '1. 原始数据',\n", "        '2. 报告类型筛选',\n", "        '3. 上市时间筛选',\n", "        '4. ST状态筛选',\n", "        '5. 日收益率异常筛选',\n", "        '6. 数据质量筛选',\n", "        '7. 最终样本'\n", "    ],\n", "    '数据量': [\n", "        len(df_forecast_clean),  # 原始数据\n", "        len(df_forecast_clean[df_forecast_clean['report_type'].isin(valid_report_types)]),  # 报告类型筛选后\n", "        after_ipo_filter if df_ipo is not None else len(df_forecast_clean[df_forecast_clean['report_type'].isin(valid_report_types)]),  # 上市时间筛选后\n", "        after_st_filter if len(st_stocks) > 0 else (after_ipo_filter if df_ipo is not None else len(df_forecast_clean[df_forecast_clean['report_type'].isin(valid_report_types)])),  # ST筛选后\n", "        after_return_filter,  # 日收益率筛选后\n", "        after_missing_filter,  # 缺失值筛选后\n", "        len(df_forecast_filtered)  # 最终样本\n", "    ]\n", "})\n", "\n", "# 计算剔除数量和比例\n", "filtering_summary['剔除数量'] = filtering_summary['数据量'].diff().fillna(0) * -1\n", "filtering_summary['剔除数量'] = filtering_summary['剔除数量'].astype(int)\n", "filtering_summary['剔除比例(%)'] = (filtering_summary['剔除数量'] / filtering_summary['数据量'].shift(1) * 100).round(2)\n", "filtering_summary['累计保留比例(%)'] = (filtering_summary['数据量'] / filtering_summary['数据量'].iloc[0] * 100).round(2)\n", "\n", "# 修正第一行\n", "filtering_summary.loc[0, '剔除数量'] = 0\n", "filtering_summary.loc[0, '剔除比例(%)'] = 0\n", "\n", "print(filtering_summary)\n", "print(f\"\\n=== 最终样本概况 ===\")\n", "print(f\"最终数据量: {len(df_forecast_filtered):,}\")\n", "print(f\"涉及股票数量: {df_forecast_filtered['stock_code'].nunique():,}\")\n", "print(f\"涉及分析师数量: {df_forecast_filtered['analyst_id'].nunique():,}\")\n", "print(f\"涉及机构数量: {df_forecast_filtered['institution_id'].nunique():,}\")\n", "print(f\"时间跨度: {df_forecast_filtered['create_date'].min().strftime('%Y-%m-%d')} 到 {df_forecast_filtered['create_date'].max().strftime('%Y-%m-%d')}\")\n", "print(f\"数据保留率: {len(df_forecast_filtered) / len(df_forecast_clean) * 100:.2f}%\")"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 保存处理后的数据 ===\n", "数据字典已保存到 processed_data/data_dictionary.json\n", "\n", "数据准备与筛选完成！\n", "最终数据量: 2,900,768\n", "涉及股票数量: 4,960\n"]}], "source": ["# 保存处理后的数据\n", "print(\"=== 保存处理后的数据 ===\")\n", "\n", "os.makedirs('processed_data', exist_ok=True)\n", "\n", "# 保存筛选后的预测数据\n", "df_forecast_filtered.to_feather('processed_data/forecast_data_filtered.feather')\n", "df_forecast_filtered.to_csv('processed_data/forecast_data_filtered.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 保存行业数据\n", "df_industry_clean.to_feather('processed_data/industry_data_clean.feather')\n", "df_industry_clean.to_csv('processed_data/industry_data_clean.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 保存IPO数据\n", "if df_ipo is not None:\n", "    df_ipo.to_feather('processed_data/ipo_data_clean.feather')\n", "    df_ipo.to_csv('processed_data/ipo_data_clean.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 保存筛选汇总\n", "filtering_summary.to_csv('processed_data/filtering_summary.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 保存数据字典\n", "data_dict = {\n", "    'forecast_data_filtered': {\n", "        'description': '筛选后的分析师预测数据',\n", "        'shape': df_forecast_filtered.shape,\n", "        'columns': list(df_forecast_filtered.columns),\n", "        'time_range': [df_forecast_filtered['create_date'].min().strftime('%Y-%m-%d'), \n", "                      df_forecast_filtered['create_date'].max().strftime('%Y-%m-%d')],\n", "        'stock_count': df_forecast_filtered['stock_code'].nunique(),\n", "        'analyst_count': df_forecast_filtered['analyst_id'].nunique()\n", "    },\n", "    'industry_data_clean': {\n", "        'description': '行业分类数据',\n", "        'shape': df_industry_clean.shape,\n", "        'columns': list(df_industry_clean.columns)\n", "    },\n", "    'filtering_summary': {\n", "        'description': '样本池筛选汇总',\n", "        'final_retention_rate': f\"{len(df_forecast_filtered) / len(df_forecast_clean) * 100:.2f}%\"\n", "    }\n", "}\n", "\n", "with open('processed_data/data_dictionary.json', 'w', encoding='utf-8') as f:\n", "    json.dump(data_dict, f, ensure_ascii=False, indent=2, default=str)\n", "print(\"数据字典已保存到 processed_data/data_dictionary.json\")\n", "\n", "print(\"\\n数据准备与筛选完成！\")\n", "print(f\"最终数据量: {len(df_forecast_filtered):,}\")\n", "print(f\"涉及股票数量: {df_forecast_filtered['stock_code'].nunique():,}\")"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 生成一致预期净利润覆盖度图 ===\n", "覆盖度统计完成，共163个月份\n", "覆盖度范围: 15.0% - 66.8%\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["覆盖度图已保存到 processed_data/consensus_profit_coverage.png\n", "覆盖度数据已保存到 processed_data/consensus_profit_coverage.csv\n"]}], "source": ["# 生成一致预期净利润覆盖度图\n", "print(\"=== 生成一致预期净利润覆盖度图 ===\")\n", "\n", "# 按月统计覆盖度\n", "df_forecast_filtered['year_month'] = df_forecast_filtered['create_date'].dt.to_period('M')\n", "\n", "# 计算每月的股票覆盖度\n", "monthly_coverage = []\n", "\n", "for period in df_forecast_filtered['year_month'].unique():\n", "    period_data = df_forecast_filtered[df_forecast_filtered['year_month'] == period]\n", "    \n", "    # 当月有预测的股票数\n", "    covered_stocks = period_data['stock_code'].nunique()\n", "    \n", "    # 当月全市场股票数（从日频数据获取）\n", "    if df_daily is not None:\n", "        period_start = period.start_time\n", "        period_end = period.end_time\n", "        \n", "        # 获取该月的交易日\n", "        month_dates = df_daily.index.get_level_values('trade_date')\n", "        month_dates = pd.to_datetime(month_dates)\n", "        month_mask = (month_dates >= period_start) & (month_dates <= period_end)\n", "        \n", "        if month_mask.any():\n", "            # 取该月最后一个交易日的股票数\n", "            last_date = month_dates[month_mask].max()\n", "            total_stocks = df_daily.loc[df_daily.index.get_level_values('trade_date') == last_date.strftime('%Y%m%d')]['close'].notna().sum()\n", "        else:\n", "            total_stocks = 4000  # 默认值\n", "    else:\n", "        total_stocks = 4000  # 默认值\n", "    \n", "    coverage_rate = covered_stocks / total_stocks if total_stocks > 0 else 0\n", "    \n", "    monthly_coverage.append({\n", "        'period': period,\n", "        'covered_stocks': covered_stocks,\n", "        'total_stocks': total_stocks,\n", "        'coverage_rate': coverage_rate\n", "    })\n", "\n", "coverage_df = pd.DataFrame(monthly_coverage)\n", "coverage_df = coverage_df.sort_values('period')\n", "\n", "print(f\"覆盖度统计完成，共{len(coverage_df)}个月份\")\n", "print(f\"覆盖度范围: {coverage_df['coverage_rate'].min():.1%} - {coverage_df['coverage_rate'].max():.1%}\")\n", "\n", "# 绘制覆盖度图\n", "plt.figure(figsize=(12, 6))\n", "plt.plot(coverage_df['period'].astype(str), coverage_df['coverage_rate'] * 100, \n", "         linewidth=2, color='#FF6B35', marker='o', markersize=3)\n", "\n", "plt.title('图7：一致预期净利润覆盖度', fontsize=14, fontweight='bold', pad=20)\n", "plt.xlabel('时间', fontsize=12)\n", "plt.ylabel('覆盖度(%)', fontsize=12)\n", "plt.grid(True, alpha=0.3)\n", "\n", "# 设置y轴范围\n", "plt.ylim(0, 100)\n", "\n", "# 设置x轴标签（每年显示一次）\n", "x_labels = coverage_df['period'].astype(str)\n", "x_ticks = range(0, len(x_labels), 12)  # 每12个月显示一次\n", "plt.xticks(x_ticks, [x_labels.iloc[i] for i in x_ticks], rotation=45)\n", "\n", "plt.tight_layout()\n", "plt.savefig('processed_data/consensus_profit_coverage.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "# 保存覆盖度数据\n", "coverage_df.to_csv('processed_data/consensus_profit_coverage.csv', index=False, encoding='utf-8-sig')\n", "print(\"覆盖度图已保存到 processed_data/consensus_profit_coverage.png\")\n", "print(\"覆盖度数据已保存到 processed_data/consensus_profit_coverage.csv\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}