#!/usr/bin/env python3
"""
01 - 数据准备与筛选

从原始数据中筛选出符合条件的预测数据，包括：
1. 加载原始预测数据
2. 数据质量检查
3. 筛选条件应用
4. 生成筛选后的数据集

筛选条件：
- 预测期间：2019年6月至2020年6月
- 预测类型：年报预测（rpt_type=4）
- 预测指标：净利润（indicator_code=321001000）
- 数据完整性：必须有股票代码、预测日期、预测值
"""

import pandas as pd
import numpy as np
import warnings
from datetime import datetime

warnings.filterwarnings('ignore')

def load_raw_data():
    """加载原始预测数据"""
    print("=== 加载原始数据 ===")
    
    # 加载原始预测数据
    df_forecast = pd.read_feather('data/rpt_forecast_stk.feather')
    print(f"原始数据量: {len(df_forecast):,} 条记录")
    
    # 显示数据基本信息
    print(f"数据列: {list(df_forecast.columns)}")
    print(f"数据时间范围: {df_forecast['est_dt'].min()} 到 {df_forecast['est_dt'].max()}")
    print(f"唯一股票数: {df_forecast['s_info_windcode'].nunique()}")
    
    return df_forecast

def data_quality_check(df):
    """数据质量检查"""
    print("\n=== 数据质量检查 ===")
    
    # 检查关键列的缺失情况
    key_columns = ['s_info_windcode', 'est_dt', 'est_eps', 'rpt_type', 'indicator_code']
    
    for col in key_columns:
        if col in df.columns:
            missing_count = df[col].isnull().sum()
            missing_pct = missing_count / len(df) * 100
            print(f"{col}: 缺失 {missing_count:,} 条 ({missing_pct:.2f}%)")
        else:
            print(f"警告: 列 {col} 不存在")
    
    # 检查预测类型分布
    if 'rpt_type' in df.columns:
        print(f"\n预测类型分布:")
        print(df['rpt_type'].value_counts().sort_index())
    
    # 检查指标代码分布
    if 'indicator_code' in df.columns:
        print(f"\n指标代码分布:")
        print(df['indicator_code'].value_counts().head(10))

def apply_filters(df):
    """应用筛选条件"""
    print("\n=== 应用筛选条件 ===")
    
    original_count = len(df)
    print(f"筛选前数据量: {original_count:,}")
    
    # 1. 时间筛选：2019年6月至2020年6月
    df['est_dt'] = pd.to_datetime(df['est_dt'])
    df_filtered = df[
        (df['est_dt'] >= '2019-06-01') & 
        (df['est_dt'] <= '2020-06-30')
    ].copy()
    
    print(f"时间筛选后: {len(df_filtered):,} 条 (保留 {len(df_filtered)/original_count*100:.1f}%)")
    
    # 2. 预测类型筛选：年报预测（rpt_type=4）
    df_filtered = df_filtered[df_filtered['rpt_type'] == 4]
    print(f"年报预测筛选后: {len(df_filtered):,} 条")
    
    # 3. 指标筛选：净利润（indicator_code=321001000）
    df_filtered = df_filtered[df_filtered['indicator_code'] == 321001000]
    print(f"净利润指标筛选后: {len(df_filtered):,} 条")
    
    # 4. 数据完整性筛选
    df_filtered = df_filtered.dropna(subset=['s_info_windcode', 'est_dt', 'est_eps'])
    print(f"完整性筛选后: {len(df_filtered):,} 条")
    
    # 5. 移除异常值（预测值为0或负数的记录）
    df_filtered = df_filtered[df_filtered['est_eps'] > 0]
    print(f"异常值移除后: {len(df_filtered):,} 条")
    
    print(f"\n最终保留率: {len(df_filtered)/original_count*100:.2f}%")
    
    return df_filtered

def generate_summary_stats(df_filtered):
    """生成筛选后数据的统计信息"""
    print("\n=== 筛选后数据统计 ===")
    
    # 基本统计
    print(f"最终数据量: {len(df_filtered):,} 条")
    print(f"涉及股票数: {df_filtered['s_info_windcode'].nunique():,} 只")
    print(f"时间范围: {df_filtered['est_dt'].min().strftime('%Y-%m-%d')} 到 {df_filtered['est_dt'].max().strftime('%Y-%m-%d')}")
    
    # 预测值统计
    print(f"\n预测EPS统计:")
    print(f"  均值: {df_filtered['est_eps'].mean():.4f}")
    print(f"  中位数: {df_filtered['est_eps'].median():.4f}")
    print(f"  标准差: {df_filtered['est_eps'].std():.4f}")
    print(f"  最小值: {df_filtered['est_eps'].min():.4f}")
    print(f"  最大值: {df_filtered['est_eps'].max():.4f}")
    
    # 按月统计
    df_filtered['year_month'] = df_filtered['est_dt'].dt.to_period('M')
    monthly_stats = df_filtered.groupby('year_month').agg({
        's_info_windcode': 'nunique',
        'est_eps': 'count'
    }).rename(columns={'s_info_windcode': 'unique_stocks', 'est_eps': 'total_records'})
    
    print(f"\n按月统计:")
    print(monthly_stats)
    
    return monthly_stats

def save_filtered_data(df_filtered):
    """保存筛选后的数据"""
    print("\n=== 保存筛选后数据 ===")
    
    # 重命名列以便后续处理
    df_output = df_filtered.rename(columns={
        's_info_windcode': 'stock_code',
        'est_dt': 'prediction_date',
        'est_eps': 'predicted_eps'
    })
    
    # 选择需要的列
    output_columns = [
        'stock_code', 'prediction_date', 'predicted_eps', 
        'rpt_type', 'indicator_code'
    ]
    
    df_output = df_output[output_columns]
    
    # 保存为多种格式
    df_output.to_csv('processed_data/forecast_data_filtered.csv', index=False, encoding='utf-8-sig')
    df_output.to_feather('processed_data/forecast_data_filtered.feather')
    
    print(f"✅ 已保存筛选后数据:")
    print(f"  CSV格式: processed_data/forecast_data_filtered.csv")
    print(f"  Feather格式: processed_data/forecast_data_filtered.feather")
    print(f"  数据量: {len(df_output):,} 条")
    
    return df_output

def create_filtering_summary(original_count, final_count, monthly_stats):
    """创建筛选过程总结"""
    print("\n=== 生成筛选总结报告 ===")
    
    summary = {
        'filtering_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'original_records': original_count,
        'final_records': final_count,
        'retention_rate': final_count / original_count * 100,
        'filtering_criteria': {
            'time_range': '2019-06-01 to 2020-06-30',
            'report_type': 'Annual (rpt_type=4)',
            'indicator': 'Net Profit (321001000)',
            'data_quality': 'Non-null values only',
            'value_filter': 'Positive EPS only'
        }
    }
    
    # 保存总结
    import json
    with open('processed_data/filtering_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    # 保存月度统计
    monthly_stats.to_csv('processed_data/monthly_filtering_stats.csv', encoding='utf-8-sig')
    
    print(f"✅ 筛选总结已保存:")
    print(f"  总结报告: processed_data/filtering_summary.json")
    print(f"  月度统计: processed_data/monthly_filtering_stats.csv")

def main():
    """主函数"""
    print("开始数据准备与筛选")
    print("=" * 50)
    
    try:
        # 1. 加载原始数据
        df_raw = load_raw_data()
        original_count = len(df_raw)
        
        # 2. 数据质量检查
        data_quality_check(df_raw)
        
        # 3. 应用筛选条件
        df_filtered = apply_filters(df_raw)
        
        # 4. 生成统计信息
        monthly_stats = generate_summary_stats(df_filtered)
        
        # 5. 保存筛选后数据
        df_output = save_filtered_data(df_filtered)
        
        # 6. 创建筛选总结
        create_filtering_summary(original_count, len(df_filtered), monthly_stats)
        
        print("\n✅ 数据准备与筛选完成！")
        print(f"原始数据: {original_count:,} 条")
        print(f"筛选后数据: {len(df_filtered):,} 条")
        print(f"保留率: {len(df_filtered)/original_count*100:.2f}%")
        
        return df_output
        
    except Exception as e:
        print(f"\n❌ 处理过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    df_result = main()
