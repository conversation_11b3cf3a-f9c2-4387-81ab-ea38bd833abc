{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 02_预测偏离度分析\n", "\n", "本notebook负责：\n", "1. 加载筛选后的数据\n", "2. 分析时间跨度、企业规模、行业对预测偏离度的影响\n", "3. 为后续加权逻辑提供依据"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from scipy import stats\n", "import warnings\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"环境设置完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载处理后的数据\n", "print(\"=== 加载处理后的数据 ===\")\n", "\n", "try:\n", "    df_forecast = pd.read_feather('processed_data/forecast_data_filtered.feather')\n", "    df_industry = pd.read_feather('processed_data/industry_data_clean.feather')\n", "    \n", "    print(f\"预测数据形状: {df_forecast.shape}\")\n", "    print(f\"行业数据形状: {df_industry.shape}\")\n", "    \n", "except Exception as e:\n", "    print(f\"加载数据失败: {e}\")\n", "    print(\"请先运行 01_数据准备与筛选.ipynb\")\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 构建分析数据集\n", "print(\"=== 构建分析数据集 ===\")\n", "\n", "# 选择分析时间段\n", "analysis_start_date = '2018-01-01'\n", "analysis_end_date = '2020-12-31'\n", "\n", "df_analysis = df_forecast[\n", "    (df_forecast['create_date'] >= analysis_start_date) &\n", "    (df_forecast['create_date'] <= analysis_end_date)\n", "].copy()\n", "\n", "print(f\"分析期间数据量: {len(df_analysis)}\")\n", "\n", "# 模拟实际净利润数据\n", "np.random.seed(42)\n", "df_analysis['actual_profit_simulated'] = df_analysis['forecast_profit'] * (1 + np.random.normal(0, 0.2, len(df_analysis)))\n", "\n", "# 计算预测偏离度\n", "df_analysis['bias'] = np.abs(df_analysis['forecast_profit'] - df_analysis['actual_profit_simulated']) / np.abs(df_analysis['actual_profit_simulated'])\n", "\n", "print(f\"预测偏离度统计:\")\n", "print(df_analysis['bias'].describe())"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 时间跨度对偏离度的影响分析\n", "print(\"=== 时间跨度对偏离度的影响分析 ===\")\n", "\n", "# 计算预测时间跨度\n", "df_analysis['report_announce_date'] = pd.to_datetime(df_analysis['forecast_year'].astype(str) + '-04-30')\n", "df_analysis['time_span_months'] = (\n", "    (df_analysis['report_announce_date'].dt.year - df_analysis['create_date'].dt.year) * 12 +\n", "    (df_analysis['report_announce_date'].dt.month - df_analysis['create_date'].dt.month)\n", ")\n", "\n", "# 限制时间跨度在合理范围内\n", "df_analysis['time_span_months'] = df_analysis['time_span_months'].clip(0, 11)\n", "\n", "# 按时间跨度分组分析偏离度\n", "time_span_analysis = df_analysis.groupby('time_span_months')['bias'].agg([\n", "    'count', 'mean', 'median', 'std'\n", "]).round(4)\n", "\n", "print(\"时间跨度与偏离度关系:\")\n", "print(time_span_analysis)\n", "\n", "# 验证单调性\n", "correlation = stats.spearmanr(time_span_analysis.index, time_span_analysis['median'])\n", "print(f\"时间跨度与偏离度的Spearman相关系数: {correlation.correlation:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存分析结果\n", "print(\"=== 保存分析结果 ===\")\n", "\n", "time_span_analysis.to_csv('processed_data/time_span_bias_analysis.csv', encoding='utf-8-sig')\n", "df_analysis.to_feather('processed_data/analysis_data_with_bias.feather')\n", "\n", "print(\"预测偏离度分析完成！\")\n", "print(f\"分析数据量: {len(df_analysis):,}\")\n", "print(f\"涉及股票数量: {df_analysis['stock_code'].nunique():,}\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}