#!/usr/bin/env python3
"""
02 - 预测偏离度分析

分析分析师预测的偏离度，识别异常预测并进行清理
包括：
1. 计算预测偏离度指标
2. 识别异常预测值
3. 数据清理和过滤
4. 生成偏离度分析报告
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime

warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_filtered_data():
    """加载筛选后的预测数据"""
    print("=== 加载筛选后数据 ===")
    
    try:
        df = pd.read_feather('processed_data/forecast_data_filtered.feather')
        print(f"加载数据: {len(df):,} 条记录")
    except:
        df = pd.read_csv('processed_data/forecast_data_filtered.csv')
        print(f"加载数据: {len(df):,} 条记录")
    
    df['prediction_date'] = pd.to_datetime(df['prediction_date'])
    
    print(f"数据时间范围: {df['prediction_date'].min().strftime('%Y-%m-%d')} 到 {df['prediction_date'].max().strftime('%Y-%m-%d')}")
    print(f"涉及股票数: {df['stock_code'].nunique()}")
    
    return df

def calculate_deviation_metrics(df):
    """计算预测偏离度指标"""
    print("\n=== 计算预测偏离度指标 ===")
    
    # 按股票和日期分组，计算一致预期
    consensus_stats = df.groupby(['stock_code', 'prediction_date'])['predicted_eps'].agg([
        'mean',    # 一致预期均值
        'median',  # 一致预期中位数
        'std',     # 标准差
        'count'    # 预测数量
    ]).reset_index()
    
    consensus_stats.columns = ['stock_code', 'prediction_date', 'consensus_mean', 'consensus_median', 'consensus_std', 'analyst_count']
    
    # 合并回原数据
    df_with_consensus = df.merge(consensus_stats, on=['stock_code', 'prediction_date'])
    
    # 计算偏离度指标
    df_with_consensus['deviation_from_mean'] = (df_with_consensus['predicted_eps'] - df_with_consensus['consensus_mean']) / df_with_consensus['consensus_mean']
    df_with_consensus['deviation_from_median'] = (df_with_consensus['predicted_eps'] - df_with_consensus['consensus_median']) / df_with_consensus['consensus_median']
    
    # 计算标准化偏离度（Z-score）
    df_with_consensus['z_score'] = np.where(
        df_with_consensus['consensus_std'] > 0,
        (df_with_consensus['predicted_eps'] - df_with_consensus['consensus_mean']) / df_with_consensus['consensus_std'],
        0
    )
    
    print(f"计算完成，数据量: {len(df_with_consensus):,}")
    
    return df_with_consensus

def identify_outliers(df):
    """识别异常预测值"""
    print("\n=== 识别异常预测值 ===")
    
    # 定义异常值标准
    z_threshold = 3.0  # Z-score阈值
    deviation_threshold = 0.5  # 偏离度阈值（50%）
    
    # 标记异常值
    df['is_outlier_z'] = np.abs(df['z_score']) > z_threshold
    df['is_outlier_deviation'] = np.abs(df['deviation_from_mean']) > deviation_threshold
    df['is_outlier'] = df['is_outlier_z'] | df['is_outlier_deviation']
    
    # 统计异常值
    outlier_count = df['is_outlier'].sum()
    outlier_pct = outlier_count / len(df) * 100
    
    print(f"异常值统计:")
    print(f"  Z-score异常 (|z| > {z_threshold}): {df['is_outlier_z'].sum():,} 条")
    print(f"  偏离度异常 (|dev| > {deviation_threshold}): {df['is_outlier_deviation'].sum():,} 条")
    print(f"  总异常值: {outlier_count:,} 条 ({outlier_pct:.2f}%)")
    
    return df

def create_deviation_analysis_plots(df):
    """创建偏离度分析图表"""
    print("\n=== 生成偏离度分析图表 ===")
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('预测偏离度分析', fontsize=16, fontweight='bold')
    
    # 1. 偏离度分布直方图
    ax1 = axes[0, 0]
    df['deviation_from_mean'].hist(bins=50, alpha=0.7, ax=ax1)
    ax1.axvline(0, color='red', linestyle='--', alpha=0.8)
    ax1.set_title('预测偏离度分布')
    ax1.set_xlabel('偏离度 (相对于均值)')
    ax1.set_ylabel('频数')
    ax1.grid(True, alpha=0.3)
    
    # 2. Z-score分布
    ax2 = axes[0, 1]
    df['z_score'].hist(bins=50, alpha=0.7, ax=ax2)
    ax2.axvline(0, color='red', linestyle='--', alpha=0.8)
    ax2.axvline(3, color='orange', linestyle='--', alpha=0.8, label='±3σ')
    ax2.axvline(-3, color='orange', linestyle='--', alpha=0.8)
    ax2.set_title('Z-score分布')
    ax2.set_xlabel('Z-score')
    ax2.set_ylabel('频数')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 异常值比例（按月）
    ax3 = axes[1, 0]
    df['year_month'] = df['prediction_date'].dt.to_period('M')
    monthly_outliers = df.groupby('year_month')['is_outlier'].agg(['sum', 'count'])
    monthly_outliers['outlier_rate'] = monthly_outliers['sum'] / monthly_outliers['count'] * 100
    
    monthly_outliers['outlier_rate'].plot(kind='bar', ax=ax3)
    ax3.set_title('异常值比例（按月）')
    ax3.set_xlabel('月份')
    ax3.set_ylabel('异常值比例 (%)')
    ax3.tick_params(axis='x', rotation=45)
    ax3.grid(True, alpha=0.3)
    
    # 4. 预测数量 vs 偏离度标准差
    ax4 = axes[1, 1]
    consensus_stats = df.groupby(['stock_code', 'prediction_date']).agg({
        'consensus_std': 'first',
        'analyst_count': 'first'
    }).reset_index()
    
    ax4.scatter(consensus_stats['analyst_count'], consensus_stats['consensus_std'], alpha=0.5)
    ax4.set_title('分析师数量 vs 预测分歧')
    ax4.set_xlabel('分析师数量')
    ax4.set_ylabel('预测标准差')
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('processed_data/deviation_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("✅ 偏离度分析图表已保存: processed_data/deviation_analysis.png")

def clean_outliers(df, remove_outliers=True):
    """清理异常值"""
    print(f"\n=== 清理异常值 ===")
    
    original_count = len(df)
    
    if remove_outliers:
        # 移除异常值
        df_clean = df[~df['is_outlier']].copy()
        removed_count = original_count - len(df_clean)
        
        print(f"移除异常值: {removed_count:,} 条")
        print(f"保留数据: {len(df_clean):,} 条 ({len(df_clean)/original_count*100:.2f}%)")
    else:
        # 保留异常值但标记
        df_clean = df.copy()
        print(f"保留所有数据，异常值已标记")
    
    return df_clean

def generate_deviation_summary(df_original, df_clean):
    """生成偏离度分析总结"""
    print("\n=== 生成偏离度分析总结 ===")
    
    # 计算统计指标
    summary = {
        'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
        'original_records': len(df_original),
        'clean_records': len(df_clean),
        'outliers_removed': len(df_original) - len(df_clean),
        'outlier_rate': (len(df_original) - len(df_clean)) / len(df_original) * 100,
        'deviation_stats': {
            'mean_deviation': float(df_clean['deviation_from_mean'].mean()),
            'median_deviation': float(df_clean['deviation_from_mean'].median()),
            'std_deviation': float(df_clean['deviation_from_mean'].std()),
            'mean_z_score': float(df_clean['z_score'].mean()),
            'std_z_score': float(df_clean['z_score'].std())
        },
        'consensus_stats': {
            'avg_analyst_count': float(df_clean['analyst_count'].mean()),
            'avg_consensus_std': float(df_clean['consensus_std'].mean())
        }
    }
    
    # 保存总结
    import json
    with open('processed_data/deviation_analysis_summary.json', 'w', encoding='utf-8') as f:
        json.dump(summary, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 偏离度分析总结已保存: processed_data/deviation_analysis_summary.json")
    
    return summary

def save_clean_data(df_clean):
    """保存清理后的数据"""
    print("\n=== 保存清理后数据 ===")
    
    # 选择需要的列
    output_columns = [
        'stock_code', 'prediction_date', 'predicted_eps',
        'consensus_mean', 'consensus_median', 'consensus_std', 'analyst_count',
        'deviation_from_mean', 'deviation_from_median', 'z_score', 'is_outlier'
    ]
    
    df_output = df_clean[output_columns].copy()
    
    # 保存数据
    df_output.to_csv('processed_data/forecast_data_clean.csv', index=False, encoding='utf-8-sig')
    df_output.to_feather('processed_data/forecast_data_clean.feather')
    
    print(f"✅ 清理后数据已保存:")
    print(f"  CSV格式: processed_data/forecast_data_clean.csv")
    print(f"  Feather格式: processed_data/forecast_data_clean.feather")
    print(f"  数据量: {len(df_output):,} 条")
    
    return df_output

def main():
    """主函数"""
    print("开始预测偏离度分析")
    print("=" * 50)
    
    try:
        # 1. 加载筛选后数据
        df = load_filtered_data()
        
        # 2. 计算偏离度指标
        df_with_deviation = calculate_deviation_metrics(df)
        
        # 3. 识别异常值
        df_with_outliers = identify_outliers(df_with_deviation)
        
        # 4. 创建分析图表
        create_deviation_analysis_plots(df_with_outliers)
        
        # 5. 清理异常值
        df_clean = clean_outliers(df_with_outliers, remove_outliers=True)
        
        # 6. 生成分析总结
        summary = generate_deviation_summary(df_with_outliers, df_clean)
        
        # 7. 保存清理后数据
        df_output = save_clean_data(df_clean)
        
        print("\n✅ 预测偏离度分析完成！")
        print(f"原始数据: {len(df):,} 条")
        print(f"清理后数据: {len(df_clean):,} 条")
        print(f"异常值移除: {len(df_with_outliers) - len(df_clean):,} 条")
        print(f"异常值比例: {summary['outlier_rate']:.2f}%")
        
        return df_output
        
    except Exception as e:
        print(f"\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    df_result = main()
