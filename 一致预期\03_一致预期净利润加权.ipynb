{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 03 - 一致预期净利润加权计算（完整版）\n", "\n", "基于研报4.1节，正确实现：\n", "1. 有效报告定义：预测日前3个月内撰写且录入的报告（无3个月数据时扩展至6个月）\n", "2. 使用完整的数据时间范围，而非仅2019-2020年\n", "3. 时间加权的一致预期净利润计算"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境加载完成\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import json\n", "from datetime import datetime, timedelta\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print('环境加载完成')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载和检查数据"]}, {"cell_type": "code", "execution_count": 41, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载预测数据 ===\n", "数据总量: 2,890,832 条\n", "时间范围: 2012-01-01 00:00:00 到 2025-07-13 00:00:00\n", "预测年份范围: 2011.0 到 2032.0\n", "股票数量: 4,943\n", "\n", "=== 按年份统计数据分布 ===\n", "              股票数     记录数\n", "create_date              \n", "2012         1985  216537\n", "2013         1885  196201\n", "2014         1921  159611\n", "2015         2186  149662\n", "2016         2371  193092\n", "2017         2590  228140\n", "2018         2271  201299\n", "2019         2247  198226\n", "2020         2325  219265\n", "2021         2509  210654\n", "2022         2881  268052\n", "2023         3268  284374\n", "2024         3257  261477\n", "2025         2966  104242\n", "\n", "=== 预测年份分布 ===\n", "forecast_year\n", "2011.0     12960\n", "2012.0     97290\n", "2013.0    164184\n", "2014.0    181718\n", "2015.0    164139\n", "2016.0    171861\n", "2017.0    196402\n", "2018.0    206838\n", "2019.0    206172\n", "2020.0    202362\n", "2021.0    207557\n", "2022.0    229437\n", "2023.0    254654\n", "2024.0    263959\n", "2025.0    200677\n", "2026.0    105713\n", "2027.0     24860\n", "2028.0        19\n", "2029.0        15\n", "2030.0        11\n", "2031.0         2\n", "2032.0         2\n", "Name: count, dtype: int64\n"]}], "source": ["print('=== 加载预测数据 ===')\n", "df_forecast = pd.read_feather('processed_data/forecast_data_filtered.feather')\n", "df_forecast['create_date'] = pd.to_datetime(df_forecast['create_date'])\n", "\n", "print(f'数据总量: {len(df_forecast):,} 条')\n", "print(f'时间范围: {df_forecast[\"create_date\"].min()} 到 {df_forecast[\"create_date\"].max()}')\n", "print(f'预测年份范围: {df_forecast[\"forecast_year\"].min()} 到 {df_forecast[\"forecast_year\"].max()}')\n", "print(f'股票数量: {df_forecast[\"stock_code\"].nunique():,}')\n", "\n", "# 按年份统计数据分布\n", "print(f'\\n=== 按年份统计数据分布 ===')\n", "year_stats = df_forecast.groupby(df_forecast['create_date'].dt.year).agg({\n", "    'stock_code': 'nunique',\n", "    'create_date': 'count'\n", "}).rename(columns={'stock_code': '股票数', 'create_date': '记录数'})\n", "print(year_stats)\n", "\n", "# 检查预测年份分布\n", "print(f'\\n=== 预测年份分布 ===')\n", "forecast_year_stats = df_forecast['forecast_year'].value_counts().sort_index()\n", "print(forecast_year_stats)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 改进的有效报告筛选函数\n", "\n", "正确实现\"预测日前3个月内撰写且录入\"的逻辑，无3个月数据时扩展至6个月"]}, {"cell_type": "code", "execution_count": 42, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "预测日: 2018-12-31, 目标年份: 2019\n", "状态: 3个月窗口\n", "有效报告数: 15981, 涉及股票数: 1616\n", "        stock_code create_date              entry_date  forecast_profit\n", "1433135  600029.SH  2018-09-30 2018-10-08 10:30:11.667     9.278000e+09\n", "1433138  601098.SH  2018-10-06 2018-10-08 10:30:11.667     1.342000e+09\n", "1433140  600623.SH  2018-10-07 2018-10-08 10:30:11.667     2.864740e+09\n", "1433143  603368.SH  2018-10-07 2018-10-08 10:30:11.667     6.650800e+08\n", "1433146  600029.SH  2018-10-07 2018-10-08 10:30:11.667     7.972000e+09\n"]}], "source": ["import pandas as pd\n", "\n", "def get_valid_reports(df, prediction_date, target_year, min_reports=1):\n", "    \"\"\"筛选有效预测报告，确保录入日期在预测日之前\"\"\"\n", "    # 计算时间窗口\n", "    t_minus_3m = prediction_date - pd.DateOffset(months=3)\n", "    t_minus_6m = prediction_date - pd.DateOffset(months=6)\n", "    \n", "    # 3个月窗口筛选：创建日期在范围内且录入日期<=预测日\n", "    valid_3m = df[\n", "        (df['create_date'] >= t_minus_3m) & \n", "        (df['create_date'] <= prediction_date) &\n", "        (df['entry_date'] <= prediction_date) & \n", "        (df['forecast_year'] == target_year)\n", "    ]\n", "    \n", "    if len(valid_3m) >= min_reports:\n", "        return valid_3m, \"3个月窗口\"\n", "    \n", "    # 6个月窗口筛选：创建日期在范围内且录入日期<=预测日\n", "    valid_6m = df[\n", "        (df['create_date'] >= t_minus_6m) & \n", "        (df['create_date'] <= prediction_date) &\n", "        (df['entry_date'] <= prediction_date) & \n", "        (df['forecast_year'] == target_year)\n", "    ]\n", "    \n", "    if len(valid_6m) >= min_reports:\n", "        return valid_6m, \"6个月窗口\"\n", "    \n", "    # 无有效报告\n", "    return None, \"一致预期缺失\"\n", "\n", "# 测试筛选\n", "pred_date = pd.Timestamp('2018-12-31')\n", "target_year = 2019\n", "reports, status = get_valid_reports(df_forecast, pred_date, target_year)\n", "    \n", "# 结果展示\n", "print(f'\\n预测日: {pred_date.date()}, 目标年份: {target_year}')\n", "print(f'状态: {status}')\n", "if reports is not None:\n", "    print(f'有效报告数: {len(reports)}, 涉及股票数: {reports[\"stock_code\"].nunique()}')\n", "    print(reports[['stock_code', 'create_date', 'entry_date', 'forecast_profit']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 生成完整的预测时点序列\n", "\n", "基于数据的实际时间范围，生成季度末预测时点"]}, {"cell_type": "code", "execution_count": 43, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据时间范围: 2012-01-01 到 2025-07-13\n", "生成预测时点范围: 2012 到 2025\n", "\n", "生成的有效预测时点数量: 54\n", "时点范围: 2012-03-31 到 2025-06-30\n", "目标年份范围: 2013 到 2026\n", "\n", "=== 预测时点详情 ===\n", " 1. 2012-03-31 -> 2013年\n", " 2. 2012-06-30 -> 2013年\n", " 3. 2012-09-30 -> 2013年\n", " 4. 2012-12-31 -> 2013年\n", " 5. 2013-03-31 -> 2014年\n", " 6. 2013-06-30 -> 2014年\n", " 7. 2013-09-30 -> 2014年\n", " 8. 2013-12-31 -> 2014年\n", " 9. 2014-03-31 -> 2015年\n", "10. 2014-06-30 -> 2015年\n", "... 共54个时点\n"]}], "source": ["def generate_prediction_dates(df_forecast, start_year=None, end_year=None):\n", "    \"\"\"\n", "    生成预测时点序列\n", "    \n", "    参数:\n", "    - df_forecast: 预测数据DataFrame\n", "    - start_year: 开始年份（None时自动确定）\n", "    - end_year: 结束年份（None时自动确定）\n", "    \n", "    返回:\n", "    - 预测时点列表和对应的目标年份列表\n", "    \"\"\"\n", "    \n", "    # 确定数据的实际时间范围\n", "    data_start = df_forecast['create_date'].min()\n", "    data_end = df_forecast['create_date'].max()\n", "    \n", "    if start_year is None:\n", "        start_year = data_start.year\n", "    if end_year is None:\n", "        end_year = data_end.year\n", "    \n", "    print(f\"数据时间范围: {data_start.strftime('%Y-%m-%d')} 到 {data_end.strftime('%Y-%m-%d')}\")\n", "    print(f\"生成预测时点范围: {start_year} 到 {end_year}\")\n", "    \n", "    prediction_dates = []\n", "    target_years = []\n", "    \n", "    # 为每年生成季度末预测时点\n", "    for year in range(start_year, end_year + 1):\n", "        quarters = [\n", "            (f'{year}-03-31', year + 1),  # Q1末预测下一年\n", "            (f'{year}-06-30', year + 1),  # Q2末预测下一年\n", "            (f'{year}-09-30', year + 1),  # Q3末预测下一年\n", "            (f'{year}-12-31', year + 1),  # Q4末预测下一年\n", "        ]\n", "        \n", "        for date_str, target_year in quarters:\n", "            pred_date = pd.Timestamp(date_str)\n", "            \n", "            # 确保预测时点在数据范围内\n", "            if pred_date >= data_start and pred_date <= data_end:\n", "                # 确保目标年份在预测数据中存在\n", "                if target_year in df_forecast['forecast_year'].values:\n", "                    prediction_dates.append(pred_date)\n", "                    target_years.append(target_year)\n", "    \n", "    print(f\"\\n生成的有效预测时点数量: {len(prediction_dates)}\")\n", "    print(f\"时点范围: {prediction_dates[0].strftime('%Y-%m-%d')} 到 {prediction_dates[-1].strftime('%Y-%m-%d')}\")\n", "    print(f\"目标年份范围: {min(target_years)} 到 {max(target_years)}\")\n", "    \n", "    return prediction_dates, target_years\n", "\n", "# 生成完整的预测时点序列\n", "prediction_dates, target_years = generate_prediction_dates(df_forecast)\n", "\n", "print(f\"\\n=== 预测时点详情 ===\")\n", "for i, (pred_date, target_year) in enumerate(zip(prediction_dates[:10], target_years[:10])):\n", "    print(f\"{i+1:2d}. {pred_date.strftime('%Y-%m-%d')} -> {target_year}年\")\n", "if len(prediction_dates) > 10:\n", "    print(f\"... 共{len(prediction_dates)}个时点\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 改进的时间加权一致预期计算"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [], "source": ["def deduplicate_analyst_forecasts(df):\n", "    \"\"\"\n", "    分析师去重：同一分析师对同一股票的多次预测，保留最新的\n", "    \"\"\"\n", "    print(\"=== 分析师自我修正处理 ===\")\n", "    print(f\"去重前记录数: {len(df):,}\")\n", "    \n", "    # 按股票代码、分析师ID、创建日期排序，保留最新的预测\n", "    df_sorted = df.sort_values(['stock_code', 'analyst_id', 'create_date'])\n", "    df_dedup = df_sorted.groupby(['stock_code', 'analyst_id']).last().reset_index()\n", "    \n", "    print(f\"去重后记录数: {len(df_dedup):,}\")\n", "    print(f\"去重比例: {len(df_dedup)/len(df)*100:.1f}%\")\n", "    \n", "    return df_dedup\n", "\n", "def calculate_time_weighted_consensus(df, prediction_date):\n", "    \"\"\"\n", "    严格按照研报4.4节逻辑计算时间加权一致预期净利润\n", "    核心规则：\n", "    1. 优先使用最近3个月有效报告，不足时扩展至6个月\n", "    2. 按撰写月份分组计算均值，再按时间半衰权重（1,2,4）加权\n", "    3. 仅使用有效报告数据（无过早数据）\n", "    \"\"\"\n", "    if len(df) == 0:\n", "        return pd.DataFrame()\n", "    \n", "    results = []\n", "    \n", "    # 按股票分组计算\n", "    for stock_code, group in df.groupby('stock_code'):\n", "        group = group.copy()\n", "        \n", "        # 计算报告距离预测日的月份差（核心时间指标）\n", "        group['months_diff'] = (\n", "            (prediction_date.year - group['create_date'].dt.year) * 12 +\n", "            (prediction_date.month - group['create_date'].dt.month)\n", "        )\n", "        \n", "        # 过滤无效报告（未来报告或超过6个月的报告）\n", "        valid_group = group[\n", "            (group['months_diff'] >= 0) &  # 排除未来报告\n", "            (group['months_diff'] <= 6)    # 仅保留6个月内的报告\n", "        ]\n", "        \n", "        if len(valid_group) == 0:\n", "            continue\n", "        \n", "        # 1. 优先处理3个月窗口（0-2个月）\n", "        window_3m = valid_group[valid_group['months_diff'] <= 2]\n", "        if len(window_3m) > 0:\n", "            time_window = \"3个月\"\n", "            # 研报规定的3个月权重：距离越近权重越高（1,2,4半衰）\n", "            # 0个月(当月)=4，1个月=2，2个月=1\n", "            weights = {0:4, 1:2, 2:1}\n", "            target_window = window_3m\n", "        else:\n", "            # 2. 3个月不足时使用6个月窗口（3-5个月）\n", "            window_6m = valid_group[valid_group['months_diff'].between(3, 5)]\n", "            if len(window_6m) == 0:\n", "                continue  # 6个月内仍无有效报告则跳过\n", "            time_window = \"6个月\"\n", "            # 研报规定的6个月权重：3个月=4，4个月=2，5个月=1\n", "            weights = {3:4, 4:2, 5:1}\n", "            target_window = window_6m\n", "        \n", "        # 按月份分组计算每组平均预测值\n", "        monthly_groups = target_window.groupby('months_diff')\n", "        monthly_avg = monthly_groups['forecast_profit'].mean().reset_index()\n", "        \n", "        # 计算时间加权平均（按研报权重）\n", "        weighted_sum = 0\n", "        total_weight = 0\n", "        for _, row in monthly_avg.iterrows():\n", "            month_diff = row['months_diff']\n", "            avg_profit = row['forecast_profit']\n", "            weight = weights.get(month_diff, 0)  # 仅目标窗口内有权重\n", "            \n", "            weighted_sum += avg_profit * weight\n", "            total_weight += weight\n", "        \n", "        if total_weight > 0:\n", "            consensus_profit = weighted_sum / total_weight\n", "            analyst_count = target_window['analyst_id'].nunique()\n", "            results.append({\n", "                'stock_code': stock_code,\n", "                'prediction_date': prediction_date,\n", "                'consensus_profit_fy': consensus_profit,\n", "                'report_count': len(target_window),  # 有效报告总数\n", "                'analyst_count': analyst_count,  # 有效分析师数\n", "                'month_count': len(monthly_groups),  # 覆盖的月份数\n", "                'time_window': time_window\n", "            })\n", "    \n", "    if results:\n", "        result_df = pd.DataFrame(results)\n", "        print(f\"计算完成：{len(result_df)}只股票，3个月窗口占比{sum(result_df['time_window']=='3个月')/len(result_df):.1%}\")\n", "        print(f\"平均每份股票的报告数：{result_df['report_count'].mean():.1f}\")\n", "        return result_df\n", "    else:\n", "        print(\"未计算出任何有效一致预期\")\n", "        return pd.DataFrame()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 滚动一致预期计算"]}, {"cell_type": "code", "execution_count": 45, "metadata": {}, "outputs": [], "source": ["def calculate_rolling_consensus_improved(consensus_fy1, consensus_fy2, prediction_date):\n", "    \"\"\"\n", "    计算改进的滚动一致预期净利润\n", "    \n", "    参数:\n", "    - consensus_fy1: FY1一致预期净利润DataFrame\n", "    - consensus_fy2: FY2一致预期净利润DataFrame  \n", "    - prediction_date: 预测基准日期\n", "    \n", "    返回:\n", "    - 包含滚动一致预期净利润的DataFrame\n", "    \"\"\"\n", "    \n", "    if len(consensus_fy1) == 0:\n", "        print(\"FY1数据为空，无法计算滚动一致预期\")\n", "        return pd.DataFrame()\n", "    \n", "    # 当前月份\n", "    current_month = prediction_date.month\n", "    t = current_month\n", "    \n", "    print(f\"预测基准日期: {prediction_date.strftime('%Y-%m-%d')}\")\n", "    print(f\"当前月份: {current_month}\")\n", "    print(f\"FY1权重: {1 - t/12:.3f}, FY2权重: {t/12:.3f}\")\n", "    \n", "    # 合并FY1和FY2数据\n", "    result_df = consensus_fy1.copy()\n", "    result_df['consensus_profit_fy2'] = np.nan\n", "    result_df['consensus_profit_roll'] = result_df['consensus_profit_fy']  # 默认等于FY1\n", "    \n", "    if len(consensus_fy2) > 0:\n", "        # 合并FY2数据\n", "        fy2_dict = dict(zip(consensus_fy2['stock_code'], consensus_fy2['consensus_profit_fy']))\n", "        \n", "        for idx, row in result_df.iterrows():\n", "            stock_code = row['stock_code']\n", "            fy1_profit = row['consensus_profit_fy']\n", "            \n", "            if stock_code in fy2_dict:\n", "                fy2_profit = fy2_dict[stock_code]\n", "                result_df.at[idx, 'consensus_profit_fy2'] = fy2_profit\n", "                \n", "                # 计算滚动一致预期：S_roll = (1-t/12) * S1 + (t/12) * S2\n", "                roll_profit = (1 - t/12) * fy1_profit + (t/12) * fy2_profit\n", "                result_df.at[idx, 'consensus_profit_roll'] = roll_profit\n", "    \n", "    # 添加预测年份信息\n", "    result_df['forecast_year'] = prediction_date.year + 1\n", "    \n", "    print(f\"滚动一致预期计算完成: {len(result_df)} 只股票\")\n", "    if 'consensus_profit_fy2' in result_df.columns:\n", "        fy2_coverage = result_df['consensus_profit_fy2'].notna().sum()\n", "        print(f\"FY2数据覆盖: {fy2_coverage} 只股票 ({fy2_coverage/len(result_df)*100:.1f}%)\")\n", "    \n", "    return result_df"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 批量计算完整时间序列"]}, {"cell_type": "code", "execution_count": 46, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 开始测试批量计算（前5个时点）===\n", "开始批量计算，共 5 个时点\n", "\n", "=== 批次 1/5: 2012-03-31 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,761\n", "去重后记录数: 10,476\n", "去重比例: 76.1%\n", "计算完成：1464只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.2\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,928\n", "去重后记录数: 3,720\n", "去重比例: 94.7%\n", "计算完成：809只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：4.6\n", "预测基准日期: 2012-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1464 只股票\n", "FY2数据覆盖: 809 只股票 (55.3%)\n", "✅ 成功计算 1464 只股票\n", "\n", "=== 批次 2/5: 2012-06-30 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,327\n", "去重后记录数: 14,153\n", "去重比例: 73.2%\n", "计算完成：1617只股票，3个月窗口占比99.5%\n", "平均每份股票的报告数：8.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 10,806\n", "去重后记录数: 8,139\n", "去重比例: 75.3%\n", "计算完成：1404只股票，3个月窗口占比99.4%\n", "平均每份股票的报告数：5.7\n", "预测基准日期: 2012-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1617 只股票\n", "FY2数据覆盖: 1403 只股票 (86.8%)\n", "✅ 成功计算 1617 只股票\n", "\n", "=== 批次 3/5: 2012-09-30 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 21,547\n", "去重后记录数: 16,226\n", "去重比例: 75.3%\n", "计算完成：1647只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：9.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,309\n", "去重后记录数: 11,036\n", "去重比例: 77.1%\n", "计算完成：1474只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.5\n", "预测基准日期: 2012-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1647 只股票\n", "FY2数据覆盖: 1469 只股票 (89.2%)\n", "✅ 成功计算 1647 只股票\n", "\n", "=== 批次 4/5: 2012-12-31 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,755\n", "去重后记录数: 15,042\n", "去重比例: 76.1%\n", "计算完成：1616只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：9.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,322\n", "去重后记录数: 10,412\n", "去重比例: 78.2%\n", "计算完成：1439只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.2\n", "预测基准日期: 2012-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1616 只股票\n", "FY2数据覆盖: 1431 只股票 (88.6%)\n", "✅ 成功计算 1616 只股票\n", "\n", "=== 批次 5/5: 2013-03-31 -> 2014年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,868\n", "去重后记录数: 9,628\n", "去重比例: 74.8%\n", "计算完成：1401只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：6.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,306\n", "去重后记录数: 3,118\n", "去重比例: 94.3%\n", "计算完成：686只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：4.5\n", "预测基准日期: 2013-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1401 只股票\n", "FY2数据覆盖: 685 只股票 (48.9%)\n", "✅ 成功计算 1401 只股票\n", "\n", "=== 批量计算完成 ===\n", "总计算结果: 7,745 条记录\n", "涉及股票数: 1,965\n", "涉及时点数: 5\n", "时间范围: 2012-03-31 00:00:00 到 2013-03-31 00:00:00\n", "\n", "=== 测试结果预览 ===\n", "  stock_code prediction_date  forecast_year  consensus_profit_fy  \\\n", "0  000001.SZ      2012-03-31           2013         1.432047e+10   \n", "1  000002.SZ      2012-03-31           2013         1.549712e+10   \n", "2  000006.SZ      2012-03-31           2013         7.671663e+08   \n", "3  000012.SZ      2012-03-31           2013         2.107534e+09   \n", "4  000016.SZ      2012-03-31           2013         2.528337e+08   \n", "5  000022.SZ      2012-03-31           2013         5.555720e+08   \n", "6  000024.SZ      2012-03-31           2013         4.097929e+09   \n", "7  000026.SZ      2012-03-31           2013         2.843560e+08   \n", "8  000027.SZ      2012-03-31           2013         1.518566e+09   \n", "9  000028.SZ      2012-03-31           2013         4.956834e+08   \n", "\n", "   consensus_profit_roll  analyst_count  \n", "0           1.519907e+10             26  \n", "1           1.626477e+10             30  \n", "2           7.671663e+08              4  \n", "3           2.155185e+09             12  \n", "4           2.528337e+08              1  \n", "5           5.544165e+08             10  \n", "6           4.338433e+09             22  \n", "7           3.097492e+08             10  \n", "8           1.518566e+09              5  \n", "9           5.184769e+08             12  \n"]}], "source": ["def batch_calculate_consensus_complete(df_forecast, prediction_dates, target_years, max_batches=None):\n", "    \"\"\"\n", "    批量计算完整时间序列的一致预期净利润\n", "    \n", "    参数:\n", "    - df_forecast: 预测数据DataFrame\n", "    - prediction_dates: 预测基准日期列表\n", "    - target_years: 目标预测年份列表\n", "    - max_batches: 最大批次数（用于测试）\n", "    \n", "    返回:\n", "    - 合并后的一致预期结果DataFrame\n", "    \"\"\"\n", "    \n", "    all_results = []\n", "    total_batches = len(prediction_dates)\n", "    \n", "    if max_batches:\n", "        total_batches = min(total_batches, max_batches)\n", "        prediction_dates = prediction_dates[:max_batches]\n", "        target_years = target_years[:max_batches]\n", "    \n", "    print(f\"开始批量计算，共 {total_batches} 个时点\")\n", "    \n", "    for i, (pred_date, target_year) in enumerate(zip(prediction_dates, target_years)):\n", "        print(f\"\\n=== 批次 {i+1}/{total_batches}: {pred_date.strftime('%Y-%m-%d')} -> {target_year}年 ===\")\n", "        \n", "        try:\n", "            # 获取FY1有效报告\n", "            valid_reports_fy1, fy1_status = get_valid_reports(df_forecast, pred_date, target_year)\n", "            if valid_reports_fy1 is None or len(valid_reports_fy1) == 0:\n", "                 print(f\"❌ 未找到 {target_year} 年的有效报告（状态：{fy1_status}）\")\n", "                 continue\n", "            \n", "            # 分析师去重\n", "            valid_reports_fy1_dedup = deduplicate_analyst_forecasts(valid_reports_fy1)\n", "            \n", "            # 计算FY1一致预期\n", "            consensus_fy1 = calculate_time_weighted_consensus(valid_reports_fy1_dedup, pred_date)\n", "            \n", "            if len(consensus_fy1) == 0:\n", "                print(f\"❌ 未能计算出 {target_year} 年的一致预期\")\n", "                continue\n", "            \n", "            # 获取FY2有效报告\n", "            valid_reports_fy2, fy2_status = get_valid_reports(df_forecast, pred_date, target_year + 1)\n", "            consensus_fy2 = pd.DataFrame()\n", "            if valid_reports_fy2 is not None and len(valid_reports_fy2) > 0:\n", "                valid_reports_fy2_dedup = deduplicate_analyst_forecasts(valid_reports_fy2)\n", "                consensus_fy2 = calculate_time_weighted_consensus(valid_reports_fy2_dedup, pred_date)\n", "            else:\n", "                print(f\"ℹ️  未找到 {target_year + 1} 年的有效报告（状态：{fy2_status}）\")\n", "            \n", "            # 计算滚动一致预期\n", "            final_consensus = calculate_rolling_consensus_improved(consensus_fy1, consensus_fy2, pred_date)\n", "            \n", "            if len(final_consensus) > 0:\n", "                all_results.append(final_consensus)\n", "                print(f\"✅ 成功计算 {len(final_consensus)} 只股票\")\n", "            else:\n", "                print(f\"❌ 滚动一致预期计算失败\")\n", "                \n", "        except Exception as e:\n", "            print(f\"❌ 批次计算出错: {e}\")\n", "            continue\n", "    \n", "    if len(all_results) > 0:\n", "        combined_results = pd.concat(all_results, ignore_index=True)\n", "        print(f\"\\n=== 批量计算完成 ===\")\n", "        print(f\"总计算结果: {len(combined_results):,} 条记录\")\n", "        print(f\"涉及股票数: {combined_results['stock_code'].nunique():,}\")\n", "        print(f\"涉及时点数: {combined_results['prediction_date'].nunique()}\")\n", "        print(f\"时间范围: {combined_results['prediction_date'].min()} 到 {combined_results['prediction_date'].max()}\")\n", "        return combined_results\n", "    else:\n", "        print(\"\\n❌ 批量计算未产生有效结果\")\n", "        return pd.DataFrame()\n", "\n", "# 执行批量计算（先测试前5个时点）\n", "print(\"=== 开始测试批量计算（前5个时点）===\")\n", "test_results = batch_calculate_consensus_complete(\n", "    df_forecast, prediction_dates, target_years, max_batches=5\n", ")\n", "\n", "if len(test_results) > 0:\n", "    print(f\"\\n=== 测试结果预览 ===\")\n", "    print(test_results[['stock_code', 'prediction_date', 'forecast_year', \n", "                       'consensus_profit_fy', 'consensus_profit_roll', 'analyst_count']].head(10))\n", "else:\n", "    print(\"\\n测试计算失败\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 完整计算和保存结果"]}, {"cell_type": "code", "execution_count": 47, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 测试成功，开始完整计算 ===\n", "将计算全部 54 个时点，预计需要较长时间\n", "如需继续，请运行下一个代码单元\n"]}], "source": ["# 如果测试成功，执行完整计算\n", "if len(test_results) > 0:\n", "    print(\"\\n=== 测试成功，开始完整计算 ===\")\n", "    \n", "    # 询问用户是否继续完整计算\n", "    print(f\"将计算全部 {len(prediction_dates)} 个时点，预计需要较长时间\")\n", "    print(\"如需继续，请运行下一个代码单元\")\n", "    \n", "else:\n", "    print(\"\\n❌ 测试失败，请检查数据和代码\")"]}, {"cell_type": "code", "execution_count": 48, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 开始完整批量计算 ===\n", "开始批量计算，共 54 个时点\n", "\n", "=== 批次 1/54: 2012-03-31 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,761\n", "去重后记录数: 10,476\n", "去重比例: 76.1%\n", "计算完成：1464只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.2\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,928\n", "去重后记录数: 3,720\n", "去重比例: 94.7%\n", "计算完成：809只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：4.6\n", "预测基准日期: 2012-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1464 只股票\n", "FY2数据覆盖: 809 只股票 (55.3%)\n", "✅ 成功计算 1464 只股票\n", "\n", "=== 批次 2/54: 2012-06-30 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,327\n", "去重后记录数: 14,153\n", "去重比例: 73.2%\n", "计算完成：1617只股票，3个月窗口占比99.5%\n", "平均每份股票的报告数：8.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 10,806\n", "去重后记录数: 8,139\n", "去重比例: 75.3%\n", "计算完成：1404只股票，3个月窗口占比99.4%\n", "平均每份股票的报告数：5.7\n", "预测基准日期: 2012-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1617 只股票\n", "FY2数据覆盖: 1403 只股票 (86.8%)\n", "✅ 成功计算 1617 只股票\n", "\n", "=== 批次 3/54: 2012-09-30 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 21,547\n", "去重后记录数: 16,226\n", "去重比例: 75.3%\n", "计算完成：1647只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：9.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,309\n", "去重后记录数: 11,036\n", "去重比例: 77.1%\n", "计算完成：1474只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.5\n", "预测基准日期: 2012-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1647 只股票\n", "FY2数据覆盖: 1469 只股票 (89.2%)\n", "✅ 成功计算 1647 只股票\n", "\n", "=== 批次 4/54: 2012-12-31 -> 2013年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,755\n", "去重后记录数: 15,042\n", "去重比例: 76.1%\n", "计算完成：1616只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：9.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,322\n", "去重后记录数: 10,412\n", "去重比例: 78.2%\n", "计算完成：1439只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.2\n", "预测基准日期: 2012-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1616 只股票\n", "FY2数据覆盖: 1431 只股票 (88.6%)\n", "✅ 成功计算 1616 只股票\n", "\n", "=== 批次 5/54: 2013-03-31 -> 2014年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,868\n", "去重后记录数: 9,628\n", "去重比例: 74.8%\n", "计算完成：1401只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：6.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,306\n", "去重后记录数: 3,118\n", "去重比例: 94.3%\n", "计算完成：686只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：4.5\n", "预测基准日期: 2013-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1401 只股票\n", "FY2数据覆盖: 685 只股票 (48.9%)\n", "✅ 成功计算 1401 只股票\n", "\n", "=== 批次 6/54: 2013-06-30 -> 2014年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 18,163\n", "去重后记录数: 13,292\n", "去重比例: 73.2%\n", "计算完成：1581只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：8.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 10,445\n", "去重后记录数: 7,889\n", "去重比例: 75.5%\n", "计算完成：1341只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：5.8\n", "预测基准日期: 2013-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1581 只股票\n", "FY2数据覆盖: 1340 只股票 (84.8%)\n", "✅ 成功计算 1581 只股票\n", "\n", "=== 批次 7/54: 2013-09-30 -> 2014年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 18,379\n", "去重后记录数: 13,419\n", "去重比例: 73.0%\n", "计算完成：1561只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：8.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,445\n", "去重后记录数: 9,218\n", "去重比例: 74.1%\n", "计算完成：1390只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：6.6\n", "预测基准日期: 2013-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1561 只股票\n", "FY2数据覆盖: 1386 只股票 (88.8%)\n", "✅ 成功计算 1561 只股票\n", "\n", "=== 批次 8/54: 2013-12-31 -> 2014年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 16,420\n", "去重后记录数: 12,359\n", "去重比例: 75.3%\n", "计算完成：1513只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：8.1\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 11,106\n", "去重后记录数: 8,445\n", "去重比例: 76.0%\n", "计算完成：1383只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.1\n", "预测基准日期: 2013-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1513 只股票\n", "FY2数据覆盖: 1372 只股票 (90.7%)\n", "✅ 成功计算 1513 只股票\n", "\n", "=== 批次 9/54: 2014-03-31 -> 2015年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 10,121\n", "去重后记录数: 7,334\n", "去重比例: 72.5%\n", "计算完成：1375只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：5.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,321\n", "去重后记录数: 2,092\n", "去重比例: 90.1%\n", "计算完成：573只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：3.7\n", "预测基准日期: 2014-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1375 只股票\n", "FY2数据覆盖: 572 只股票 (41.6%)\n", "✅ 成功计算 1375 只股票\n", "\n", "=== 批次 10/54: 2014-06-30 -> 2015年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 15,284\n", "去重后记录数: 10,737\n", "去重比例: 70.2%\n", "计算完成：1537只股票，3个月窗口占比99.3%\n", "平均每份股票的报告数：6.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 9,027\n", "去重后记录数: 6,586\n", "去重比例: 73.0%\n", "计算完成：1357只股票，3个月窗口占比99.4%\n", "平均每份股票的报告数：4.7\n", "预测基准日期: 2014-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1537 只股票\n", "FY2数据覆盖: 1351 只股票 (87.9%)\n", "✅ 成功计算 1537 只股票\n", "\n", "=== 批次 11/54: 2014-09-30 -> 2015年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,789\n", "去重后记录数: 10,440\n", "去重比例: 70.6%\n", "计算完成：1589只股票，3个月窗口占比99.6%\n", "平均每份股票的报告数：6.4\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 10,297\n", "去重后记录数: 7,376\n", "去重比例: 71.6%\n", "计算完成：1441只股票，3个月窗口占比99.5%\n", "平均每份股票的报告数：5.0\n", "预测基准日期: 2014-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1589 只股票\n", "FY2数据覆盖: 1433 只股票 (90.2%)\n", "✅ 成功计算 1589 只股票\n", "\n", "=== 批次 12/54: 2014-12-31 -> 2015年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,139\n", "去重后记录数: 9,695\n", "去重比例: 73.8%\n", "计算完成：1596只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.0\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 9,048\n", "去重后记录数: 6,755\n", "去重比例: 74.7%\n", "计算完成：1465只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：4.6\n", "预测基准日期: 2014-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1596 只股票\n", "FY2数据覆盖: 1450 只股票 (90.9%)\n", "✅ 成功计算 1596 只股票\n", "\n", "=== 批次 13/54: 2015-03-31 -> 2016年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 8,668\n", "去重后记录数: 6,395\n", "去重比例: 73.8%\n", "计算完成：1437只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：4.4\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 1,887\n", "去重后记录数: 1,746\n", "去重比例: 92.5%\n", "计算完成：591只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：3.0\n", "预测基准日期: 2015-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1437 只股票\n", "FY2数据覆盖: 590 只股票 (41.1%)\n", "✅ 成功计算 1437 只股票\n", "\n", "=== 批次 14/54: 2015-06-30 -> 2016年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,047\n", "去重后记录数: 9,749\n", "去重比例: 69.4%\n", "计算完成：1756只股票，3个月窗口占比99.2%\n", "平均每份股票的报告数：5.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 8,174\n", "去重后记录数: 5,882\n", "去重比例: 72.0%\n", "计算完成：1522只股票，3个月窗口占比98.5%\n", "平均每份股票的报告数：3.7\n", "预测基准日期: 2015-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1756 只股票\n", "FY2数据覆盖: 1516 只股票 (86.3%)\n", "✅ 成功计算 1756 只股票\n", "\n", "=== 批次 15/54: 2015-09-30 -> 2016年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,351\n", "去重后记录数: 10,416\n", "去重比例: 72.6%\n", "计算完成：1753只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：5.9\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 8,918\n", "去重后记录数: 6,767\n", "去重比例: 75.9%\n", "计算完成：1569只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：4.3\n", "预测基准日期: 2015-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1753 只股票\n", "FY2数据覆盖: 1562 只股票 (89.1%)\n", "✅ 成功计算 1753 只股票\n", "\n", "=== 批次 16/54: 2015-12-31 -> 2016年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,996\n", "去重后记录数: 10,140\n", "去重比例: 72.4%\n", "计算完成：1846只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：5.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 9,580\n", "去重后记录数: 7,188\n", "去重比例: 75.0%\n", "计算完成：1696只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：4.2\n", "预测基准日期: 2015-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1846 只股票\n", "FY2数据覆盖: 1683 只股票 (91.2%)\n", "✅ 成功计算 1846 只股票\n", "\n", "=== 批次 17/54: 2016-03-31 -> 2017年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 10,629\n", "去重后记录数: 7,963\n", "去重比例: 74.9%\n", "计算完成：1713只股票，3个月窗口占比99.5%\n", "平均每份股票的报告数：4.6\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,220\n", "去重后记录数: 2,089\n", "去重比例: 94.1%\n", "计算完成：616只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：3.4\n", "预测基准日期: 2016-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1713 只股票\n", "FY2数据覆盖: 615 只股票 (35.9%)\n", "✅ 成功计算 1713 只股票\n", "\n", "=== 批次 18/54: 2016-06-30 -> 2017年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 17,810\n", "去重后记录数: 12,923\n", "去重比例: 72.6%\n", "计算完成：1958只股票，3个月窗口占比99.4%\n", "平均每份股票的报告数：6.4\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 11,224\n", "去重后记录数: 8,608\n", "去重比例: 76.7%\n", "计算完成：1774只股票，3个月窗口占比98.9%\n", "平均每份股票的报告数：4.7\n", "预测基准日期: 2016-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1958 只股票\n", "FY2数据覆盖: 1764 只股票 (90.1%)\n", "✅ 成功计算 1958 只股票\n", "\n", "=== 批次 19/54: 2016-09-30 -> 2017年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 20,005\n", "去重后记录数: 14,806\n", "去重比例: 74.0%\n", "计算完成：2004只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,939\n", "去重后记录数: 11,344\n", "去重比例: 75.9%\n", "计算完成：1897只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：5.9\n", "预测基准日期: 2016-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 2004 只股票\n", "FY2数据覆盖: 1890 只股票 (94.3%)\n", "✅ 成功计算 2004 只股票\n", "\n", "=== 批次 20/54: 2016-12-31 -> 2017年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 17,055\n", "去重后记录数: 13,086\n", "去重比例: 76.7%\n", "计算完成：2005只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：6.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,310\n", "去重后记录数: 9,755\n", "去重比例: 79.2%\n", "计算完成：1876只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：5.2\n", "预测基准日期: 2016-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 2005 只股票\n", "FY2数据覆盖: 1869 只股票 (93.2%)\n", "✅ 成功计算 2005 只股票\n", "\n", "=== 批次 21/54: 2017-03-31 -> 2018年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,466\n", "去重后记录数: 10,083\n", "去重比例: 74.9%\n", "计算完成：1839只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：5.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,295\n", "去重后记录数: 3,138\n", "去重比例: 95.2%\n", "计算完成：717只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：4.4\n", "预测基准日期: 2017-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1839 只股票\n", "FY2数据覆盖: 717 只股票 (39.0%)\n", "✅ 成功计算 1839 只股票\n", "\n", "=== 批次 22/54: 2017-06-30 -> 2018年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 21,729\n", "去重后记录数: 15,622\n", "去重比例: 71.9%\n", "计算完成：2093只股票，3个月窗口占比99.2%\n", "平均每份股票的报告数：7.2\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,776\n", "去重后记录数: 10,502\n", "去重比例: 76.2%\n", "计算完成：1885只股票，3个月窗口占比98.2%\n", "平均每份股票的报告数：5.3\n", "预测基准日期: 2017-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 2093 只股票\n", "FY2数据覆盖: 1881 只股票 (89.9%)\n", "✅ 成功计算 2093 只股票\n", "\n", "=== 批次 23/54: 2017-09-30 -> 2018年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 23,353\n", "去重后记录数: 16,897\n", "去重比例: 72.4%\n", "计算完成：2075只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：8.1\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 17,137\n", "去重后记录数: 13,079\n", "去重比例: 76.3%\n", "计算完成：1909只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.8\n", "预测基准日期: 2017-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 2075 只股票\n", "FY2数据覆盖: 1909 只股票 (92.0%)\n", "✅ 成功计算 2075 只股票\n", "\n", "=== 批次 24/54: 2017-12-31 -> 2018年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,069\n", "去重后记录数: 14,495\n", "去重比例: 76.0%\n", "计算完成：1927只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,446\n", "去重后记录数: 11,375\n", "去重比例: 78.7%\n", "计算完成：1792只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.3\n", "预测基准日期: 2017-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1927 只股票\n", "FY2数据覆盖: 1791 只股票 (92.9%)\n", "✅ 成功计算 1927 只股票\n", "\n", "=== 批次 25/54: 2018-03-31 -> 2019年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,655\n", "去重后记录数: 10,019\n", "去重比例: 73.4%\n", "计算完成：1654只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：6.0\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,725\n", "去重后记录数: 2,657\n", "去重比例: 97.5%\n", "计算完成：527只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：5.0\n", "预测基准日期: 2018-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1654 只股票\n", "FY2数据覆盖: 527 只股票 (31.9%)\n", "✅ 成功计算 1654 只股票\n", "\n", "=== 批次 26/54: 2018-06-30 -> 2019年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,091\n", "去重后记录数: 13,979\n", "去重比例: 73.2%\n", "计算完成：1844只股票，3个月窗口占比99.3%\n", "平均每份股票的报告数：7.4\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,098\n", "去重后记录数: 10,041\n", "去重比例: 76.7%\n", "计算完成：1618只股票，3个月窗口占比99.1%\n", "平均每份股票的报告数：6.1\n", "预测基准日期: 2018-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1844 只股票\n", "FY2数据覆盖: 1618 只股票 (87.7%)\n", "✅ 成功计算 1844 只股票\n", "\n", "=== 批次 27/54: 2018-09-30 -> 2019年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 18,662\n", "去重后记录数: 14,103\n", "去重比例: 75.6%\n", "计算完成：1720只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：8.2\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,909\n", "去重后记录数: 11,602\n", "去重比例: 77.8%\n", "计算完成：1591只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.3\n", "预测基准日期: 2018-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1720 只股票\n", "FY2数据覆盖: 1590 只股票 (92.4%)\n", "✅ 成功计算 1720 只股票\n", "\n", "=== 批次 28/54: 2018-12-31 -> 2019年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 15,981\n", "去重后记录数: 12,710\n", "去重比例: 79.5%\n", "计算完成：1616只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.9\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,457\n", "去重后记录数: 10,334\n", "去重比例: 83.0%\n", "计算完成：1481只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.0\n", "预测基准日期: 2018-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1616 只股票\n", "FY2数据覆盖: 1479 只股票 (91.5%)\n", "✅ 成功计算 1616 只股票\n", "\n", "=== 批次 29/54: 2019-03-31 -> 2020年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 11,641\n", "去重后记录数: 8,947\n", "去重比例: 76.9%\n", "计算完成：1480只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：6.0\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,874\n", "去重后记录数: 2,770\n", "去重比例: 96.4%\n", "计算完成：487只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：5.7\n", "预测基准日期: 2019-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1480 只股票\n", "FY2数据覆盖: 487 只股票 (32.9%)\n", "✅ 成功计算 1480 只股票\n", "\n", "=== 批次 30/54: 2019-06-30 -> 2020年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 18,420\n", "去重后记录数: 13,559\n", "去重比例: 73.6%\n", "计算完成：1729只股票，3个月窗口占比99.6%\n", "平均每份股票的报告数：7.7\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,170\n", "去重后记录数: 10,293\n", "去重比例: 78.2%\n", "计算完成：1547只股票，3个月窗口占比99.5%\n", "平均每份股票的报告数：6.5\n", "预测基准日期: 2019-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1729 只股票\n", "FY2数据覆盖: 1547 只股票 (89.5%)\n", "✅ 成功计算 1729 只股票\n", "\n", "=== 批次 31/54: 2019-09-30 -> 2020年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 18,872\n", "去重后记录数: 14,604\n", "去重比例: 77.4%\n", "计算完成：1744只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：8.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 15,101\n", "去重后记录数: 12,111\n", "去重比例: 80.2%\n", "计算完成：1636只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：7.4\n", "预测基准日期: 2019-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1744 只股票\n", "FY2数据覆盖: 1634 只股票 (93.7%)\n", "✅ 成功计算 1744 只股票\n", "\n", "=== 批次 32/54: 2019-12-31 -> 2020年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 17,248\n", "去重后记录数: 13,356\n", "去重比例: 77.4%\n", "计算完成：1631只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：8.2\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,132\n", "去重后记录数: 10,731\n", "去重比例: 81.7%\n", "计算完成：1467只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.3\n", "预测基准日期: 2019-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1631 只股票\n", "FY2数据覆盖: 1467 只股票 (89.9%)\n", "✅ 成功计算 1631 只股票\n", "\n", "=== 批次 33/54: 2020-03-31 -> 2021年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,903\n", "去重后记录数: 9,802\n", "去重比例: 76.0%\n", "计算完成：1519只股票，3个月窗口占比99.4%\n", "平均每份股票的报告数：6.4\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,034\n", "去重后记录数: 1,971\n", "去重比例: 96.9%\n", "计算完成：311只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.3\n", "预测基准日期: 2020-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1519 只股票\n", "FY2数据覆盖: 311 只股票 (20.5%)\n", "✅ 成功计算 1519 只股票\n", "\n", "=== 批次 34/54: 2020-06-30 -> 2021年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 22,615\n", "去重后记录数: 16,288\n", "去重比例: 72.0%\n", "计算完成：1806只股票，3个月窗口占比99.2%\n", "平均每份股票的报告数：8.7\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 16,047\n", "去重后记录数: 12,613\n", "去重比例: 78.6%\n", "计算完成：1661只股票，3个月窗口占比99.3%\n", "平均每份股票的报告数：7.3\n", "预测基准日期: 2020-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1806 只股票\n", "FY2数据覆盖: 1661 只股票 (92.0%)\n", "✅ 成功计算 1806 只股票\n", "\n", "=== 批次 35/54: 2020-09-30 -> 2021年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 20,096\n", "去重后记录数: 15,628\n", "去重比例: 77.8%\n", "计算完成：1831只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：8.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 16,602\n", "去重后记录数: 13,249\n", "去重比例: 79.8%\n", "计算完成：1719只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：7.7\n", "预测基准日期: 2020-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 1831 只股票\n", "FY2数据覆盖: 1718 只股票 (93.8%)\n", "✅ 成功计算 1831 只股票\n", "\n", "=== 批次 36/54: 2020-12-31 -> 2021年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 17,206\n", "去重后记录数: 13,681\n", "去重比例: 79.5%\n", "计算完成：1779只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.7\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,592\n", "去重后记录数: 11,954\n", "去重比例: 81.9%\n", "计算完成：1670只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.1\n", "预测基准日期: 2020-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1779 只股票\n", "FY2数据覆盖: 1667 只股票 (93.7%)\n", "✅ 成功计算 1779 只股票\n", "\n", "=== 批次 37/54: 2021-03-31 -> 2022年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 11,703\n", "去重后记录数: 8,930\n", "去重比例: 76.3%\n", "计算完成：1524只股票，3个月窗口占比99.1%\n", "平均每份股票的报告数：5.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,646\n", "去重后记录数: 2,448\n", "去重比例: 92.5%\n", "计算完成：432只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：5.7\n", "预测基准日期: 2021-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1524 只股票\n", "FY2数据覆盖: 432 只股票 (28.3%)\n", "✅ 成功计算 1524 只股票\n", "\n", "=== 批次 38/54: 2021-06-30 -> 2022年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 20,625\n", "去重后记录数: 15,391\n", "去重比例: 74.6%\n", "计算完成：1894只股票，3个月窗口占比98.9%\n", "平均每份股票的报告数：7.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 16,319\n", "去重后记录数: 12,616\n", "去重比例: 77.3%\n", "计算完成：1777只股票，3个月窗口占比98.6%\n", "平均每份股票的报告数：6.8\n", "预测基准日期: 2021-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 1894 只股票\n", "FY2数据覆盖: 1775 只股票 (93.7%)\n", "✅ 成功计算 1894 只股票\n", "\n", "=== 批次 39/54: 2021-09-30 -> 2022年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 20,917\n", "去重后记录数: 16,227\n", "去重比例: 77.6%\n", "计算完成：2001只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：8.1\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 18,220\n", "去重后记录数: 14,284\n", "去重比例: 78.4%\n", "计算完成：1940只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.3\n", "预测基准日期: 2021-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 2001 只股票\n", "FY2数据覆盖: 1938 只股票 (96.9%)\n", "✅ 成功计算 2001 只股票\n", "\n", "=== 批次 40/54: 2021-12-31 -> 2022年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 17,667\n", "去重后记录数: 14,300\n", "去重比例: 80.9%\n", "计算完成：1966只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：7.3\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 15,018\n", "去重后记录数: 12,383\n", "去重比例: 82.5%\n", "计算完成：1902只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：6.5\n", "预测基准日期: 2021-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 1966 只股票\n", "FY2数据覆盖: 1892 只股票 (96.2%)\n", "✅ 成功计算 1966 只股票\n", "\n", "=== 批次 41/54: 2022-03-31 -> 2023年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 14,115\n", "去重后记录数: 10,905\n", "去重比例: 77.3%\n", "计算完成：1825只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：5.9\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,087\n", "去重后记录数: 2,978\n", "去重比例: 96.5%\n", "计算完成：463只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.4\n", "预测基准日期: 2022-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1825 只股票\n", "FY2数据覆盖: 463 只股票 (25.4%)\n", "✅ 成功计算 1825 只股票\n", "\n", "=== 批次 42/54: 2022-06-30 -> 2023年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 27,515\n", "去重后记录数: 20,435\n", "去重比例: 74.3%\n", "计算完成：2260只股票，3个月窗口占比99.3%\n", "平均每份股票的报告数：8.8\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 22,274\n", "去重后记录数: 17,275\n", "去重比例: 77.6%\n", "计算完成：2172只股票，3个月窗口占比99.3%\n", "平均每份股票的报告数：7.7\n", "预测基准日期: 2022-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 2260 只股票\n", "FY2数据覆盖: 2169 只股票 (96.0%)\n", "✅ 成功计算 2260 只股票\n", "\n", "=== 批次 43/54: 2022-09-30 -> 2023年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 25,546\n", "去重后记录数: 20,348\n", "去重比例: 79.7%\n", "计算完成：2327只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：8.7\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 22,635\n", "去重后记录数: 18,219\n", "去重比例: 80.5%\n", "计算完成：2272只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：8.0\n", "预测基准日期: 2022-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 2327 只股票\n", "FY2数据覆盖: 2262 只股票 (97.2%)\n", "✅ 成功计算 2327 只股票\n", "\n", "=== 批次 44/54: 2022-12-31 -> 2023年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 23,845\n", "去重后记录数: 19,762\n", "去重比例: 82.9%\n", "计算完成：2420只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：8.1\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 20,709\n", "去重后记录数: 17,536\n", "去重比例: 84.7%\n", "计算完成：2368只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.4\n", "预测基准日期: 2022-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 2420 只股票\n", "FY2数据覆盖: 2353 只股票 (97.2%)\n", "✅ 成功计算 2420 只股票\n", "\n", "=== 批次 45/54: 2023-03-31 -> 2024年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 13,859\n", "去重后记录数: 11,484\n", "去重比例: 82.9%\n", "计算完成：2069只股票，3个月窗口占比99.5%\n", "平均每份股票的报告数：5.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,024\n", "去重后记录数: 2,970\n", "去重比例: 98.2%\n", "计算完成：451只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.6\n", "预测基准日期: 2023-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 2069 只股票\n", "FY2数据覆盖: 450 只股票 (21.7%)\n", "✅ 成功计算 2069 只股票\n", "\n", "=== 批次 46/54: 2023-06-30 -> 2024年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 30,103\n", "去重后记录数: 23,718\n", "去重比例: 78.8%\n", "计算完成：2709只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：8.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 25,376\n", "去重后记录数: 20,731\n", "去重比例: 81.7%\n", "计算完成：2635只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：7.6\n", "预测基准日期: 2023-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 2709 只股票\n", "FY2数据覆盖: 2626 只股票 (96.9%)\n", "✅ 成功计算 2709 只股票\n", "\n", "=== 批次 47/54: 2023-09-30 -> 2024年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 28,358\n", "去重后记录数: 23,602\n", "去重比例: 83.2%\n", "计算完成：2716只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：8.7\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 25,634\n", "去重后记录数: 21,510\n", "去重比例: 83.9%\n", "计算完成：2691只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：8.0\n", "预测基准日期: 2023-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 2716 只股票\n", "FY2数据覆盖: 2673 只股票 (98.4%)\n", "✅ 成功计算 2716 只股票\n", "\n", "=== 批次 48/54: 2023-12-31 -> 2024年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 23,558\n", "去重后记录数: 20,803\n", "去重比例: 88.3%\n", "计算完成：2645只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.9\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 21,033\n", "去重后记录数: 18,765\n", "去重比例: 89.2%\n", "计算完成：2615只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：7.2\n", "预测基准日期: 2023-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 2645 只股票\n", "FY2数据覆盖: 2586 只股票 (97.8%)\n", "✅ 成功计算 2645 只股票\n", "\n", "=== 批次 49/54: 2024-03-31 -> 2025年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 12,616\n", "去重后记录数: 10,726\n", "去重比例: 85.0%\n", "计算完成：2121只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：5.0\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 3,035\n", "去重后记录数: 3,010\n", "去重比例: 99.2%\n", "计算完成：457只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：6.6\n", "预测基准日期: 2024-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 2121 只股票\n", "FY2数据覆盖: 456 只股票 (21.5%)\n", "✅ 成功计算 2121 只股票\n", "\n", "=== 批次 50/54: 2024-06-30 -> 2025年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 28,065\n", "去重后记录数: 23,595\n", "去重比例: 84.1%\n", "计算完成：2712只股票，3个月窗口占比99.6%\n", "平均每份股票的报告数：8.5\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 24,463\n", "去重后记录数: 21,144\n", "去重比例: 86.4%\n", "计算完成：2680只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：7.7\n", "预测基准日期: 2024-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 2712 只股票\n", "FY2数据覆盖: 2661 只股票 (98.1%)\n", "✅ 成功计算 2712 只股票\n", "\n", "=== 批次 51/54: 2024-09-30 -> 2025年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 25,887\n", "去重后记录数: 22,157\n", "去重比例: 85.6%\n", "计算完成：2579只股票，3个月窗口占比99.8%\n", "平均每份股票的报告数：8.6\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 23,485\n", "去重后记录数: 20,294\n", "去重比例: 86.4%\n", "计算完成：2564只股票，3个月窗口占比99.9%\n", "平均每份股票的报告数：7.9\n", "预测基准日期: 2024-09-30\n", "当前月份: 9\n", "FY1权重: 0.250, FY2权重: 0.750\n", "滚动一致预期计算完成: 2579 只股票\n", "FY2数据覆盖: 2539 只股票 (98.4%)\n", "✅ 成功计算 2579 只股票\n", "\n", "=== 批次 52/54: 2024-12-31 -> 2025年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 21,288\n", "去重后记录数: 19,138\n", "去重比例: 89.9%\n", "计算完成：2521只股票，3个月窗口占比99.7%\n", "平均每份股票的报告数：7.6\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 19,230\n", "去重后记录数: 17,473\n", "去重比例: 90.9%\n", "计算完成：2496只股票，3个月窗口占比99.6%\n", "平均每份股票的报告数：7.0\n", "预测基准日期: 2024-12-31\n", "当前月份: 12\n", "FY1权重: 0.000, FY2权重: 1.000\n", "滚动一致预期计算完成: 2521 只股票\n", "FY2数据覆盖: 2457 只股票 (97.5%)\n", "✅ 成功计算 2521 只股票\n", "\n", "=== 批次 53/54: 2025-03-31 -> 2026年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 8,943\n", "去重后记录数: 7,938\n", "去重比例: 88.8%\n", "计算完成：1942只股票，3个月窗口占比98.8%\n", "平均每份股票的报告数：4.0\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 2,338\n", "去重后记录数: 2,325\n", "去重比例: 99.4%\n", "计算完成：436只股票，3个月窗口占比100.0%\n", "平均每份股票的报告数：5.3\n", "预测基准日期: 2025-03-31\n", "当前月份: 3\n", "FY1权重: 0.750, FY2权重: 0.250\n", "滚动一致预期计算完成: 1942 只股票\n", "FY2数据覆盖: 435 只股票 (22.4%)\n", "✅ 成功计算 1942 只股票\n", "\n", "=== 批次 54/54: 2025-06-30 -> 2026年 ===\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 24,437\n", "去重后记录数: 21,083\n", "去重比例: 86.3%\n", "计算完成：2753只股票，3个月窗口占比99.6%\n", "平均每份股票的报告数：7.4\n", "=== 分析师自我修正处理 ===\n", "去重前记录数: 21,376\n", "去重后记录数: 18,845\n", "去重比例: 88.2%\n", "计算完成：2716只股票，3个月窗口占比99.6%\n", "平均每份股票的报告数：6.7\n", "预测基准日期: 2025-06-30\n", "当前月份: 6\n", "FY1权重: 0.500, FY2权重: 0.500\n", "滚动一致预期计算完成: 2753 只股票\n", "FY2数据覆盖: 2689 只股票 (97.7%)\n", "✅ 成功计算 2753 只股票\n", "\n", "=== 批量计算完成 ===\n", "总计算结果: 102,240 条记录\n", "涉及股票数: 4,892\n", "涉及时点数: 54\n", "时间范围: 2012-03-31 00:00:00 到 2025-06-30 00:00:00\n", "\n", "=== 保存完整结果 ===\n", "✅ 完整结果已保存:\n", "  - processed_data/consensus_results_complete.feather\n", "  - processed_data/consensus_results_complete.csv\n", "\n", "✅ 主要结果文件已更新\n", "✅ 计算统计信息已保存: processed_data/consensus_calculation_stats_complete.json\n", "\n", "=== 最终统计 ===\n", "总记录数: 102,240\n", "涉及股票: 4,892\n", "时间点数: 54\n", "时间范围: 2012-03-31 00:00:00 到 2025-06-30 00:00:00\n", "目标年份: 2013 到 2026\n", "平均分析师数: 7.27\n", "\n", "=== 按预测年份统计 ===\n", "                股票数  时点数\n", "forecast_year           \n", "2013           1933    4\n", "2014           1822    4\n", "2015           1870    4\n", "2016           2139    4\n", "2017           2321    4\n", "2018           2500    4\n", "2019           2191    4\n", "2020           2102    4\n", "2021           2236    4\n", "2022           2448    4\n", "2023           2841    4\n", "2024           3230    4\n", "2025           3199    4\n", "2026           2899    2\n"]}], "source": ["# 执行完整批量计算\n", "print(\"=== 开始完整批量计算 ===\")\n", "complete_results = batch_calculate_consensus_complete(\n", "    df_forecast, prediction_dates, target_years\n", ")\n", "\n", "if len(complete_results) > 0:\n", "    print(f\"\\n=== 保存完整结果 ===\")\n", "    \n", "    # 保存主要结果文件\n", "    complete_results.to_feather('processed_data/consensus_results_complete.feather')\n", "    complete_results.to_csv('processed_data/consensus_results_complete.csv', index=False, encoding='utf-8-sig')\n", "    print(\"✅ 完整结果已保存:\")\n", "    print(\"  - processed_data/consensus_results_complete.feather\")\n", "    print(\"  - processed_data/consensus_results_complete.csv\")\n", "    \n", "    # 更新主要结果文件（覆盖原有的不完整版本）\n", "    complete_results.to_feather('processed_data/consensus_results.feather')\n", "    complete_results.to_csv('processed_data/consensus_results.csv', index=False, encoding='utf-8-sig')\n", "    print(\"\\n✅ 主要结果文件已更新\")\n", "    \n", "    # 保存计算统计信息\n", "    calculation_stats = {\n", "        'calculation_method': 'complete_time_series',\n", "        'total_time_points': len(prediction_dates),\n", "        'successful_calculations': complete_results['prediction_date'].nunique(),\n", "        'total_records': len(complete_results),\n", "        'unique_stocks': complete_results['stock_code'].nunique(),\n", "        'time_range': {\n", "            'start': complete_results['prediction_date'].min().strftime('%Y-%m-%d'),\n", "            'end': complete_results['prediction_date'].max().strftime('%Y-%m-%d')\n", "        },\n", "        'target_years': {\n", "            'min': int(complete_results['forecast_year'].min()),\n", "            'max': int(complete_results['forecast_year'].max())\n", "        },\n", "        'avg_analyst_count': float(complete_results['analyst_count'].mean()),\n", "        'avg_report_count': float(complete_results['report_count'].mean()),\n", "        'calculation_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')\n", "    }\n", "    \n", "    with open('processed_data/consensus_calculation_stats_complete.json', 'w', encoding='utf-8') as f:\n", "        json.dump(calculation_stats, f, ensure_ascii=False, indent=2)\n", "    print(\"✅ 计算统计信息已保存: processed_data/consensus_calculation_stats_complete.json\")\n", "    \n", "    # 显示最终统计\n", "    print(f\"\\n=== 最终统计 ===\")\n", "    print(f\"总记录数: {len(complete_results):,}\")\n", "    print(f\"涉及股票: {complete_results['stock_code'].nunique():,}\")\n", "    print(f\"时间点数: {complete_results['prediction_date'].nunique()}\")\n", "    print(f\"时间范围: {complete_results['prediction_date'].min()} 到 {complete_results['prediction_date'].max()}\")\n", "    print(f\"目标年份: {complete_results['forecast_year'].min()} 到 {complete_results['forecast_year'].max()}\")\n", "    print(f\"平均分析师数: {complete_results['analyst_count'].mean():.2f}\")\n", "    \n", "    # 按年份统计\n", "    print(f\"\\n=== 按预测年份统计 ===\")\n", "    year_stats = complete_results.groupby('forecast_year').agg({\n", "        'stock_code': 'nunique',\n", "        'prediction_date': 'nunique'\n", "    }).rename(columns={'stock_code': '股票数', 'prediction_date': '时点数'})\n", "    print(year_stats)\n", "    \n", "else:\n", "    print(\"\\n❌ 完整计算失败\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "### 📊 输出文件\n", "- `consensus_results_complete.feather/csv`：完整的一致预期结果\n", "- `consensus_results.feather/csv`：更新的主要结果文件\n", "- `consensus_calculation_stats_complete.json`：计算统计信息"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}