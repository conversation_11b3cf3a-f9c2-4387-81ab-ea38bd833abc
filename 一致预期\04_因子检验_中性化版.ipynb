{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 05 - 因子检验\n", "\n", "## 检验流程\n", "1. **加载因子数据**：从04文件生成的因子\n", "2. **因子中性化**：对每个因子进行行业、市值中性化\n", "3. **IC分析**：计算信息系数\n", "4. **分层回测**：十分位数分组回测\n", "5. **累计收益**：绘制累计收益曲线"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境加载完成\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "import os\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print('环境加载完成')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载因子数据"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载中性化因子数据 ===\n", "✅ 加载中性化因子数据\n", "因子数据形状: (102240, 11)\n", "时间范围: 2012-03-31 到 2025-06-30\n", "股票数量: 4,892\n", "\n", "=== 中性化因子覆盖情况 ===\n", "EP_PURE: 83,239 条记录 (81.4%)\n", "EP_ROLL_PURE: 83,239 条记录 (81.4%)\n"]}], "source": ["# 1. 加载因子数据\n", "print('=== 加载中性化因子数据 ===')\n", "try:\n", "    df_factors = pd.read_feather('processed_data/derived_factors_neutralized.feather')\n", "    print('✅ 加载中性化因子数据')\n", "except FileNotFoundError:\n", "    print('❌ 未找到因子数据，请先运行04文件')\n", "    raise\n", "\n", "print(f'因子数据形状: {df_factors.shape}')\n", "print(f'时间范围: {df_factors[\"date\"].min()} 到 {df_factors[\"date\"].max()}')\n", "print(f'股票数量: {df_factors[\"stock_code\"].nunique():,}')\n", "\n", "neutralized_factor_cols = ['EP_PURE', 'EP_ROLL_PURE']  \n", "print('\\n=== 中性化因子覆盖情况 ===')\n", "for col in neutralized_factor_cols:\n", "    if col in df_factors.columns:\n", "        coverage = df_factors[col].notna().sum()\n", "        print(f'{col}: {coverage:,} 条记录 ({coverage/len(df_factors)*100:.1f}%)')\n", "    else:\n", "        print(f'{col}: 未找到')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. 加载收益率数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载收益率数据 ===\n", "收益率数据：13,002,395 条记录\n", "时间范围：2009-01-05 00:00:00 到 2025-05-30 00:00:00\n", "\n", "=== 加载指数收益率数据 ===\n", "指数收益率数据：6,191 条记录\n", "时间范围：2000-01-04 00:00:00 到 2025-07-22 00:00:00\n", "指数收益率统计：均值=0.0644%, 标准差=1.7636%\n"]}], "source": ["print('=== 加载收益率数据 ===')\n", "\n", "h5_file_path = r'C:\\Users\\<USER>\\Desktop\\金元顺安\\一致预期\\data\\daily0925.h5'\n", "dataset_name = 'data'  \n", "\n", "try:\n", "    df_daily = pd.read_hdf(h5_file_path, dataset_name)\n", "    if df_daily.index.names is not None:\n", "        df_daily = df_daily.reset_index()\n", "\n", "    df_daily = df_daily.sort_values(['ts_code', 'trade_date'])\n", "    df_daily['return'] = df_daily.groupby('ts_code')['close'].pct_change() * 100\n", "    df_daily = df_daily[df_daily['return'].abs() <= 21]\n", "    df_returns = df_daily[['ts_code', 'trade_date', 'return']].copy()\n", "    \n", "    print(f'收益率数据：{len(df_returns):,} 条记录')\n", "    print(f'时间范围：{df_returns[\"trade_date\"].min()} 到 {df_returns[\"trade_date\"].max()}')\n", "\n", "except Exception as e:\n", "    print(f'❌ 加载及计算收益率数据失败：{e}')\n", "    df_returns = None\n", "\n", "print('\\n=== 加载指数收益率数据 ===')\n", "\n", "try:\n", "    df_index = pd.read_csv('data/windaew.csv')\n", "    df_index['date'] = pd.to_datetime(df_index['date'])\n", "    df_index = df_index.sort_values('date')\n", "    \n", "    # 计算指数收益率\n", "    df_index['index_return'] = df_index['close'].pct_change() * 100\n", "    df_index = df_index.dropna()\n", "    \n", "    # 重命名列以匹配股票数据格式\n", "    df_index_returns = df_index[['date', 'index_return']].copy()\n", "    df_index_returns.rename(columns={'date': 'trade_date'}, inplace=True)\n", "    \n", "    print(f'指数收益率数据：{len(df_index_returns):,} 条记录')\n", "    print(f'时间范围：{df_index_returns[\"trade_date\"].min()} 到 {df_index_returns[\"trade_date\"].max()}')\n", "    print(f'指数收益率统计：均值={df_index_returns[\"index_return\"].mean():.4f}%, 标准差={df_index_returns[\"index_return\"].std():.4f}%')\n", "\n", "except Exception as e:\n", "    print(f'❌ 加载windaew.csv指数数据失败：{e}')\n", "    df_index_returns = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. IC分析"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "收益率数据列名: ['ts_code', 'trade_date', 'return']\n", "\n", "=== EP_PURE IC分析 ===\n", "平均IC: 0.0459\n", "IC标准差: 0.0962\n", "信息比率(IR): 0.4770\n", "胜率: 68.3%\n", "有效月度样本数: 41\n", "\n", "=== EP_ROLL_PURE IC分析 ===\n", "平均IC: 0.0456\n", "IC标准差: 0.0940\n", "信息比率(IR): 0.4850\n", "胜率: 68.3%\n", "有效月度样本数: 41\n"]}], "source": ["# 3. IC分析\n", "def calculate_ic_analysis(factor_data, return_data, factor_col):\n", "    print(f'\\n=== {factor_col} IC分析 ===')\n", "    factor_data['date'] = pd.to_datetime(factor_data['date'])\n", "    return_data['trade_date'] = pd.to_datetime(return_data['trade_date'])\n", "    \n", "    # 1. 筛选有效报告（研报4.1节：3个月窗口）\n", "    factor_data['pred_month'] = factor_data['date'].dt.to_period('M')\n", "    valid_mask = factor_data.groupby('stock_code')['pred_month'].transform(\n", "        lambda x: x.between(x - 2, x)\n", "    )\n", "    factor_data_valid = factor_data[valid_mask].copy()\n", "    if len(factor_data_valid) == 0:\n", "        print('❌ 无有效报告，IC分析终止')\n", "        return pd.DataFrame()\n", "    \n", "    # 2. 匹配下期收益（研报6.1节：下一个交易日）\n", "    all_trade_dates = return_data['trade_date'].drop_duplicates().sort_values().tolist()\n", "    ic_results = []\n", "    \n", "    for pred_date, pred_group in factor_data_valid.groupby('date'):\n", "        next_trade_dates = [d for d in all_trade_dates if d > pred_date]\n", "        if not next_trade_dates:\n", "            continue\n", "        target_trade_date = next_trade_dates[0]\n", "        next_returns = return_data[return_data['trade_date'] == target_trade_date].copy()\n", "        if len(next_returns) == 0:\n", "            continue\n", "        \n", "        # 3. 合并数据并计算IC\n", "        merged = pred_group.merge(\n", "            next_returns[['ts_code', 'return']],\n", "            left_on='stock_code',\n", "            right_on='ts_code',\n", "            how='inner'\n", "        ).dropna(subset=[factor_col, 'return'])\n", "        if len(merged) < 20:\n", "            continue\n", "        \n", "        ic, p_value = stats.spearmanr(merged[factor_col], merged['return'])\n", "        ic_results.append({\n", "            'date': pred_date, \n", "            'target_trade_date': target_trade_date,\n", "            'ic': ic,\n", "            'p_value': p_value,\n", "            'sample_size': len(merged),\n", "            'pred_month': pred_group['pred_month'].iloc[0]\n", "        })\n", "    \n", "    if not ic_results:\n", "        return pd.DataFrame()\n", "    \n", "    # 4. 输出标准统计\n", "    ic_df = pd.DataFrame(ic_results)\n", "    mean_ic = ic_df['ic'].mean()\n", "    std_ic = ic_df['ic'].std()\n", "    ir = mean_ic / std_ic if std_ic > 0 else 0\n", "    win_rate = (ic_df['ic'] > 0).mean()\n", "    valid_monthly_samples = ic_df['pred_month'].nunique()\n", "    \n", "    print(f'平均IC: {mean_ic:.4f}')\n", "    print(f'IC标准差: {std_ic:.4f}')\n", "    print(f'信息比率(IR): {ir:.4f}')\n", "    print(f'胜率: {win_rate:.1%}')\n", "    print(f'有效月度样本数: {valid_monthly_samples}')\n", "    \n", "    return ic_df\n", "\n", "# 执行IC分析\n", "ic_results = {}\n", "if df_returns is not None:\n", "    print(f\"\\n收益率数据列名: {df_returns.columns.tolist()}\")\n", "    for factor_col in neutralized_factor_cols:\n", "        if factor_col in df_factors.columns:\n", "            ic_df = calculate_ic_analysis(df_factors, df_returns, factor_col)\n", "            if not ic_df.empty:\n", "                ic_results[factor_col] = ic_df\n", "else:\n", "    print('⚠️ 无收益率数据，跳过IC分析')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 分层回测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== EP_PURE 分层回测 ===\n", "分组收益统计:\n", "         平均收益    收益波动  样本数      夏普比率\n", "group                               \n", "1     -0.1681  0.4146   41 -0.405451\n", "2     -0.1630  0.7112   41 -0.229190\n", "3     -0.1248  0.6580   41 -0.189666\n", "4     -0.1506  0.6019   41 -0.250208\n", "5     -0.0618  0.4674   41 -0.132221\n", "6     -0.0544  0.5307   41 -0.102506\n", "7     -0.0594  0.4233   41 -0.140326\n", "8     -0.0595  0.4440   41 -0.134009\n", "9      0.0039  0.5354   41  0.007284\n", "10     0.0915  0.7079   41  0.129256\n", "\n", "多空收益 (G10 - G1): 0.2597\n", "=== EP_ROLL_PURE 分层回测 ===\n", "分组收益统计:\n", "         平均收益    收益波动  样本数      夏普比率\n", "group                               \n", "1     -0.1686  0.4039   41 -0.417430\n", "2     -0.1449  0.7020   41 -0.206410\n", "3     -0.1195  0.6473   41 -0.184613\n", "4     -0.1460  0.6027   41 -0.242243\n", "5     -0.0841  0.4998   41 -0.168267\n", "6     -0.0858  0.4896   41 -0.175245\n", "7     -0.0717  0.4370   41 -0.164073\n", "8     -0.0570  0.4332   41 -0.131579\n", "9      0.0175  0.5184   41  0.033758\n", "10     0.1139  0.6565   41  0.173496\n", "\n", "多空收益 (G10 - G1): 0.2824\n"]}], "source": ["# 4. 分层回测\n", "def factor_layered_backtest(factor_data, return_data, factor_col, n_groups=10):\n", "    print(f'=== {factor_col} 分层回测 ===')\n", "    group_returns = []\n", "    \n", "    for pred_date, group in factor_data.groupby('date'):\n", "        pred_date = pd.to_datetime(pred_date)\n", "        next_date = pred_date + pd.Timedel<PERSON>(days=1)\n", "        next_returns = None\n", "        # 寻找30天内的下期交易日数据\n", "        for i in range(30):\n", "            target_date = next_date + pd.Timedelta(days=i)\n", "            next_returns = return_data[return_data['trade_date'] == target_date]\n", "            if len(next_returns) > 0:\n", "                break\n", "        if next_returns is None or len(next_returns) == 0:\n", "            continue\n", "        \n", "        # 合并数据\n", "        merged = group.merge(\n", "            next_returns[['ts_code', 'return']],\n", "            left_on='stock_code', \n", "            right_on='ts_code', \n", "            how='inner'\n", "        ).dropna(subset=[factor_col, 'return'])\n", "        \n", "        # 获取对应日期的指数收益率\n", "        target_date = next_returns['trade_date'].iloc[0] if len(next_returns) > 0 else None\n", "        index_return = 0  # 默认指数收益率为0\n", "        if target_date is not None and df_index_returns is not None:\n", "            index_mask = df_index_returns['trade_date'] == target_date\n", "            if index_mask.sum() > 0:\n", "                index_return = df_index_returns[index_mask]['index_return'].iloc[0]\n", "        \n", "        # 实现对冲逻辑：股票收益 - 指数收益\n", "        merged['hedged_return'] = merged['return'] - index_return\n", "        \n", "        if len(merged) < n_groups * 5:  # 每组至少5只股票\n", "            continue\n", "        \n", "        # 十分位数分组\n", "        merged['factor_rank'] = pd.qcut(merged[factor_col], n_groups, labels=False, duplicates='drop')\n", "        group_ret = merged.groupby('factor_rank')['hedged_return'].mean()  # 使用对冲后的收益率\n", "        \n", "        for rank, ret in group_ret.items():\n", "            group_returns.append({\n", "                'date': pred_date,\n", "                'group': rank + 1,  # 分组从1开始（1=最低因子值，10=最高因子值）\n", "                'return': ret\n", "            })\n", "    \n", "    if not group_returns:\n", "        print('❌ 无法进行分层回测')\n", "        return pd.DataFrame(), pd.DataFrame()\n", "    \n", "    backtest_df = pd.DataFrame(group_returns)\n", "    # 计算分组统计\n", "    group_stats = backtest_df.groupby('group')['return'].agg([\n", "        'mean', 'std', 'count'  # 平均收益、收益波动、样本数\n", "    ]).round(4)\n", "    group_stats['sharpe'] = group_stats['mean'] / group_stats['std']  # 夏普比率\n", "    group_stats.columns = ['平均收益', '收益波动', '样本数', '夏普比率']\n", "    \n", "    print('分组收益统计:')\n", "    print(group_stats)\n", "    \n", "    # 计算多空收益（G10 - G1，研报核心评价指标）\n", "    if 1 in backtest_df['group'].values and n_groups in backtest_df['group'].values:\n", "        long_return = backtest_df[backtest_df['group'] == n_groups]['return'].mean()\n", "        short_return = backtest_df[backtest_df['group'] == 1]['return'].mean()\n", "        long_short = long_return - short_return\n", "        print(f'\\n多空收益 (G{n_groups} - G1): {long_short:.4f}')\n", "    else:\n", "        long_short = np.nan\n", "        print(f'\\n多空收益: 分组不完整，无法计算')\n", "    \n", "    return backtest_df, group_stats, long_short\n", "\n", "# 执行分层回测\n", "backtest_results = {}\n", "if df_returns is not None:\n", "    for factor_col in neutralized_factor_cols:\n", "        if factor_col in df_factors.columns:\n", "            backtest_df, group_stats, long_short = factor_layered_backtest(\n", "                df_factors, df_returns, factor_col\n", "            )\n", "            if not backtest_df.empty:\n", "                backtest_results[factor_col] = {\n", "                    'backtest_df': backtest_df,\n", "                    'group_stats': group_stats,\n", "                    'long_short_return': long_short\n", "                }\n", "else:\n", "    print('⚠️ 无收益率数据，跳过分层回测')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 结果可视化"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== EP_PURE 绩效统计 ===\n", "       指标    数值\n", "年化收益（G10） 1.07%\n", "   多空年化收益 3.13%\n", "     多空胜率 68.3%\n", "     多空IR  0.48\n", "     最大回撤 -2.7%\n", "     平均IC 0.046\n", "    IC-IR  0.48\n", "     IC胜率 68.3%\n", "   有效月度样本    41\n", "\n", "=== EP_PURE 分年绩效统计 ===\n", "      年化收益(%)  多空胜率   多空IR  最大回撤(%)     IC  IC-IR  IC胜率\n", "year                                                   \n", "2015    0.819  0.75  2.979   -0.088  0.098  0.786  0.75\n", "2016    1.231  0.75  2.445   -0.073  0.040  0.858  0.75\n", "2017    7.632  0.75  3.665   -0.118  0.073  1.042  1.00\n", "2018   -6.426  0.25 -4.027   -1.075 -0.063 -1.011  0.00\n", "2019    4.020  1.00  5.280    0.000  0.050  1.576  1.00\n", "2020    0.408  0.25  0.282   -1.005 -0.002 -0.017  0.25\n", "2021    3.583  1.00  4.597    0.000  0.080  0.614  0.75\n", "2022    5.548  0.75  4.002   -0.127  0.100  1.176  0.75\n", "2023    1.088  0.75  2.696    0.000  0.063  0.644  0.75\n", "2024   -5.913  0.50 -0.708   -2.694  0.013  0.083  0.75\n", "2025    0.683  1.00    inf    0.000  0.073    NaN  1.00\n"]}, {"data": {"image/png": "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**************************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", "text/plain": ["<Figure size 1500x1200 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== EP_ROLL_PURE 绩效统计 ===\n", "       指标    数值\n", "年化收益（G10） 1.35%\n", "   多空年化收益 3.41%\n", "     多空胜率 68.3%\n", "     多空IR  0.49\n", "     最大回撤 -2.6%\n", "     平均IC 0.046\n", "    IC-IR  0.49\n", "     IC胜率 68.3%\n", "   有效月度样本    41\n", "\n", "=== EP_ROLL_PURE 分年绩效统计 ===\n", "      年化收益(%)  多空胜率   多空IR  最大回撤(%)     IC  IC-IR  IC胜率\n", "year                                                   \n", "2015    1.901  1.00  3.737    0.000  0.104  0.808  0.75\n", "2016    1.289  0.75  1.951   -0.057  0.035  0.720  0.75\n", "2017    7.230  0.75  3.655   -0.104  0.076  1.121  1.00\n", "2018   -6.154  0.25 -3.870   -0.976 -0.064 -1.008  0.00\n", "2019    4.703  1.00  6.297    0.000  0.052  1.749  1.00\n", "2020   -0.190  0.25  0.339   -0.763  0.001  0.013  0.25\n", "2021    4.196  1.00  4.841    0.000  0.078  0.625  0.75\n", "2022    5.704  0.75  2.922   -0.394  0.097  1.167  0.75\n", "2023    0.869  0.75  3.122    0.000  0.056  0.656  0.75\n", "2024   -4.780  0.50 -0.631   -2.632  0.015  0.103  0.75\n", "2025    0.416  1.00    inf    0.000  0.072    NaN  1.00\n"]}], "source": ["# 5. 结果可视化\n", "def plot_factor_performance(ic_results, backtest_results, factor_name):\n", "    if factor_name not in ic_results or factor_name not in backtest_results:\n", "        print(f'⚠️ {factor_name} 结果不完整，跳过绘图')\n", "        return\n", "    \n", "    # 提取数据并验证关键列\n", "    ic_df = ic_results[factor_name].copy()\n", "    backtest_df = backtest_results[factor_name]['backtest_df'].copy()\n", "    group_stats = backtest_results[factor_name]['group_stats'].copy()\n", "    long_short = backtest_results[factor_name]['long_short_return']\n", "    n_groups = 10  # 保持原有硬编码方式\n", "    \n", "    # 确保日期列存在\n", "    if 'date' not in ic_df.columns:\n", "        raise ValueError(f'❌ IC结果中缺少\"date\"列，当前列名：{ic_df.columns.tolist()}')\n", "    if 'date' not in backtest_df.columns:\n", "        raise ValueError(f'❌ 回测结果中缺少\"date\"列，当前列名：{backtest_df.columns.tolist()}')\n", "    if '平均收益' not in group_stats.columns:\n", "        raise ValueError(f'❌ 分组统计中缺少\"平均收益\"列，当前列名：{group_stats.columns.tolist()}')\n", "    \n", "    fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "    fig.suptitle(f'{factor_name} 因子表现分析', fontsize=16)\n", "    \n", "\n", "    # 图1：IC时间序列\n", "    axes[0, 0].plot(ic_df['date'], ic_df['ic'], color='royalblue', alpha=0.8, linewidth=2)\n", "    axes[0, 0].axhline(y=0, color='red', linestyle='--', alpha=0.6)\n", "    axes[0, 0].set_title('IC时间序列')\n", "    axes[0, 0].set_ylabel('IC值')\n", "    axes[0, 0].grid(True, alpha=0.3)\n", "    mean_ic = ic_df['ic'].mean()\n", "    axes[0, 0].axhline(y=mean_ic, color='orange', linestyle='-.', alpha=0.6, \n", "                      label=f'平均IC: {mean_ic:.4f}')\n", "    axes[0, 0].legend()\n", "    \n", "\n", "    # 图2：IC分布\n", "    axes[0, 1].hist(ic_df['ic'], bins=20, alpha=0.7, edgecolor='black', color='lightgreen')\n", "    axes[0, 1].axvline(x=mean_ic, color='red', linestyle='--', \n", "                      label=f'均值: {mean_ic:.4f}')\n", "    std_ic = ic_df['ic'].std()\n", "    axes[0, 1].axvline(x=mean_ic + std_ic, color='orange', linestyle='-.', alpha=0.6,\n", "                      label=f'+1σ: {mean_ic + std_ic:.4f}')\n", "    axes[0, 1].axvline(x=mean_ic - std_ic, color='orange', linestyle='-.', alpha=0.6,\n", "                      label=f'-1σ: {mean_ic - std_ic:.4f}')\n", "    axes[0, 1].set_title('IC分布')\n", "    axes[0, 1].set_xlabel('IC值')\n", "    axes[0, 1].set_ylabel('频数')\n", "    axes[0, 1].legend()\n", "    axes[0, 1].grid(True, alpha=0.3)\n", "    \n", "\n", "    # 图3：分组收益\n", "    group_means = group_stats['平均收益']\n", "    groups = sorted(group_means.index)\n", "    group_means_sorted = group_means[groups]\n", "    colors = ['red' if x < 0 else 'green' for x in group_means_sorted]\n", "    bars = axes[1, 0].bar(\n", "        [str(g) for g in groups],\n", "        group_means_sorted,\n", "        color=colors,\n", "        alpha=0.8,\n", "        edgecolor='black'\n", "    )\n", "    for bar, val in zip(bars, group_means_sorted):\n", "        height = bar.get_height()\n", "        axes[1, 0].text(bar.get_x() + bar.get_width()/2., height + (0.02 if height > 0 else -0.05),\n", "                       f'{val:.2f}%', ha='center', va='bottom' if height > 0 else 'top')\n", "    \n", "    axes[1, 0].axhline(y=0, color='black', linestyle='-', alpha=0.5)\n", "    axes[1, 0].set_title(f'{factor_name} 分组收益率')\n", "    axes[1, 0].set_xlabel('分组（1=最低因子值，10=最高因子值）')\n", "    axes[1, 0].set_ylabel('平均收益率（%）')\n", "    axes[1, 0].grid(True, alpha=0.3)\n", "    \n", "\n", "    # 图4：多空累计收益\n", "    backtest_df_sorted = backtest_df.sort_values('date')\n", "    daily_long = backtest_df_sorted[backtest_df_sorted['group'] == n_groups].set_index('date')['return']\n", "    daily_short = backtest_df_sorted[backtest_df_sorted['group'] == 1].set_index('date')['return']\n", "    common_dates = daily_long.index.intersection(daily_short.index)\n", "    daily_long_short = None\n", "    \n", "    if len(common_dates) == 0:\n", "        print('⚠️ 多空组合无重叠日期，无法绘制累计收益曲线')\n", "        axes[1, 1].text(0.5, 0.5, '无多空重叠数据', ha='center', va='center', transform=axes[1, 1].transAxes)\n", "    else:\n", "        daily_long = daily_long.loc[common_dates]\n", "        daily_short = daily_short.loc[common_dates]\n", "        daily_long_short = daily_long - daily_short\n", "        daily_long_short_clip = daily_long_short.clip(-10, 10)\n", "        cumulative_ls = (1 + daily_long_short_clip/100).cumprod()\n", "        axes[1, 1].plot(cumulative_ls.index, cumulative_ls, color='orange', linewidth=2.5)\n", "        axes[1, 1].axhline(y=1, color='black', linestyle='--', alpha=0.6)\n", "        final_cum = cumulative_ls.iloc[-1]\n", "        axes[1, 1].text(cumulative_ls.index[-1], final_cum, \n", "                       f'最终收益: {final_cum:.2f}', \n", "                       ha='right', va='bottom', fontsize=10,\n", "                       bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))\n", "    \n", "    axes[1, 1].set_title(f'{factor_name} 多空组合累计收益')\n", "    axes[1, 1].set_xlabel('时间')\n", "    axes[1, 1].set_ylabel('累计收益（初始值=1）')\n", "    axes[1, 1].grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout(rect=[0, 0, 1, 0.96])\n", "    plt.show()\n", "    \n", "\n", "    # 绩效统计\n", "    print(f'\\n=== {factor_name} 绩效统计 ===')\n", "    # IC指标\n", "    mean_ic = ic_df['ic'].mean()\n", "    std_ic = ic_df['ic'].std()\n", "    ic_ir = mean_ic / std_ic if std_ic > 0 else 0\n", "    ic_win_rate = (ic_df['ic'] > 0).mean()\n", "    \n", "    # G10年化收益计算\n", "    backtest_df_sorted['month'] = backtest_df_sorted['date'].dt.to_period('M')\n", "    g10_returns = backtest_df_sorted[backtest_df_sorted['group'] == n_groups]\n", "    monthly_g10_returns = g10_returns.groupby('month')['return'].mean()\n", "    \n", "    if not monthly_g10_returns.empty:\n", "        total_months = len(monthly_g10_returns)\n", "        if total_months == 0:\n", "            top_group_annual = np.nan\n", "        else:\n", "            monthly_g10_decimal = monthly_g10_returns / 100\n", "            cumulative_return = (monthly_g10_decimal + 1).prod()\n", "            top_group_annual = (cumulative_return ** (12 / total_months) - 1) * 100\n", "    else:\n", "        top_group_annual = np.nan\n", "    \n", "    # 多空年化收益计算\n", "    long_short_annual = np.nan\n", "    if not pd.isna(long_short) and daily_long_short is not None and not daily_long_short.empty:\n", "        daily_ls_df = pd.DataFrame({\n", "            'date': daily_long_short.index,\n", "            'ls_return': daily_long_short.values\n", "        })\n", "        daily_ls_df['month'] = daily_ls_df['date'].dt.to_period('M')\n", "        monthly_ls_returns = daily_ls_df.groupby('month')['ls_return'].mean()\n", "        \n", "        if not monthly_ls_returns.empty:\n", "            total_months_ls = len(monthly_ls_returns)\n", "            if total_months_ls > 0:\n", "                monthly_ls_decimal = monthly_ls_returns / 100\n", "                cumulative_ls_return = (monthly_ls_decimal + 1).prod()\n", "                long_short_annual = (cumulative_ls_return ** (12 / total_months_ls) - 1) * 100\n", "    \n", "    \n", "    def calculate_max_drawdown(return_series):\n", "        try:\n", "            returns_decimal = return_series / 100  # 百分比转小数\n", "            returns_decimal = returns_decimal.clip(lower=-0.9)  # 限制单日最大亏损为90%\n", "            cumulative = (1 + returns_decimal).cumprod()\n", "            drawdown = None\n", "            if cumulative.min() <= 0:\n", "                return -100.0  # 本金全部亏损，最大回撤100%\n", "            else:\n", "                # 计算运行最大值和回撤\n", "                running_max = cumulative.expanding().max()\n", "                drawdown = (cumulative - running_max) / running_max \n", "            \n", "            # 6. 计算最大回撤\n", "            if drawdown is not None and not drawdown.empty:\n", "                max_drawdown = drawdown.min() * 100  # 转回百分比\n", "                return max(max_drawdown, -100.0)  # 确保不低于-100%\n", "            else:\n", "                return -100.0  # 无有效回撤数据时默认100%回撤\n", "            \n", "        except Exception as e:\n", "            print(f\"计算最大回撤时出错: {e}\")\n", "        return np.nan\n", "    \n", "    max_drawdown = calculate_max_drawdown(daily_long_short) if (daily_long_short is not None and not daily_long_short.empty) else np.nan\n", "    \n", "    # 绩效表\n", "    stats_table = pd.DataFrame({\n", "        '指标': ['年化收益（G10）', '多空年化收益', '多空胜率', '多空IR', '最大回撤', \n", "                '平均IC', 'IC-IR', 'IC胜率', '有效月度样本'],\n", "        '数值': [\n", "            f'{top_group_annual:.2f}%' if not pd.isna(top_group_annual) else '无数据',\n", "            f'{long_short_annual:.2f}%' if not pd.isna(long_short_annual) else '无数据',\n", "            f'{ic_win_rate:.1%}',\n", "            f'{ic_ir:.2f}',\n", "            f'{max_drawdown:.1f}%' if not pd.isna(max_drawdown) else '无数据',\n", "            f'{mean_ic:.3f}',\n", "            f'{ic_ir:.2f}',\n", "            f'{ic_win_rate:.1%}',\n", "            f'{len(ic_df)}'\n", "        ]\n", "    })\n", "    \n", "    print(stats_table.to_string(index=False))\n", "    \n", "    # 分年绩效统计\n", "    print(f'\\n=== {factor_name} 分年绩效统计 ===')\n", "    # IC分年\n", "    ic_df['year'] = ic_df['date'].dt.year\n", "    ic_yearly = ic_df.groupby('year')['ic'].agg(['mean', 'std', lambda x: (x>0).mean()])\n", "    ic_yearly.columns = ['平均IC', 'IC标准差', 'IC胜率']\n", "    ic_yearly['IC-IR'] = ic_yearly['平均IC'] / ic_yearly['IC标准差'].replace(0, np.nan)\n", "    \n", "    # 回测分年\n", "    backtest_df['year'] = backtest_df['date'].dt.year\n", "    group_yearly = backtest_df.groupby(['year', 'group'])['return'].mean().unstack()\n", "    \n", "    if 1 in group_yearly.columns and n_groups in group_yearly.columns:\n", "        group_yearly = group_yearly[[1, n_groups]]\n", "        group_yearly.columns = ['G1平均收益', 'G10平均收益']\n", "        group_yearly['多空收益'] = group_yearly['G10平均收益'] - group_yearly['G1平均收益']\n", "        \n", "        # 多空胜率\n", "        long_short_win = backtest_df.groupby('year').apply(\n", "            lambda x: ((x[x['group']==n_groups]['return'].values - x[x['group']==1]['return'].values) > 0).mean()\n", "        ).rename('多空胜率')\n", "        group_yearly = group_yearly.join(long_short_win)\n", "        \n", "        # 多空收益标准差\n", "        long_short_std = backtest_df.groupby('year').apply(\n", "            lambda x: (x[x['group']==n_groups]['return'].values - x[x['group']==1]['return'].values).std()\n", "        ).rename('多空收益标准差')\n", "        group_yearly = group_yearly.join(long_short_std)\n", "        \n", "        # 年化收益\n", "        group_yearly['G10年化收益'] = group_yearly.apply(\n", "            lambda row: ((1 + row['G10平均收益']/100) **12 - 1) * 100 \n", "            if not pd.isna(row['G10平均收益']) else np.nan, axis=1\n", "            )\n", "        group_yearly['多空年化收益'] = ((1 + group_yearly['多空收益']/100) ** 12 - 1) * 100\n", "        \n", "        # 多空IR\n", "        group_yearly['多空IR'] = group_yearly['多空年化收益'] / (group_yearly['多空收益标准差'] * np.sqrt(12))\n", "        \n", "        # 年度最大回撤\n", "        def yearly_max_dd_func(df):\n", "            ls_returns = df[df['group']==n_groups]['return'].values - df[df['group']==1]['return'].values\n", "            if len(ls_returns) == 0:\n", "                return np.nan\n", "            return calculate_max_drawdown(pd.Series(ls_returns)) \n", "        \n", "        yearly_max_dd = backtest_df.groupby('year').apply(yearly_max_dd_func).rename('最大回撤')\n", "        group_yearly = group_yearly.join(yearly_max_dd)\n", "        \n", "        # 合并IC分年数据\n", "        yearly_stats = ic_yearly.join(group_yearly)\n", "        \n", "        # 整理输出格式\n", "        final_table = yearly_stats[[\n", "            'G10年化收益', '多空胜率', '多空IR', '最大回撤', \n", "            '平均IC', 'IC-IR', 'IC胜率'\n", "        ]].round(3)\n", "        final_table.columns = [\n", "            '年化收益(%)', '多空胜率', '多空IR', '最大回撤(%)', \n", "            'IC', 'IC-IR', 'IC胜率'\n", "        ]\n", "        print(final_table.to_string())\n", "    else:\n", "        print('⚠️ 分组数据不完整（缺少G1或G10），无法输出分年绩效')\n", "\n", "\n", "factor_cols = ['EP_PURE', 'EP_ROLL_PURE']\n", "\n", "# 遍历因子执行可视化\n", "for factor_col in factor_cols:\n", "    if factor_col in ic_results and factor_col in backtest_results:\n", "        # 验证必要列\n", "        ic_df_check = ic_results[factor_col]\n", "        backtest_df_check = backtest_results[factor_col]['backtest_df']\n", "        if 'date' not in ic_df_check.columns:\n", "            print(f'⚠️ {factor_col} IC结果缺少\"date\"列，跳过可视化')\n", "            continue\n", "        if 'date' not in backtest_df_check.columns:\n", "            print(f'⚠️ {factor_col} 回测结果缺少\"date\"列，跳过可视化')\n", "            continue\n", "        # 执行可视化\n", "        plot_factor_performance(ic_results, backtest_results, factor_col)\n", "    else:\n", "        print(f'⚠️ {factor_col} 数据不完整，跳过绘图')\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 保存检验结果"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== 保存检验结果 ===\n", "✅ 保存 EP_PURE IC结果至: processed_data/ic_results_EP_PURE.feather\n", "✅ 保存 EP_ROLL_PURE IC结果至: processed_data/ic_results_EP_ROLL_PURE.feather\n", "✅ 保存 EP_PURE 回测结果至: processed_data/backtest_EP_PURE.feather、processed_data/group_stats_EP_PURE.csv\n", "✅ 保存 EP_ROLL_PURE 回测结果至: processed_data/backtest_EP_ROLL_PURE.feather、processed_data/group_stats_EP_ROLL_PURE.csv\n", "✅ 保存 EP_PURE 可视化图表至: processed_data/group_return_EP_PURE.png\n", "✅ 保存 EP_ROLL_PURE 可视化图表至: processed_data/group_return_EP_ROLL_PURE.png\n", "\n", "=== 因子检验全流程完成 ===\n", "📋 完成的核心任务:\n", "1. 加载已中性化的因子数据\n", "2. 计算Spearman IC（核心评价指标，衡量因子选股能力）\n", "3. 十分位数分层回测\n", "4. 多空组合累计收益\n", "5. 绩效统计\n", "6. 结果保存\n"]}], "source": ["# 7. 保存检验结果\n", "print('\\n=== 保存检验结果 ===')\n", "os.makedirs('processed_data', exist_ok=True) \n", "\n", "# 保存IC结果\n", "for factor_col, ic_df in ic_results.items():\n", "    ic_save_path = f'processed_data/ic_results_{factor_col}.feather'\n", "    ic_df.to_feather(ic_save_path)\n", "    print(f'✅ 保存 {factor_col} IC结果至: {ic_save_path}')\n", "\n", "# 保存回测结果\n", "for factor_col, results in backtest_results.items():\n", "    # 保存回测明细\n", "    backtest_save_path = f'processed_data/backtest_{factor_col}.feather'\n", "    results['backtest_df'].to_feather(backtest_save_path)\n", "    # 保存分组统计\n", "    group_stats_save_path = f'processed_data/group_stats_{factor_col}.csv'\n", "    results['group_stats'].to_csv(group_stats_save_path, encoding='utf-8-sig')\n", "    # 保存多空收益单独文件\n", "    long_short_df = pd.DataFrame({\n", "        'date': results['backtest_df']['date'].unique(),\n", "        'long_short_return': [results['long_short_return']] * len(results['backtest_df']['date'].unique())\n", "    }).drop_duplicates()\n", "    long_short_save_path = f'processed_data/long_short_{factor_col}.feather'\n", "    long_short_df.to_feather(long_short_save_path)\n", "    print(f'✅ 保存 {factor_col} 回测结果至: {backtest_save_path}、{group_stats_save_path}')\n", "\n", "# 保存可视化图表\n", "for factor_col in neutralized_factor_cols:\n", "    if factor_col in ic_results and factor_col in backtest_results:\n", "        # 单独保存分组收益图\n", "        group_stats = backtest_results[factor_col]['group_stats']\n", "        group_means = group_stats['平均收益']\n", "        plt.figure(figsize=(10, 6))\n", "        plt.bar(range(1, len(group_means)+1), group_means, color='orange', alpha=0.7, edgecolor='black')\n", "        plt.title(f'{factor_col} 分组收益率（对应研报图24）', fontsize=14)\n", "        plt.xlabel('分组（1=最低因子值，10=最高因子值）', fontsize=12)\n", "        plt.ylabel('平均收益率（%）', fontsize=12)\n", "        plt.xticks(range(1, len(group_means)+1))\n", "        plt.grid(True, alpha=0.3)\n", "        plt.savefig(f'processed_data/group_return_{factor_col}.png', dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "        \n", "        # 单独保存多空累计收益图\n", "        backtest_df_sorted = backtest_results[factor_col]['backtest_df'].sort_values('date')\n", "        daily_long = backtest_df_sorted[backtest_df_sorted['group'] == 10].set_index('date')['return']\n", "        daily_short = backtest_df_sorted[backtest_df_sorted['group'] == 1].set_index('date')['return']\n", "        # 确保两个序列的索引对齐\n", "        common_dates = daily_long.index.intersection(daily_short.index)\n", "        daily_long = daily_long.loc[common_dates]\n", "        daily_short = daily_short.loc[common_dates]\n", "        daily_long_short = daily_long - daily_short\n", "        # 修复累计收益计算：确保收益率在合理范围内\n", "        daily_long_short = daily_long_short.clip(-50, 50)  # 限制单期收益率在-50%到50%之间\n", "        cumulative_long_short = (1 + daily_long_short/100).cumprod()\n", "        \n", "        plt.figure(figsize=(12, 6))\n", "        plt.plot(cumulative_long_short.index, cumulative_long_short, color='orange', linewidth=2)\n", "        plt.title(f'{factor_col} 多空组合累计收益（对应研报图25）', fontsize=14)\n", "        plt.xlabel('时间', fontsize=12)\n", "        plt.ylabel('累计收益（初始值=1）', fontsize=12)\n", "        plt.grid(True, alpha=0.3)\n", "        plt.savefig(f'processed_data/cumulative_return_{factor_col}.png', dpi=300, bbox_inches='tight')\n", "        plt.close()\n", "        print(f'✅ 保存 {factor_col} 可视化图表至: processed_data/group_return_{factor_col}.png')\n", "\n", "print('\\n=== 因子检验全流程完成 ===')\n", "print('📋 完成的核心任务:')\n", "print('1. 加载已中性化的因子数据')\n", "print('2. 计算Spearman IC（核心评价指标，衡量因子选股能力）')\n", "print('3. 十分位数分层回测')\n", "print('4. 多空组合累计收益')\n", "print('5. 绩效统计')\n", "print('6. 结果保存')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}