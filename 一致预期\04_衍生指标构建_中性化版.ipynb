{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 04 - 基础因子构建\n", "\n", "## 构建目标\n", "只构建两个基础因子，严格按研报公式：\n", "\n", "1. **EP_FY**：下一年一致预期净利润/当前市值\n", "2. **EP_ROLL**：滚动12个月一致预期净利润/当前市值\n", "\n", "## 注意\n", "- 不在构建阶段进行中性化\n", "- 中性化处理在05检验文件中进行\n", "- 只构建原始因子值"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境加载完成\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "from sklearn.linear_model import LinearRegression\n", "from sklearn.preprocessing import StandardScaler\n", "import warnings\n", "import os\n", "from datetime import datetime\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "# 确保输出目录存在\n", "os.makedirs('processed_data', exist_ok=True)\n", "\n", "print('环境加载完成')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. 加载数据"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载一致预期数据 ===\n", "✅ 加载完整版一致预期数据\n", "数据形状: (102240, 10)\n", "时间范围: 2012-03-31 00:00:00 到 2025-06-30 00:00:00\n", "股票数量: 4,892\n", "\n", "=== 加载市值数据 ===\n", "市值数据: 9,914,030 条记录\n", "\n", "=== 加载行业数据 ===\n", "行业数据: 7,827 条记录\n"]}], "source": ["print('=== 加载一致预期数据 ===')\n", "\n", "# 尝试加载完整版数据\n", "try:\n", "    df_consensus = pd.read_feather('processed_data/consensus_results_complete.feather')\n", "    print('✅ 加载完整版一致预期数据')\n", "except FileNotFoundError:\n", "    try:\n", "        df_consensus = pd.read_feather('processed_data/consensus_results.feather')\n", "        print('⚠️ 加载原版一致预期数据')\n", "    except FileNotFoundError:\n", "        print('❌ 未找到一致预期数据文件，请先运行03文件')\n", "        raise\n", "\n", "print(f'数据形状: {df_consensus.shape}')\n", "print(f'时间范围: {df_consensus[\"prediction_date\"].min()} 到 {df_consensus[\"prediction_date\"].max()}')\n", "print(f'股票数量: {df_consensus[\"stock_code\"].nunique():,}')\n", "\n", "# 加载市值数据\n", "print('\\n=== 加载市值数据 ===')\n", "df_market = pd.read_hdf('data/ind.h5', 'data')\n", "if df_market.index.names is not None:\n", "    df_market = df_market.reset_index()\n", "\n", "# 市值单位转换（万元->元）\n", "df_market['total_mv'] = df_market['total_mv'] * 1e4\n", "df_market = df_market[df_market['total_mv'].notna() & (df_market['total_mv'] > 0)]\n", "\n", "print(f'市值数据: {len(df_market):,} 条记录')\n", "\n", "# 加载行业数据\n", "print('\\n=== 加载行业数据 ===')\n", "try:\n", "    df_industry = pd.read_feather('processed_data/industry_data_clean.feather')\n", "    print(f'行业数据: {len(df_industry):,} 条记录')\n", "except FileNotFoundError:\n", "    print('⚠️ 行业数据未找到，将影响行业中性化')\n", "    df_industry = None"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 市值匹配和基础因子计算"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 市值匹配和基础因子计算 ===\n", "正在匹配市值数据...\n", "市值匹配成功: 83,239 / 102,240 (81.4%)\n", "EP_FY因子覆盖: 83,239\n", "EP_ROLL因子覆盖: 83,239\n"]}], "source": ["def match_market_value_and_calculate_factors(df_consensus, df_market):\n", "    \"\"\"\n", "    匹配市值并计算基础EP因子\n", "    \"\"\"\n", "    print('=== 市值匹配和基础因子计算 ===')\n", "    \n", "    df = df_consensus.copy()\n", "    df['market_value'] = np.nan\n", "    df['log_market_value'] = np.nan  # 用于中性化的对数市值\n", "    \n", "    # 转换日期格式\n", "    df_market['trade_date'] = pd.to_datetime(df_market['trade_date']).dt.strftime('%Y-%m-%d')\n", "    df['prediction_date'] = pd.to_datetime(df['prediction_date']).dt.strftime('%Y-%m-%d')\n", "    \n", "    # 按股票分组进行市值匹配\n", "    market_grp = df_market.sort_values(['ts_code','trade_date']).groupby('ts_code')\n", "    \n", "    def find_latest_mv(ts_code, pred_date):\n", "        try:\n", "            g = market_grp.get_group(ts_code)\n", "            s = g[g['trade_date'] <= pred_date]\n", "            if len(s) == 0:\n", "                return np.nan\n", "            return s.iloc[-1]['total_mv']\n", "        except KeyError:\n", "            return np.nan\n", "    \n", "    print('正在匹配市值数据...')\n", "    df['market_value'] = [\n", "        find_latest_mv(sc, pd) \n", "        for sc, pd in zip(df['stock_code'], df['prediction_date'])\n", "    ]\n", "    \n", "    # 计算对数市值（用于中性化）\n", "    valid_mv = df['market_value'].notna() & (df['market_value'] > 0)\n", "    df.loc[valid_mv, 'log_market_value'] = np.log(df.loc[valid_mv, 'market_value'])\n", "    \n", "    # 计算基础EP因子\n", "    df.loc[valid_mv, 'EP_FY'] = (\n", "        df.loc[valid_mv, 'consensus_profit_fy'] / \n", "        df.loc[valid_mv, 'market_value']\n", "    )\n", "    \n", "    df.loc[valid_mv, 'EP_ROLL'] = (\n", "        df.loc[valid_mv, 'consensus_profit_roll'] / \n", "        df.loc[valid_mv, 'market_value']\n", "    )\n", "    \n", "    print(f'市值匹配成功: {valid_mv.sum():,} / {len(df):,} ({valid_mv.mean()*100:.1f}%)')\n", "    print(f'EP_FY因子覆盖: {df[\"EP_FY\"].notna().sum():,}')\n", "    print(f'EP_ROLL因子覆盖: {df[\"EP_ROLL\"].notna().sum():,}')\n", "    \n", "    return df\n", "\n", "# 执行市值匹配和基础因子计算\n", "df_factors = match_market_value_and_calculate_factors(df_consensus, df_market)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. 行业信息匹配"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 行业信息匹配 ===\n", "行业信息匹配完成\n", "行业分布:\n", "industry_l1\n", "医药生物    8975\n", "机械设备    7735\n", "电子      7349\n", "基础化工    6749\n", "电力设备    6604\n", "计算机     6265\n", "汽车      4602\n", "有色金属    3551\n", "食品饮料    3389\n", "传媒      3255\n", "Name: count, dtype: int64\n"]}], "source": ["def match_industry_info(df_factors, df_industry):\n", "    \"\"\"\n", "    匹配行业信息（用于行业中性化）\n", "    \"\"\"\n", "    print('=== 行业信息匹配 ===')\n", "    \n", "    if df_industry is None:\n", "        print('⚠️ 行业数据不可用，使用默认行业')\n", "        df_factors['industry_l1'] = '其他'\n", "        return df_factors\n", "    \n", "    # 获取每只股票最新的行业信息\n", "    latest_industry = df_industry.groupby('stock_code').last().reset_index()\n", "    \n", "    # 合并行业信息\n", "    df_result = df_factors.merge(\n", "        latest_industry[['stock_code', 'industry_l1']], \n", "        on='stock_code', \n", "        how='left'\n", "    )\n", "    \n", "    # 填充缺失的行业信息\n", "    df_result['industry_l1'] = df_result['industry_l1'].fillna('其他')\n", "    \n", "    print(f'行业信息匹配完成')\n", "    print(f'行业分布:')\n", "    print(df_result['industry_l1'].value_counts().head(10))\n", "    \n", "    return df_result\n", "\n", "# 匹配行业信息\n", "df_factors = match_industry_info(df_factors, df_industry)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 标准化、中性化处理"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 因子中性化处理 ===\n", "2015-03-31 EP_FY 去极值完成，范围: [-0.05, 0.13]\n", "2015-03-31 EP_ROLL 去极值完成，范围: [-0.05, 0.13]\n", "2015-03-31 EP_FY 标准化完成，均值: 0.0399, 标准差: 0.0238\n", "2015-03-31 EP_ROLL 标准化完成，均值: 0.0409, 标准差: 0.0243\n", "2015-06-30 EP_FY 去极值完成，范围: [-0.09, 0.16]\n", "2015-06-30 EP_ROLL 去极值完成，范围: [-0.09, 0.17]\n", "2015-06-30 EP_FY 标准化完成，均值: 0.0317, 标准差: 0.0230\n", "2015-06-30 EP_ROLL 标准化完成，均值: 0.0352, 标准差: 0.0246\n", "2015-09-30 EP_FY 去极值完成，范围: [-0.06, 0.15]\n", "2015-09-30 EP_ROLL 去极值完成，范围: [-0.07, 0.17]\n", "2015-09-30 EP_FY 标准化完成，均值: 0.0439, 标准差: 0.0295\n", "2015-09-30 EP_ROLL 标准化完成，均值: 0.0513, 标准差: 0.0329\n", "2015-12-31 EP_FY 去极值完成，范围: [-0.04, 0.11]\n", "2015-12-31 EP_ROLL 去极值完成，范围: [-0.05, 0.12]\n", "2015-12-31 EP_FY 标准化完成，均值: 0.0296, 标准差: 0.0212\n", "2015-12-31 EP_ROLL 标准化完成，均值: 0.0368, 标准差: 0.0245\n", "2016-03-31 EP_FY 去极值完成，范围: [-0.06, 0.14]\n", "2016-03-31 EP_ROLL 去极值完成，范围: [-0.06, 0.15]\n", "2016-03-31 EP_FY 标准化完成，均值: 0.0436, 标准差: 0.0290\n", "2016-03-31 EP_ROLL 标准化完成，均值: 0.0444, 标准差: 0.0295\n", "2016-06-30 EP_FY 去极值完成，范围: [-0.05, 0.13]\n", "2016-06-30 EP_ROLL 去极值完成，范围: [-0.06, 0.14]\n", "2016-06-30 EP_FY 标准化完成，均值: 0.0391, 标准差: 0.0261\n", "2016-06-30 EP_ROLL 标准化完成，均值: 0.0431, 标准差: 0.0280\n", "2016-09-30 EP_FY 去极值完成，范围: [-0.06, 0.14]\n", "2016-09-30 EP_ROLL 去极值完成，范围: [-0.07, 0.16]\n", "2016-09-30 EP_FY 标准化完成，均值: 0.0378, 标准差: 0.0249\n", "2016-09-30 EP_ROLL 标准化完成，均值: 0.0440, 标准差: 0.0277\n", "2016-12-31 EP_FY 去极值完成，范围: [-0.09, 0.16]\n", "2016-12-31 EP_ROLL 去极值完成，范围: [-0.09, 0.18]\n", "2016-12-31 EP_FY 标准化完成，均值: 0.0376, 标准差: 0.0249\n", "2016-12-31 EP_ROLL 标准化完成，均值: 0.0462, 标准差: 0.0286\n", "2017-03-31 EP_FY 去极值完成，范围: [-0.03, 0.13]\n", "2017-03-31 EP_ROLL 去极值完成，范围: [-0.04, 0.13]\n", "2017-03-31 EP_FY 标准化完成，均值: 0.0457, 标准差: 0.0247\n", "2017-03-31 EP_ROLL 标准化完成，均值: 0.0467, 标准差: 0.0251\n", "2017-06-30 EP_FY 去极值完成，范围: [-0.04, 0.14]\n", "2017-06-30 EP_ROLL 去极值完成，范围: [-0.04, 0.15]\n", "2017-06-30 EP_FY 标准化完成，均值: 0.0464, 标准差: 0.0247\n", "2017-06-30 EP_ROLL 标准化完成，均值: 0.0511, 标准差: 0.0265\n", "2017-09-30 EP_FY 去极值完成，范围: [-0.03, 0.12]\n", "2017-09-30 EP_ROLL 去极值完成，范围: [-0.03, 0.14]\n", "2017-09-30 EP_FY 标准化完成，均值: 0.0448, 标准差: 0.0234\n", "2017-09-30 EP_ROLL 标准化完成，均值: 0.0518, 标准差: 0.0260\n", "2017-12-31 EP_FY 去极值完成，范围: [-0.03, 0.13]\n", "2017-12-31 EP_ROLL 去极值完成，范围: [-0.04, 0.16]\n", "2017-12-31 EP_FY 标准化完成，均值: 0.0487, 标准差: 0.0253\n", "2017-12-31 EP_ROLL 标准化完成，均值: 0.0599, 标准差: 0.0295\n", "2018-03-31 EP_FY 去极值完成，范围: [-0.05, 0.17]\n", "2018-03-31 EP_ROLL 去极值完成，范围: [-0.04, 0.17]\n", "2018-03-31 EP_FY 标准化完成，均值: 0.0632, 标准差: 0.0333\n", "2018-03-31 EP_ROLL 标准化完成，均值: 0.0641, 标准差: 0.0336\n", "2018-06-30 EP_FY 去极值完成，范围: [-0.07, 0.21]\n", "2018-06-30 EP_ROLL 去极值完成，范围: [-0.06, 0.21]\n", "2018-06-30 EP_FY 标准化完成，均值: 0.0695, 标准差: 0.0388\n", "2018-06-30 EP_ROLL 标准化完成，均值: 0.0757, 标准差: 0.0408\n", "2018-09-30 EP_FY 去极值完成，范围: [-0.05, 0.20]\n", "2018-09-30 EP_ROLL 去极值完成，范围: [-0.05, 0.22]\n", "2018-09-30 EP_FY 标准化完成，均值: 0.0734, 标准差: 0.0395\n", "2018-09-30 EP_ROLL 标准化完成，均值: 0.0843, 标准差: 0.0435\n", "2018-12-31 EP_FY 去极值完成，范围: [-0.06, 0.22]\n", "2018-12-31 EP_ROLL 去极值完成，范围: [-0.06, 0.25]\n", "2018-12-31 EP_FY 标准化完成，均值: 0.0781, 标准差: 0.0432\n", "2018-12-31 EP_ROLL 标准化完成，均值: 0.0934, 标准差: 0.0489\n", "2019-03-31 EP_FY 去极值完成，范围: [-0.05, 0.18]\n", "2019-03-31 EP_ROLL 去极值完成，范围: [-0.04, 0.18]\n", "2019-03-31 EP_FY 标准化完成，均值: 0.0679, 标准差: 0.0361\n", "2019-03-31 EP_ROLL 标准化完成，均值: 0.0688, 标准差: 0.0363\n", "2019-06-30 EP_FY 去极值完成，范围: [-0.05, 0.19]\n", "2019-06-30 EP_ROLL 去极值完成，范围: [-0.05, 0.21]\n", "2019-06-30 EP_FY 标准化完成，均值: 0.0712, 标准差: 0.0385\n", "2019-06-30 EP_ROLL 标准化完成，均值: 0.0773, 标准差: 0.0408\n", "2019-09-30 EP_FY 去极值完成，范围: [-0.06, 0.20]\n", "2019-09-30 EP_ROLL 去极值完成，范围: [-0.06, 0.22]\n", "2019-09-30 EP_FY 标准化完成，均值: 0.0680, 标准差: 0.0405\n", "2019-09-30 EP_ROLL 标准化完成，均值: 0.0769, 标准差: 0.0439\n", "2019-12-31 EP_FY 去极值完成，范围: [-0.06, 0.19]\n", "2019-12-31 EP_ROLL 去极值完成，范围: [-0.13, 0.28]\n", "2019-12-31 EP_FY 标准化完成，均值: 0.0627, 标准差: 0.0385\n", "2019-12-31 EP_ROLL 标准化完成，均值: 0.0745, 标准差: 0.0456\n", "2020-03-31 EP_FY 去极值完成，范围: [-0.07, 0.22]\n", "2020-03-31 EP_ROLL 去极值完成，范围: [-0.07, 0.22]\n", "2020-03-31 EP_FY 标准化完成，均值: 0.0752, 标准差: 0.0457\n", "2020-03-31 EP_ROLL 标准化完成，均值: 0.0757, 标准差: 0.0458\n", "2020-06-30 EP_FY 去极值完成，范围: [-0.08, 0.20]\n", "2020-06-30 EP_ROLL 去极值完成，范围: [-0.08, 0.22]\n", "2020-06-30 EP_FY 标准化完成，均值: 0.0616, 标准差: 0.0428\n", "2020-06-30 EP_ROLL 标准化完成，均值: 0.0669, 标准差: 0.0451\n", "2020-09-30 EP_FY 去极值完成，范围: [-0.07, 0.18]\n", "2020-09-30 EP_ROLL 去极值完成，范围: [-0.07, 0.20]\n", "2020-09-30 EP_FY 标准化完成，均值: 0.0567, 标准差: 0.0387\n", "2020-09-30 EP_ROLL 标准化完成，均值: 0.0651, 标准差: 0.0419\n", "2020-12-31 EP_FY 去极值完成，范围: [-0.07, 0.19]\n", "2020-12-31 EP_ROLL 去极值完成，范围: [-0.07, 0.21]\n", "2020-12-31 EP_FY 标准化完成，均值: 0.0560, 标准差: 0.0388\n", "2020-12-31 EP_ROLL 标准化完成，均值: 0.0678, 标准差: 0.0432\n", "2021-03-31 EP_FY 去极值完成，范围: [-0.07, 0.21]\n", "2021-03-31 EP_ROLL 去极值完成，范围: [-0.07, 0.21]\n", "2021-03-31 EP_FY 标准化完成，均值: 0.0664, 标准差: 0.0410\n", "2021-03-31 EP_ROLL 标准化完成，均值: 0.0672, 标准差: 0.0412\n", "2021-06-30 EP_FY 去极值完成，范围: [-0.09, 0.22]\n", "2021-06-30 EP_ROLL 去极值完成，范围: [-0.09, 0.23]\n", "2021-06-30 EP_FY 标准化完成，均值: 0.0650, 标准差: 0.0450\n", "2021-06-30 EP_ROLL 标准化完成，均值: 0.0711, 标准差: 0.0473\n", "2021-09-30 EP_FY 去极值完成，范围: [-0.07, 0.20]\n", "2021-09-30 EP_ROLL 去极值完成，范围: [-0.07, 0.22]\n", "2021-09-30 EP_FY 标准化完成，均值: 0.0622, 标准差: 0.0409\n", "2021-09-30 EP_ROLL 标准化完成，均值: 0.0725, 标准差: 0.0442\n", "2021-12-31 EP_FY 去极值完成，范围: [-0.09, 0.20]\n", "2021-12-31 EP_ROLL 去极值完成，范围: [-0.09, 0.23]\n", "2021-12-31 EP_FY 标准化完成，均值: 0.0560, 标准差: 0.0425\n", "2021-12-31 EP_ROLL 标准化完成，均值: 0.0690, 标准差: 0.0468\n", "2022-03-31 EP_FY 去极值完成，范围: [-0.06, 0.21]\n", "2022-03-31 EP_ROLL 去极值完成，范围: [-0.06, 0.21]\n", "2022-03-31 EP_FY 标准化完成，均值: 0.0755, 标准差: 0.0414\n", "2022-03-31 EP_ROLL 标准化完成，均值: 0.0765, 标准差: 0.0418\n", "2022-06-30 EP_FY 去极值完成，范围: [-0.07, 0.21]\n", "2022-06-30 EP_ROLL 去极值完成，范围: [-0.07, 0.22]\n", "2022-06-30 EP_FY 标准化完成，均值: 0.0697, 标准差: 0.0430\n", "2022-06-30 EP_ROLL 标准化完成，均值: 0.0769, 标准差: 0.0451\n", "2022-09-30 EP_FY 去极值完成，范围: [-0.07, 0.22]\n", "2022-09-30 EP_ROLL 去极值完成，范围: [-0.07, 0.24]\n", "2022-09-30 EP_FY 标准化完成，均值: 0.0734, 标准差: 0.0443\n", "2022-09-30 EP_ROLL 标准化完成，均值: 0.0863, 标准差: 0.0479\n", "2022-12-31 EP_FY 去极值完成，范围: [-0.07, 0.20]\n", "2022-12-31 EP_ROLL 去极值完成，范围: [-0.07, 0.23]\n", "2022-12-31 EP_FY 标准化完成，均值: 0.0640, 标准差: 0.0417\n", "2022-12-31 EP_ROLL 标准化完成，均值: 0.0804, 标准差: 0.0467\n", "2023-03-31 EP_FY 去极值完成，范围: [-0.07, 0.21]\n", "2023-03-31 EP_ROLL 去极值完成，范围: [-0.07, 0.21]\n", "2023-03-31 EP_FY 标准化完成，均值: 0.0700, 标准差: 0.0414\n", "2023-03-31 EP_ROLL 标准化完成，均值: 0.0708, 标准差: 0.0417\n", "2023-06-30 EP_FY 去极值完成，范围: [-0.07, 0.21]\n", "2023-06-30 EP_ROLL 去极值完成，范围: [-0.07, 0.22]\n", "2023-06-30 EP_FY 标准化完成，均值: 0.0666, 标准差: 0.0408\n", "2023-06-30 EP_ROLL 标准化完成，均值: 0.0740, 标准差: 0.0434\n", "2023-09-30 EP_FY 去极值完成，范围: [-0.06, 0.19]\n", "2023-09-30 EP_ROLL 去极值完成，范围: [-0.06, 0.21]\n", "2023-09-30 EP_FY 标准化完成，均值: 0.0643, 标准差: 0.0377\n", "2023-09-30 EP_ROLL 标准化完成，均值: 0.0760, 标准差: 0.0417\n", "2023-12-31 EP_FY 去极值完成，范围: [-0.07, 0.19]\n", "2023-12-31 EP_ROLL 去极值完成，范围: [-0.07, 0.22]\n", "2023-12-31 EP_FY 标准化完成，均值: 0.0611, 标准差: 0.0383\n", "2023-12-31 EP_ROLL 标准化完成，均值: 0.0765, 标准差: 0.0439\n", "2024-03-31 EP_FY 去极值完成，范围: [-0.05, 0.20]\n", "2024-03-31 EP_ROLL 去极值完成，范围: [-0.05, 0.20]\n", "2024-03-31 EP_FY 标准化完成，均值: 0.0750, 标准差: 0.0389\n", "2024-03-31 EP_ROLL 标准化完成，均值: 0.0755, 标准差: 0.0390\n", "2024-06-30 EP_FY 去极值完成，范围: [-0.05, 0.20]\n", "2024-06-30 EP_ROLL 去极值完成，范围: [-0.05, 0.21]\n", "2024-06-30 EP_FY 标准化完成，均值: 0.0735, 标准差: 0.0382\n", "2024-06-30 EP_ROLL 标准化完成，均值: 0.0810, 标准差: 0.0404\n", "2024-09-30 EP_FY 去极值完成，范围: [-0.04, 0.16]\n", "2024-09-30 EP_ROLL 去极值完成，范围: [-0.04, 0.18]\n", "2024-09-30 EP_FY 标准化完成，均值: 0.0590, 标准差: 0.0313\n", "2024-09-30 EP_ROLL 标准化完成，均值: 0.0682, 标准差: 0.0341\n", "2024-12-31 EP_FY 去极值完成，范围: [-0.05, 0.16]\n", "2024-12-31 EP_ROLL 去极值完成，范围: [-0.05, 0.18]\n", "2024-12-31 EP_FY 标准化完成，均值: 0.0558, 标准差: 0.0325\n", "2024-12-31 EP_ROLL 标准化完成，均值: 0.0678, 标准差: 0.0359\n", "2025-03-31 EP_FY 去极值完成，范围: [-0.05, 0.18]\n", "2025-03-31 EP_ROLL 去极值完成，范围: [-0.05, 0.18]\n", "2025-03-31 EP_FY 标准化完成，均值: 0.0621, 标准差: 0.0354\n", "2025-03-31 EP_ROLL 标准化完成，均值: 0.0626, 标准差: 0.0357\n", "2025-06-30 EP_FY 去极值完成，范围: [-0.05, 0.16]\n", "2025-06-30 EP_ROLL 去极值完成，范围: [-0.05, 0.17]\n", "2025-06-30 EP_FY 标准化完成，均值: 0.0561, 标准差: 0.0334\n", "2025-06-30 EP_ROLL 标准化完成，均值: 0.0614, 标准差: 0.0351\n", "\n", "中性化处理完成，总有效记录: 83,239\n", "EP_FY_PURE 覆盖度: 81.4%\n", "EP_ROLL_PURE 覆盖度: 81.4%\n"]}], "source": ["def neutralize_factors(df, factor_cols=['EP_FY', 'EP_ROLL']):\n", "    \"\"\"\n", "    对因子进行\"去极值→标准化→行业+市值中性化\"处理（与研报逻辑一致）\n", "    \n", "    步骤：\n", "    1. 对原始因子去极值（3σ法）\n", "    2. 对去极值后的因子标准化（z-score）\n", "    3. 以行业哑变量+对数市值为自变量做回归，取残差作为中性化因子\n", "    \"\"\"\n", "    print('=== 因子中性化处理 ===')\n", "    df_result = df.copy()\n", "    \n", "    # 按预测日期分组处理（截面中性化）\n", "    neutralized_results = []\n", "    \n", "    for pred_date, group in df.groupby('prediction_date'):\n", "        if len(group) < 50:  # 样本量不足跳过\n", "            continue\n", "            \n", "        # 筛选有效数据（剔除因子和市值为空的样本）\n", "        group_clean = group.dropna(subset=['log_market_value'] + factor_cols).copy()\n", "        if len(group_clean) < 30:\n", "            continue\n", "        \n", "        # --------------------------\n", "        # 1. 因子去极值（3σ法，研报常用）\n", "        # --------------------------\n", "        for factor_col in factor_cols:\n", "            # 计算均值和标准差\n", "            mean = group_clean[factor_col].mean()\n", "            std = group_clean[factor_col].std()\n", "            # 确定上下限（3倍标准差）\n", "            upper = mean + 3 * std\n", "            lower = mean - 3 * std\n", "            # 截断极值\n", "            group_clean[f'{factor_col}_winsorized'] = group_clean[factor_col].clip(lower, upper)\n", "            print(f\"{pred_date} {factor_col} 去极值完成，范围: [{lower:.2f}, {upper:.2f}]\")\n", "        \n", "        # --------------------------\n", "        # 2. 因子标准化（z-score）\n", "        # --------------------------\n", "        for factor_col in factor_cols:\n", "            winsor_col = f'{factor_col}_winsorized'\n", "            # 计算标准化值（均值0，标准差1）\n", "            mean = group_clean[winsor_col].mean()\n", "            std = group_clean[winsor_col].std()\n", "            group_clean[f'{factor_col}_standardized'] = (group_clean[winsor_col] - mean) / std\n", "            print(f\"{pred_date} {factor_col} 标准化完成，均值: {mean:.4f}, 标准差: {std:.4f}\")\n", "        \n", "        # --------------------------\n", "        # 3. 行业+市值中性化（回归残差法）\n", "        # --------------------------\n", "        # 创建行业哑变量（中信一级行业，避免共线性）\n", "        industry_dummies = pd.get_dummies(group_clean['industry_l1'], prefix='industry')\n", "        \n", "        # 准备自变量（市值+行业哑变量）\n", "        X = group_clean[['log_market_value']].copy()  # 对数市值（已预处理）\n", "        if len(industry_dummies.columns) > 1:\n", "            # 去掉一个哑变量避免多重共线性\n", "            X = pd.concat([X, industry_dummies.iloc[:, :-1]], axis=1)\n", "        \n", "        # 自变量标准化（提高回归稳定性）\n", "        from sklearn.preprocessing import StandardScaler\n", "        scaler = StandardScaler()\n", "        X_scaled = scaler.fit_transform(X)\n", "        \n", "        # 对每个因子进行中性化\n", "        from sklearn.linear_model import LinearRegression\n", "        for factor_col in factor_cols:\n", "            # 使用标准化后的因子作为因变量\n", "            y = group_clean[f'{factor_col}_standardized'].values\n", "            \n", "            # 线性回归：因子 ~ 市值 + 行业\n", "            reg = LinearRegression()\n", "            reg.fit(X_scaled, y)\n", "            \n", "            # 残差 = 实际值 - 预测值（剔除行业和市值影响后的因子）\n", "            y_pred = reg.predict(X_scaled)\n", "            residuals = y - y_pred\n", "            \n", "            # 保存中性化结果\n", "            group_clean[f'{factor_col}_PURE'] = residuals\n", "            \n", "            # 输出第一个日期的R²（衡量中性化效果，R²越高说明行业/市值解释力越强）\n", "            if pred_date == df['prediction_date'].iloc[0]:\n", "                r2 = reg.score(X_scaled, y)\n", "                print(f\"{factor_col} 中性化 R²: {r2:.3f} (行业+市值解释力)\")\n", "        \n", "        neutralized_results.append(group_clean)\n", "    \n", "    # 合并结果到原始数据\n", "    if neutralized_results:\n", "        df_neutralized = pd.concat(neutralized_results, ignore_index=True)\n", "        for factor_col in factor_cols:\n", "            pure_col = f'{factor_col}_PURE'\n", "            if pure_col in df_neutralized.columns:\n", "                df_result = df_result.merge(\n", "                    df_neutralized[['stock_code', 'prediction_date', pure_col]],\n", "                    on=['stock_code', 'prediction_date'],\n", "                    how='left'\n", "                )\n", "        \n", "        print(f\"\\n中性化处理完成，总有效记录: {len(df_neutralized):,}\")\n", "        for factor_col in factor_cols:\n", "            pure_col = f'{factor_col}_PURE'\n", "            coverage = df_result[pure_col].notna().sum()\n", "            print(f\"{pure_col} 覆盖度: {coverage/len(df_result):.1%}\")\n", "    else:\n", "        print(\"⚠️ 无有效数据进行中性化处理\")\n", "        for factor_col in factor_cols:\n", "            df_result[f'{factor_col}_PURE'] = np.nan\n", "    \n", "    return df_result\n", "\n", "df_factors = neutralize_factors(df_factors, factor_cols=['EP_FY', 'EP_ROLL'])\n"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PER因子构建已跳过\n"]}], "source": ["# PER因子已删除，按照用户要求不再构建此因子\n", "print('PER因子构建已跳过')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 因子统计和保存"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 因子统计汇总 ===\n", "\n", "可用因子列表: ['EP_FY', 'EP_ROLL', 'EP_FY_PURE', 'EP_ROLL_PURE']\n", "数据总行数: 102,240\n", "\n", "EP_FY:\n", "  - 有效数据量: 83,239\n", "  - 数据覆盖率: 81.4%\n", "  - 均值: 0.0599\n", "  - 中位数: 0.0503\n", "  - 标准差: 0.0425\n", "\n", "EP_ROLL:\n", "  - 有效数据量: 83,239\n", "  - 数据覆盖率: 81.4%\n", "  - 均值: 0.0671\n", "  - 中位数: 0.0573\n", "  - 标准差: 0.0458\n", "\n", "EP_FY_PURE:\n", "  - 有效数据量: 83,239\n", "  - 数据覆盖率: 81.4%\n", "  - 均值: 0.0000\n", "  - 中位数: -0.0730\n", "  - 标准差: 0.7829\n", "\n", "EP_ROLL_PURE:\n", "  - 有效数据量: 83,239\n", "  - 数据覆盖率: 81.4%\n", "  - 均值: 0.0000\n", "  - 中位数: -0.0720\n", "  - 标准差: 0.7915\n", "\n", "=== 结果数据处理与保存 ===\n", "输出数据初始列名: ['stock_code', 'prediction_date', 'consensus_profit_fy', 'consensus_profit_roll', 'market_value', 'log_market_value', 'industry_l1', 'EP_FY', 'EP_ROLL', 'EP_FY_PURE', 'EP_ROLL_PURE']\n", "✅ 未发现重复列名，无需处理\n", "✅ 重命名后最终列名: ['stock_code', 'date', 'consensus_profit_fy', 'consensus_profit_roll', 'market_value', 'log_market_value', 'industry_l1', 'EP_FY', 'EP_ROLL', 'EP_PURE', 'EP_ROLL_PURE']\n", "\n", "=== 保存结果验证 ===\n", "✅ 中性化因子数据已保存至:\n", "  - Feather格式: processed_data\\derived_factors_neutralized.feather\n", "  - CSV格式: processed_data\\derived_factors_neutralized.csv\n", "\n", "数据基本信息:\n", "  - 数据形状: (102240, 11) (行数: 102,240, 列数: 11)\n", "  - 时间范围: 2012-03-31 至 2025-06-30\n", "  - 涉及股票数量: 4,892\n", "  - 非空数据占比: 89.9%\n"]}], "source": ["print('=== 因子统计汇总 ===')\n", "\n", "# 定义需要统计的因子列（与原始数据列名对应）\n", "factor_cols = ['EP_FY', 'EP_ROLL', 'EP_FY_PURE', 'EP_ROLL_PURE']\n", "# 筛选出数据中实际存在的因子列（避免KeyError）\n", "available_factors = [col for col in factor_cols if col in df_factors.columns]\n", "\n", "print(f'\\n可用因子列表: {available_factors}')\n", "print(f'数据总行数: {len(df_factors):,}')\n", "\n", "# 逐个因子计算统计信息（有效数量、覆盖率、均值、标准差）\n", "for factor in available_factors:\n", "    # 有效数据量（非空值数量）\n", "    valid_count = df_factors[factor].notna().sum()\n", "    # 数据覆盖率（有效数据占比）\n", "    coverage = valid_count / len(df_factors) * 100\n", "    \n", "    if valid_count > 0:\n", "        # 数值型统计（均值、标准差，保留4位小数）\n", "        mean_val = df_factors[factor].mean()\n", "        std_val = df_factors[factor].std()\n", "        # 补充中位数（可选，增强统计信息）\n", "        median_val = df_factors[factor].median()\n", "        print(f'\\n{factor}:')\n", "        print(f'  - 有效数据量: {valid_count:,}')\n", "        print(f'  - 数据覆盖率: {coverage:.1f}%')\n", "        print(f'  - 均值: {mean_val:.4f}')\n", "        print(f'  - 中位数: {median_val:.4f}')\n", "        print(f'  - 标准差: {std_val:.4f}')\n", "    else:\n", "        print(f'\\n{factor}: 无有效数据（全部为空值）')\n", "\n", "# ====================== 结果数据处理与保存 ======================\n", "print('\\n=== 结果数据处理与保存 ===')\n", "\n", "# 1. 选择最终输出的列（基础信息列 + 可用因子列）\n", "output_cols = [\n", "    'stock_code', 'prediction_date',  # 股票标识与日期\n", "    'consensus_profit_fy', 'consensus_profit_roll',  # 一致预期利润\n", "    'market_value', 'log_market_value', 'industry_l1'  # 市值与行业信息\n", "] + available_factors  # 拼接可用因子列\n", "\n", "# 筛选列并创建副本（避免修改原始数据）\n", "df_final = df_factors[output_cols].copy()\n", "print(f'输出数据初始列名: {list(df_final.columns)}')\n", "\n", "# 2. 检查并处理重复列名（Feather格式不支持重复列，关键修复）\n", "duplicate_cols = df_final.columns[df_final.columns.duplicated()].tolist()\n", "if duplicate_cols:\n", "    print(f'⚠️  发现重复列名: {duplicate_cols}')\n", "    # 保留重复列中的第一列，删除后续重复列（可根据需求调整为keep=\"last\"）\n", "    df_final = df_final.loc[:, ~df_final.columns.duplicated(keep='first')]\n", "    print(f'✅ 删除重复列后，最终列名: {list(df_final.columns)}')\n", "else:\n", "    print('✅ 未发现重复列名，无需处理')\n", "\n", "# 3. 重命名列以符合研报命名规范（避免重复，优化可读性）\n", "rename_mapping = {\n", "    'prediction_date': 'date',  # 日期列简化命名\n", "    'EP_FY_PURE': 'EP_PURE'     # 研报中的提纯因子命名\n", "}\n", "\n", "# 仅当\"EP_PER_NEUTRAL\"存在且\"EP_PER\"不存在时，才将其重命名为\"EP_PER\"（避免重复）\n", "if 'EP_PER_NEUTRAL' in df_final.columns and 'EP_PER' not in df_final.columns:\n", "    rename_mapping['EP_PER_NEUTRAL'] = 'EP_PER'  # 最终中性化因子命名\n", "elif 'EP_PER_NEUTRAL' in df_final.columns and 'EP_PER' in df_final.columns:\n", "    # 若\"EP_PER\"已存在，给中性化因子加后缀区分（避免冲突）\n", "    rename_mapping['EP_PER_NEUTRAL'] = 'EP_PER_FINAL'\n", "    print(f'⚠️  \"EP_PER\"列已存在，将\"EP_PER_NEUTRAL\"重命名为\"EP_PER_FINAL\"')\n", "\n", "# 执行重命名\n", "df_final = df_final.rename(columns=rename_mapping)\n", "print(f'✅ 重命名后最终列名: {list(df_final.columns)}')\n", "\n", "# 4. 确保保存路径存在（避免\"文件夹不存在\"报错）\n", "save_dir = 'processed_data'\n", "os.makedirs(save_dir, exist_ok=True)  # 文件夹不存在则创建，存在则跳过\n", "\n", "# 5. 保存数据（Feather格式+CSV格式，满足不同使用场景）\n", "# Feather格式（高效压缩，适合后续Python处理）\n", "feather_path = os.path.join(save_dir, 'derived_factors_neutralized.feather')\n", "df_final.to_feather(feather_path)\n", "\n", "# CSV格式（通用格式，适合Excel查看或其他工具处理）\n", "csv_path = os.path.join(save_dir, 'derived_factors_neutralized.csv')\n", "df_final.to_csv(csv_path, index=False, encoding='utf-8-sig')  # utf-8-sig支持中文\n", "\n", "# ====================== 保存结果验证 ======================\n", "print(f'\\n=== 保存结果验证 ===')\n", "print(f'✅ 中性化因子数据已保存至:')\n", "print(f'  - Feather格式: {feather_path}')\n", "print(f'  - CSV格式: {csv_path}')\n", "print(f'\\n数据基本信息:')\n", "print(f'  - 数据形状: {df_final.shape} (行数: {df_final.shape[0]:,}, 列数: {df_final.shape[1]})')\n", "print(f'  - 时间范围: {df_final[\"date\"].min()} 至 {df_final[\"date\"].max()}')\n", "print(f'  - 涉及股票数量: {df_final[\"stock_code\"].nunique():,}')\n", "print(f'  - 非空数据占比: {df_final.notna().sum().sum() / (df_final.shape[0] * df_final.shape[1]) * 100:.1f}%')"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}