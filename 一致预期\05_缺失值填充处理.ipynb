{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 05 - 缺失值填充处理\n", "\n", "**使用行业中位数填充方法**"]}, {"cell_type": "code", "execution_count": 1, "id": "abe48bdc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["库导入完成\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import warnings\n", "warnings.filterwarnings('ignore')\n", "\n", "print(\"库导入完成\")"]}, {"cell_type": "markdown", "id": "e627e157", "metadata": {}, "source": ["## 1. 加载数据"]}, {"cell_type": "code", "execution_count": 2, "id": "7aa6b672", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载衍生指标数据 ===\n", "从feather文件加载成功\n", "因子数据形状: (8655, 15)\n", "涉及股票数量: 2315\n", "数据列: ['stock_code', 'forecast_year', 'prediction_date', 'industry_l1', 'consensus_profit_fy', 'consensus_profit_fy2', 'consensus_profit_roll', 'EP_FY', 'EP_ROLL', 'Growth_FY', 'PEG_proxy', 'DEP', 'EP_PER', 'market_value', 'analyst_count']\n"]}], "source": ["print(\"=== 加载衍生指标数据 ===\")\n", "\n", "try:\n", "    # 优先尝试读取feather格式\n", "    try:\n", "        df_factors = pd.read_feather('processed_data/final_factors.feather')\n", "        print(\"从feather文件加载成功\")\n", "    except FileNotFoundError:\n", "        # 如果feather不存在，尝试读取CSV并转换\n", "        print(\"feather文件不存在，尝试从CSV加载...\")\n", "        df_factors = pd.read_csv('processed_data/final_factors.csv', encoding='utf-8-sig')\n", "        # 立即保存为feather格式供后续使用\n", "        df_factors.to_feather('processed_data/final_factors.feather')\n", "        print(\"已从CSV转换并保存为feather格式\")\n", "    \n", "    print(f\"因子数据形状: {df_factors.shape}\")\n", "    print(f\"涉及股票数量: {df_factors['stock_code'].nunique()}\")\n", "    print(f\"数据列: {list(df_factors.columns)}\")\n", "    \n", "except Exception as e:\n", "    print(f\"加载数据失败: {e}\")\n", "    print(\"请先运行前面的notebook\")\n", "    raise"]}, {"cell_type": "markdown", "id": "df6ff09e", "metadata": {}, "source": ["## 2. 缺失值分析"]}, {"cell_type": "code", "execution_count": 3, "id": "e36955e0", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 缺失值分析 ===\n", "                      指标  总样本数  缺失数量  缺失比例(%)\n", "0    consensus_profit_fy  8655     0     0.00\n", "1  consensus_profit_roll  8655     0     0.00\n", "2                  EP_FY  8655    46     0.53\n", "3                EP_ROLL  8655    46     0.53\n", "\n", "行业信息覆盖率: 0/8655 (0.0%)\n", "\n", "数据预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stock_code</th>\n", "      <th>forecast_year</th>\n", "      <th>prediction_date</th>\n", "      <th>industry_l1</th>\n", "      <th>consensus_profit_fy</th>\n", "      <th>consensus_profit_fy2</th>\n", "      <th>consensus_profit_roll</th>\n", "      <th>EP_FY</th>\n", "      <th>EP_ROLL</th>\n", "      <th>Growth_FY</th>\n", "      <th>PEG_proxy</th>\n", "      <th>DEP</th>\n", "      <th>EP_PER</th>\n", "      <th>market_value</th>\n", "      <th>analyst_count</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001.SZ</td>\n", "      <td>2020</td>\n", "      <td>2019-06-30</td>\n", "      <td>NaN</td>\n", "      <td>3.224965e+10</td>\n", "      <td>3.765754e+10</td>\n", "      <td>3.495359e+10</td>\n", "      <td>0.136300</td>\n", "      <td>0.147728</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>2.366083e+11</td>\n", "      <td>24</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000002.SZ</td>\n", "      <td>2020</td>\n", "      <td>2019-06-30</td>\n", "      <td>NaN</td>\n", "      <td>4.878083e+10</td>\n", "      <td>5.651548e+10</td>\n", "      <td>5.264816e+10</td>\n", "      <td>0.155198</td>\n", "      <td>0.167503</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.143126e+11</td>\n", "      <td>32</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000008.SZ</td>\n", "      <td>2020</td>\n", "      <td>2019-06-30</td>\n", "      <td>NaN</td>\n", "      <td>5.910000e+08</td>\n", "      <td>7.510000e+08</td>\n", "      <td>6.710000e+08</td>\n", "      <td>0.056374</td>\n", "      <td>0.064005</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.048360e+10</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000012.SZ</td>\n", "      <td>2020</td>\n", "      <td>2019-06-30</td>\n", "      <td>NaN</td>\n", "      <td>7.100870e+08</td>\n", "      <td>9.900500e+08</td>\n", "      <td>8.500685e+08</td>\n", "      <td>0.054917</td>\n", "      <td>0.065743</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>1.293010e+10</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000026.SZ</td>\n", "      <td>2020</td>\n", "      <td>2019-06-30</td>\n", "      <td>NaN</td>\n", "      <td>2.540650e+08</td>\n", "      <td>2.998050e+08</td>\n", "      <td>2.769350e+08</td>\n", "      <td>0.073816</td>\n", "      <td>0.080461</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>3.441868e+09</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  stock_code  forecast_year prediction_date  industry_l1  consensus_profit_fy  \\\n", "0  000001.SZ           2020      2019-06-30          NaN         3.224965e+10   \n", "1  000002.SZ           2020      2019-06-30          NaN         4.878083e+10   \n", "2  000008.SZ           2020      2019-06-30          NaN         5.910000e+08   \n", "3  000012.SZ           2020      2019-06-30          NaN         7.100870e+08   \n", "4  000026.SZ           2020      2019-06-30          NaN         2.540650e+08   \n", "\n", "   consensus_profit_fy2  consensus_profit_roll     EP_FY   EP_ROLL  Growth_FY  \\\n", "0          3.765754e+10           3.495359e+10  0.136300  0.147728        NaN   \n", "1          5.651548e+10           5.264816e+10  0.155198  0.167503        NaN   \n", "2          7.510000e+08           6.710000e+08  0.056374  0.064005        NaN   \n", "3          9.900500e+08           8.500685e+08  0.054917  0.065743        NaN   \n", "4          2.998050e+08           2.769350e+08  0.073816  0.080461        NaN   \n", "\n", "   PEG_proxy  DEP  EP_PER  market_value  analyst_count  \n", "0        NaN  NaN     NaN  2.366083e+11             24  \n", "1        NaN  NaN     NaN  3.143126e+11             32  \n", "2        NaN  NaN     NaN  1.048360e+10              1  \n", "3        NaN  NaN     NaN  1.293010e+10              3  \n", "4        NaN  NaN     NaN  3.441868e+09              2  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"=== 缺失值分析 ===\")\n", "\n", "# 分析主要指标的缺失情况\n", "key_factors = ['consensus_profit_fy', 'consensus_profit_roll', 'EP_FY', 'EP_ROLL']\n", "\n", "missing_analysis = pd.DataFrame({\n", "    '指标': key_factors,\n", "    '总样本数': len(df_factors),\n", "    '缺失数量': [df_factors[col].isna().sum() for col in key_factors],\n", "    '缺失比例(%)': [df_factors[col].isna().sum() / len(df_factors) * 100 for col in key_factors]\n", "})\n", "missing_analysis = missing_analysis.round(2)\n", "\n", "print(missing_analysis)\n", "print(f\"\\n行业信息覆盖率: {df_factors['industry_l1'].notna().sum()}/{len(df_factors)} ({df_factors['industry_l1'].notna().sum()/len(df_factors)*100:.1f}%)\")\n", "\n", "# 显示前几行数据\n", "print(\"\\n数据预览:\")\n", "df_factors.head()"]}, {"cell_type": "markdown", "id": "80e62612", "metadata": {}, "source": ["## 3. 行业中位数填充"]}, {"cell_type": "code", "execution_count": 4, "id": "1328b61c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 行业中位数填充 ===\n", "警告：consensus_profit_fy 无行业信息，跳过填充\n", "警告：consensus_profit_roll 无行业信息，跳过填充\n", "警告：EP_FY 无行业信息，跳过填充\n", "警告：EP_ROLL 无行业信息，跳过填充\n", "\n", "行业中位数填充完成\n"]}], "source": ["print(\"=== 行业中位数填充 ===\")\n", "\n", "def industry_median_fill(df, target_col, industry_col='industry_l1'):\n", "    \"\"\"使用行业中位数填充缺失值\"\"\"\n", "    result = df[target_col].copy()\n", "    \n", "    # 只对有行业信息的数据计算中位数\n", "    valid_industry = df[industry_col].notna()\n", "    \n", "    if valid_industry.sum() == 0:\n", "        print(f\"警告：{target_col} 无行业信息，跳过填充\")\n", "        return result\n", "    \n", "    # 计算各行业的中位数\n", "    industry_medians = df[valid_industry].groupby(industry_col)[target_col].median()\n", "    \n", "    # 填充缺失值\n", "    missing_mask = result.isna() & valid_industry\n", "    \n", "    for idx in df[missing_mask].index:\n", "        industry = df.loc[idx, industry_col]\n", "        if industry in industry_medians.index:\n", "            result.loc[idx] = industry_medians[industry]\n", "    \n", "    filled_count = missing_mask.sum() - result.isna().sum()\n", "    print(f\"{target_col}: 填充了 {filled_count} 个缺失值\")\n", "    \n", "    return result\n", "\n", "# 创建填充后的数据副本\n", "df_filled = df_factors.copy()\n", "\n", "# 对主要指标进行行业中位数填充\n", "for col in ['consensus_profit_fy', 'consensus_profit_roll', 'EP_FY', 'EP_ROLL']:\n", "    if col in df_filled.columns:\n", "        df_filled[f'{col}_filled'] = industry_median_fill(df_filled, col)\n", "\n", "print(\"\\n行业中位数填充完成\")"]}, {"cell_type": "markdown", "id": "84b98fae", "metadata": {}, "source": ["## 4. 填充效果评估"]}, {"cell_type": "code", "execution_count": 5, "id": "31647b93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 填充效果评估 ===\n", "                      指标  原始覆盖率(%)  填充后覆盖率(%)  提升(%)\n", "0    consensus_profit_fy    100.00     100.00    0.0\n", "1  consensus_profit_roll    100.00     100.00    0.0\n", "2                  EP_FY     99.47      99.47    0.0\n", "3                EP_ROLL     99.47      99.47    0.0\n", "\n", "填充后数据预览:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>stock_code</th>\n", "      <th>industry_l1</th>\n", "      <th>EP_FY</th>\n", "      <th>EP_FY_filled</th>\n", "      <th>EP_ROLL</th>\n", "      <th>EP_ROLL_filled</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>000001.SZ</td>\n", "      <td>NaN</td>\n", "      <td>0.136300</td>\n", "      <td>0.136300</td>\n", "      <td>0.147728</td>\n", "      <td>0.147728</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>000002.SZ</td>\n", "      <td>NaN</td>\n", "      <td>0.155198</td>\n", "      <td>0.155198</td>\n", "      <td>0.167503</td>\n", "      <td>0.167503</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>000008.SZ</td>\n", "      <td>NaN</td>\n", "      <td>0.056374</td>\n", "      <td>0.056374</td>\n", "      <td>0.064005</td>\n", "      <td>0.064005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>000012.SZ</td>\n", "      <td>NaN</td>\n", "      <td>0.054917</td>\n", "      <td>0.054917</td>\n", "      <td>0.065743</td>\n", "      <td>0.065743</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>000026.SZ</td>\n", "      <td>NaN</td>\n", "      <td>0.073816</td>\n", "      <td>0.073816</td>\n", "      <td>0.080461</td>\n", "      <td>0.080461</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  stock_code  industry_l1     EP_FY  EP_FY_filled   EP_ROLL  EP_ROLL_filled\n", "0  000001.SZ          NaN  0.136300      0.136300  0.147728        0.147728\n", "1  000002.SZ          NaN  0.155198      0.155198  0.167503        0.167503\n", "2  000008.SZ          NaN  0.056374      0.056374  0.064005        0.064005\n", "3  000012.SZ          NaN  0.054917      0.054917  0.065743        0.065743\n", "4  000026.SZ          NaN  0.073816      0.073816  0.080461        0.080461"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["print(\"=== 填充效果评估 ===\")\n", "\n", "# 对比填充前后的覆盖率\n", "comparison_data = []\n", "\n", "for col in ['consensus_profit_fy', 'consensus_profit_roll', 'EP_FY', 'EP_ROLL']:\n", "    if col in df_filled.columns and f'{col}_filled' in df_filled.columns:\n", "        original_coverage = df_filled[col].notna().sum() / len(df_filled) * 100\n", "        filled_coverage = df_filled[f'{col}_filled'].notna().sum() / len(df_filled) * 100\n", "        \n", "        comparison_data.append({\n", "            '指标': col,\n", "            '原始覆盖率(%)': round(original_coverage, 2),\n", "            '填充后覆盖率(%)': round(filled_coverage, 2),\n", "            '提升(%)': round(filled_coverage - original_coverage, 2)\n", "        })\n", "\n", "coverage_comparison = pd.DataFrame(comparison_data)\n", "print(coverage_comparison)\n", "\n", "# 显示填充后的数据预览\n", "print(\"\\n填充后数据预览:\")\n", "display_cols = ['stock_code', 'industry_l1', 'EP_FY', 'EP_FY_filled', 'EP_ROLL', 'EP_ROLL_filled']\n", "available_cols = [col for col in display_cols if col in df_filled.columns]\n", "df_filled[available_cols].head()"]}, {"cell_type": "markdown", "id": "072606d0", "metadata": {}, "source": ["## 5. 保存结果"]}, {"cell_type": "code", "execution_count": 6, "id": "a8e3943d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 保存填充结果 ===\n", "✅ 填充处理完成！\n", "最终数据量: 8,655\n", "涉及股票数量: 2,315\n", "\n", "保存的文件:\n", "- processed_data/factors_filled_industry_median.feather\n", "- processed_data/factors_filled_industry_median.csv\n", "- processed_data/filling_comparison_industry_median.csv\n", "- processed_data/missing_analysis.csv\n"]}], "source": ["print(\"=== 保存填充结果 ===\")\n", "\n", "# 创建最终版本的因子（优先使用原始值，缺失时使用填充值）\n", "final_data = df_filled.copy()\n", "\n", "for col in ['consensus_profit_fy', 'consensus_profit_roll', 'EP_FY', 'EP_ROLL']:\n", "    if f'{col}_filled' in final_data.columns:\n", "        final_data[f'{col}_final'] = final_data[col].fillna(final_data[f'{col}_filled'])\n", "\n", "# 保存完整的填充数据\n", "final_data.to_feather('processed_data/factors_filled_industry_median.feather')\n", "final_data.to_csv('processed_data/factors_filled_industry_median.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 保存填充效果对比\n", "coverage_comparison.to_csv('processed_data/filling_comparison_industry_median.csv', index=False, encoding='utf-8-sig')\n", "\n", "# 保存缺失值分析结果\n", "missing_analysis.to_csv('processed_data/missing_analysis.csv', index=False, encoding='utf-8-sig')\n", "\n", "print(f\"✅ 填充处理完成！\")\n", "print(f\"最终数据量: {len(final_data):,}\")\n", "print(f\"涉及股票数量: {final_data['stock_code'].nunique():,}\")\n", "print(f\"\\n保存的文件:\")\n", "print(f\"- processed_data/factors_filled_industry_median.feather\")\n", "print(f\"- processed_data/factors_filled_industry_median.csv\")\n", "print(f\"- processed_data/filling_comparison_industry_median.csv\")\n", "print(f\"- processed_data/missing_analysis.csv\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}