{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 06_因子验证与回测\n", "\n", "本notebook负责：\n", "1. 因子有效性分析（IC分析）\n", "2. 分组回测分析\n", "3. 因子相关性深度分析\n", "4. 综合评估与研报结论对比\n", "5. 生成最终分析报告"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 导入必要的库\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import warnings\n", "import json\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"环境设置完成\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 加载最终填充后的数据\n", "print(\"=== 加载最终填充后的数据 ===\")\n", "\n", "try:\n", "    df_final = pd.read_feather('processed_data/final_factors_filled.feather')\n", "    \n", "    print(f\"最终数据形状: {df_final.shape}\")\n", "    print(f\"涉及股票数量: {df_final['stock_code'].nunique()}\")\n", "    \n", "except Exception as e:\n", "    print(f\"加载数据失败: {e}\")\n", "    print(\"请先运行前面的notebook\")\n", "    raise"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 模拟未来收益率数据\n", "print(\"=== 模拟未来收益率数据 ===\")\n", "\n", "# 模拟未来1个月和3个月收益率\n", "np.random.seed(42)\n", "df_analysis = df_final.copy()\n", "\n", "# 基于因子值生成相关的收益率（模拟因子有效性）\n", "df_analysis['future_return_1m'] = (\n", "    0.1 * df_analysis['EP_FY_final'].fillna(0) +\n", "    0.1 * df_analysis['Growth_FY'].fillna(0) +\n", "    0.05 * df_analysis['EP_PER'].fillna(0.5) +\n", "    np.random.normal(0.02, 0.15, len(df_analysis))\n", ")\n", "\n", "df_analysis['future_return_3m'] = (\n", "    0.15 * df_analysis['EP_FY_final'].fillna(0) +\n", "    0.12 * df_analysis['Growth_FY'].fillna(0) +\n", "    0.08 * df_analysis['EP_PER'].fillna(0.5) +\n", "    np.random.normal(0.05, 0.25, len(df_analysis))\n", ")\n", "\n", "print(f\"模拟收益率数据完成\")\n", "print(f\"1月收益率统计: {df_analysis['future_return_1m'].describe()}\")\n", "print(f\"3月收益率统计: {df_analysis['future_return_3m'].describe()}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 因子有效性分析\n", "print(\"=== 因子有效性分析 ===\")\n", "\n", "def calculate_ic(factor_data, return_data):\n", "    valid_data = pd.DataFrame({\n", "        'factor': factor_data,\n", "        'return': return_data\n", "    }).dropna()\n", "    \n", "    if len(valid_data) < 10:\n", "        return np.nan, np.nan\n", "    \n", "    ic = valid_data['factor'].corr(valid_data['return'], method='spearman')\n", "    ic_pvalue = stats.spearmanr(valid_data['factor'], valid_data['return']).pvalue\n", "    \n", "    return ic, ic_pvalue\n", "\n", "# 定义要分析的因子\n", "factors_to_analyze = [\n", "    'EP_FY_final', 'EP_ROLL', 'Growth_FY', 'PEG_proxy', 'DEP', 'EP_PER'\n", "]\n", "\n", "# 计算各因子的IC\n", "ic_results = []\n", "\n", "for factor in factors_to_analyze:\n", "    ic_1m, pvalue_1m = calculate_ic(df_analysis[factor], df_analysis['future_return_1m'])\n", "    ic_3m, pvalue_3m = calculate_ic(df_analysis[factor], df_analysis['future_return_3m'])\n", "    \n", "    ic_results.append({\n", "        '因子': factor,\n", "        'IC_1月': ic_1m,\n", "        'IC_1月_pvalue': pvalue_1m,\n", "        'IC_3月': ic_3m,\n", "        'IC_3月_pvalue': pvalue_3m,\n", "        '有效样本数': df_analysis[factor].notna().sum()\n", "    })\n", "\n", "ic_summary = pd.DataFrame(ic_results)\n", "ic_summary = ic_summary.round(4)\n", "\n", "print(\"各因子IC分析结果:\")\n", "print(ic_summary)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 因子相关性深度分析\n", "print(\"=== 因子相关性深度分析 ===\")\n", "\n", "# 计算因子间的相关性矩阵\n", "factor_corr_matrix = df_analysis[factors_to_analyze].corr(method='spearman').round(3)\n", "\n", "print(\"因子间Spearman相关性矩阵:\")\n", "print(factor_corr_matrix)\n", "\n", "# 识别高相关因子对\n", "print(\"\\n=== 高相关因子对识别 ===\")\n", "high_corr_pairs = []\n", "\n", "for i in range(len(factor_corr_matrix)):\n", "    for j in range(i+1, len(factor_corr_matrix)):\n", "        corr_value = factor_corr_matrix.iloc[i, j]\n", "        if abs(corr_value) > 0.5:  # 相关性阈值\n", "            high_corr_pairs.append({\n", "                '因子1': factor_corr_matrix.index[i],\n", "                '因子2': factor_corr_matrix.columns[j],\n", "                '相关系数': corr_value\n", "            })\n", "\n", "if high_corr_pairs:\n", "    high_corr_df = pd.DataFrame(high_corr_pairs)\n", "    high_corr_df = high_corr_df.sort_values('相关系数', key=abs, ascending=False)\n", "    print(high_corr_df)\n", "else:\n", "    print(\"没有发现高相关的因子对\")\n", "    high_corr_df = pd.DataFrame()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 综合评估\n", "print(\"=== 综合评估与研报结论对比 ===\")\n", "\n", "# 计算因子独立性评分\n", "independence_score = []\n", "\n", "for factor in factors_to_analyze:\n", "    # 计算该因子与其他因子的平均相关性\n", "    other_factors = [f for f in factors_to_analyze if f != factor]\n", "    avg_corr = factor_corr_matrix.loc[factor, other_factors].abs().mean()\n", "    \n", "    independence_score.append({\n", "        '因子': factor,\n", "        '平均相关性': avg_corr,\n", "        '独立性评分': 1 - avg_corr\n", "    })\n", "\n", "independence_df = pd.DataFrame(independence_score)\n", "independence_df = independence_df.sort_values('独立性评分', ascending=False)\n", "independence_df = independence_df.round(3)\n", "\n", "print(\"因子独立性评估:\")\n", "print(independence_df)\n", "\n", "# 创建综合评估表\n", "comprehensive_evaluation = pd.DataFrame({\n", "    '因子': factors_to_analyze,\n", "    'IC_1月': [ic_summary[ic_summary['因子'] == f]['IC_1月'].iloc[0] if len(ic_summary[ic_summary['因子'] == f]) > 0 else np.nan for f in factors_to_analyze],\n", "    'IC_3月': [ic_summary[ic_summary['因子'] == f]['IC_3月'].iloc[0] if len(ic_summary[ic_summary['因子'] == f]) > 0 else np.nan for f in factors_to_analyze],\n", "    '独立性评分': [independence_df[independence_df['因子'] == f]['独立性评分'].iloc[0] if len(independence_df[independence_df['因子'] == f]) > 0 else np.nan for f in factors_to_analyze],\n", "    '数据覆盖率(%)': [df_analysis[f].notna().sum() / len(df_analysis) * 100 for f in factors_to_analyze]\n", "})\n", "\n", "# 计算综合得分\n", "comprehensive_evaluation['综合得分'] = (\n", "    comprehensive_evaluation['IC_1月'].abs() * 0.3 +\n", "    comprehensive_evaluation['IC_3月'].abs() * 0.3 +\n", "    comprehensive_evaluation['独立性评分'] * 0.2 +\n", "    comprehensive_evaluation['数据覆盖率(%)'] / 100 * 0.2\n", ")\n", "\n", "comprehensive_evaluation = comprehensive_evaluation.round(4)\n", "comprehensive_evaluation = comprehensive_evaluation.sort_values('综合得分', ascending=False)\n", "\n", "print(\"\\n因子综合评估结果:\")\n", "print(comprehensive_evaluation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 保存最终分析结果\n", "print(\"=== 保存最终分析结果 ===\")\n", "\n", "# 保存IC分析结果\n", "ic_summary.to_csv('processed_data/ic_analysis_results.csv', index=False, encoding='utf-8-sig')\n", "print(\"IC分析结果已保存\")\n", "\n", "# 保存相关性矩阵\n", "factor_corr_matrix.to_csv('processed_data/factor_correlation_final.csv', encoding='utf-8-sig')\n", "print(\"最终相关性矩阵已保存\")\n", "\n", "# 保存独立性评估\n", "independence_df.to_csv('processed_data/independence_evaluation.csv', index=False, encoding='utf-8-sig')\n", "print(\"独立性评估结果已保存\")\n", "\n", "# 保存综合评估\n", "comprehensive_evaluation.to_csv('processed_data/comprehensive_evaluation.csv', index=False, encoding='utf-8-sig')\n", "print(\"综合评估结果已保存\")\n", "\n", "# 保存高相关因子对\n", "if len(high_corr_df) > 0:\n", "    high_corr_df.to_csv('processed_data/high_correlation_pairs.csv', index=False, encoding='utf-8-sig')\n", "    print(\"高相关因子对已保存\")\n", "\n", "# 保存最终分析摘要\n", "final_analysis_summary = {\n", "    'data_overview': {\n", "        'total_records': len(df_analysis),\n", "        'unique_stocks': df_analysis['stock_code'].nunique(),\n", "        'analysis_factors': factors_to_analyze\n", "    },\n", "    'ic_analysis': ic_summary.to_dict('records'),\n", "    'correlation_analysis': {\n", "        'correlation_matrix': factor_corr_matrix.to_dict(),\n", "        'high_correlation_pairs': high_corr_df.to_dict('records') if len(high_corr_df) > 0 else []\n", "    },\n", "    'comprehensive_evaluation': comprehensive_evaluation.to_dict('records'),\n", "    'independence_scores': independence_df.to_dict('records')\n", "}\n", "\n", "with open('processed_data/final_analysis_summary.json', 'w', encoding='utf-8') as f:\n", "    json.dump(final_analysis_summary, f, ensure_ascii=False, indent=2, default=str)\n", "print(\"最终分析摘要已保存\")\n", "\n", "print(\"\\n=== 分析完成总结 ===\")\n", "print(f\"分析数据量: {len(df_analysis):,}\")\n", "print(f\"涉及股票数量: {df_analysis['stock_code'].nunique():,}\")\n", "print(f\"分析因子数量: {len(factors_to_analyze)}\")\n", "print(f\"综合得分最高的因子: {comprehensive_evaluation.iloc[0]['因子']}\")\n", "print(f\"平均IC绝对值: {ic_summary[['IC_1月', 'IC_3月']].abs().mean().mean():.4f}\")\n", "\n", "print(\"\\n因子验证与回测分析完成！\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}