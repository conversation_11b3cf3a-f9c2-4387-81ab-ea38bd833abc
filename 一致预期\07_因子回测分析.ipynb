{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 07 - 因子回测分析\n", "\n", "使用真实股票日频数据回测EP_FY1和EP_ROLL因子的有效性\n", "\n", "## 分析内容：\n", "1. 加载日频股价数据和因子数据\n", "2. 计算股票收益率\n", "3. 因子分组回测\n", "4. 计算关键指标：年化收益、多空IR、IC、IC-IR、IC胜率\n", "5. 可视化分析结果"]}, {"cell_type": "code", "execution_count": 1, "id": "1f866600", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["环境设置完成\n"]}], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import warnings\n", "from datetime import datetime, timedelta\n", "import matplotlib.dates as mdates\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']\n", "plt.rcParams['axes.unicode_minus'] = False\n", "\n", "print(\"环境设置完成\")"]}, {"cell_type": "markdown", "id": "2cb60d4a", "metadata": {}, "source": ["## 1. 数据加载"]}, {"cell_type": "code", "execution_count": 2, "id": "5da32cfc", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 加载数据 ===\n", "因子数据加载成功: (9043, 11)\n", "因子数据列: ['stock_code', 'forecast_year', 'prediction_date', 'consensus_profit_fy', 'consensus_profit_fy2', 'consensus_profit_roll', 'market_value', 'EP_FY1', 'EP_ROLL', 'PE_FY1', 'PE_ROLL']\n", "有效因子数据: 8994\n", "日频数据加载成功: (13020585, 9)\n", "日频数据列: ['open', 'high', 'low', 'close', 'pre_close', 'change', 'pct_chg', 'vol', 'amount']\n", "缺少必要字段: ['adj_close']\n", "使用close作为adj_close\n", "日期范围: 2008-12-31 00:00:00 到 2025-05-30 00:00:00\n"]}], "source": ["print(\"=== 加载数据 ===\")\n", "\n", "# 1. 加载因子数据\n", "try:\n", "    df_factors = pd.read_feather('processed_data/derived_factors.feather')\n", "    print(f\"因子数据加载成功: {df_factors.shape}\")\n", "    print(f\"因子数据列: {list(df_factors.columns)}\")\n", "    \n", "    # 转换日期格式\n", "    df_factors['prediction_date'] = pd.to_datetime(df_factors['prediction_date'])\n", "    \n", "    # 只保留有EP_FY1和EP_ROLL数据的记录\n", "    df_factors = df_factors.dropna(subset=['EP_FY1', 'EP_ROLL'])\n", "    print(f\"有效因子数据: {len(df_factors)}\")\n", "    \n", "except Exception as e:\n", "    print(f\"加载因子数据失败: {e}\")\n", "    raise\n", "\n", "# 2. 加载日频股价数据\n", "try:\n", "    df_daily = pd.read_hdf(r'C:\\Users\\<USER>\\Desktop\\金元顺安\\一致预期\\data\\daily0925.h5', 'data')\n", "    print(f\"日频数据加载成功: {df_daily.shape}\")\n", "    \n", "    # 重置索引\n", "    if df_daily.index.names:\n", "        df_daily = df_daily.reset_index()\n", "    \n", "    # 转换日期格式\n", "    df_daily['trade_date'] = pd.to_datetime(df_daily['trade_date'])\n", "    \n", "    print(f\"日期范围: {df_daily['trade_date'].min()} 到 {df_daily['trade_date'].max()}\")\n", "    \n", "except Exception as e:\n", "    print(f\"加载日频数据失败: {e}\")\n", "    raise\n", "\n", "# 3. 加载复权因子数据\n", "try:\n", "    df_adjfactor = pd.read_hdf(r'C:\\Users\\<USER>\\Desktop\\金元顺安\\一致预期\\data\\adjfactor.hd5', 'data')\n", "    print(f\"复权因子数据加载成功: {df_adjfactor.shape}\")\n", "    \n", "    # 重置索引\n", "    if df_adjfactor.index.names:\n", "        df_adjfactor = df_adjfactor.reset_index()\n", "    \n", "    # 转换日期格式\n", "    df_adjfactor['trade_date'] = pd.to_datetime(df_adjfactor['trade_date'])\n", "    \n", "    print(f\"复权因子列: {list(df_adjfactor.columns)}\")\n", "    \n", "except Exception as e:\n", "    print(f\"加载复权因子失败: {e}\")\n", "    print(\"将使用原始收盘价\")\n", "    df_adjfactor = None\n", "\n", "# 4. 合并复权因子并计算复权价格\n", "if df_adjfactor is not None:\n", "    print(\"=== 计算复权价格 ===\")\n", "    \n", "    # 合并复权因子\n", "    df_daily = df_daily.merge(\n", "        df_adjfactor[['ts_code', 'trade_date', 'adj_factor']], \n", "        on=['ts_code', 'trade_date'], \n", "        how='left'\n", "    )\n", "    \n", "    # 计算复权收盘价\n", "    df_daily['adj_close'] = df_daily['close'] * df_daily['adj_factor'].fillna(1.0)\n", "    \n", "    print(f\"复权价格计算完成\")\n", "    print(f\"复权因子覆盖率: {df_daily['adj_factor'].notna().sum() / len(df_daily) * 100:.1f}%\")\n", "else:\n", "    # 如果没有复权因子，使用原始收盘价\n", "    df_daily['adj_close'] = df_daily['close']\n", "    print(\"使用原始收盘价作为复权价格\")"]}, {"cell_type": "markdown", "id": "a6d0c37c", "metadata": {}, "source": ["## 2. 数据预处理"]}, {"cell_type": "code", "execution_count": 3, "id": "db74422f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== 数据预处理 ===\n", "收益率计算完成\n"]}, {"ename": "KeyboardInterrupt", "evalue": "", "output_type": "error", "traceback": ["\u001b[1;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[1;31mKeyboardInterrupt\u001b[0m                         <PERSON><PERSON> (most recent call last)", "Cell \u001b[1;32mIn[3], line 49\u001b[0m\n\u001b[0;32m     46\u001b[0m     \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m pd\u001b[38;5;241m.\u001b[39mDataFrame(merged_data)\n\u001b[0;32m     48\u001b[0m \u001b[38;5;66;03m# 合并数据\u001b[39;00m\n\u001b[1;32m---> 49\u001b[0m df_analysis \u001b[38;5;241m=\u001b[39m merge_factor_returns(df_factors, df_returns)\n\u001b[0;32m     50\u001b[0m \u001b[38;5;28mprint\u001b[39m(\u001b[38;5;124mf\u001b[39m\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m数据合并完成: \u001b[39m\u001b[38;5;132;01m{\u001b[39;00m\u001b[38;5;28mlen\u001b[39m(df_analysis)\u001b[38;5;132;01m}\u001b[39;00m\u001b[38;5;124m 条记录\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     52\u001b[0m \u001b[38;5;66;03m# 移除收益率缺失的记录\u001b[39;00m\n", "Cell \u001b[1;32mIn[3], line 29\u001b[0m, in \u001b[0;36mmerge_factor_returns\u001b[1;34m(df_factors, df_returns)\u001b[0m\n\u001b[0;32m     24\u001b[0m pred_date \u001b[38;5;241m=\u001b[39m factor_row[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mprediction_date\u001b[39m\u001b[38;5;124m'\u001b[39m]\n\u001b[0;32m     26\u001b[0m \u001b[38;5;66;03m# 找到预测日期当天或之后最近的交易日\u001b[39;00m\n\u001b[0;32m     27\u001b[0m stock_returns \u001b[38;5;241m=\u001b[39m df_returns[\n\u001b[0;32m     28\u001b[0m     (df_returns[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mts_code\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m==\u001b[39m stock_code) \u001b[38;5;241m&\u001b[39m \n\u001b[1;32m---> 29\u001b[0m     (df_returns[\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrade_date\u001b[39m\u001b[38;5;124m'\u001b[39m] \u001b[38;5;241m>\u001b[39m\u001b[38;5;241m=\u001b[39m pred_date)\n\u001b[0;32m     30\u001b[0m ]\u001b[38;5;241m.\u001b[39msort_values(\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mtrade_date\u001b[39m\u001b[38;5;124m'\u001b[39m)\n\u001b[0;32m     32\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(stock_returns) \u001b[38;5;241m>\u001b[39m \u001b[38;5;241m0\u001b[39m:\n\u001b[0;32m     33\u001b[0m     \u001b[38;5;66;03m# 取最近的交易日数据\u001b[39;00m\n\u001b[0;32m     34\u001b[0m     nearest_return \u001b[38;5;241m=\u001b[39m stock_returns\u001b[38;5;241m.\u001b[39miloc[\u001b[38;5;241m0\u001b[39m]\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\ops\\common.py:76\u001b[0m, in \u001b[0;36m_unpack_zerodim_and_defer.<locals>.new_method\u001b[1;34m(self, other)\u001b[0m\n\u001b[0;32m     72\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mNotImplemented\u001b[39m\n\u001b[0;32m     74\u001b[0m other \u001b[38;5;241m=\u001b[39m item_from_zerodim(other)\n\u001b[1;32m---> 76\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m method(\u001b[38;5;28mself\u001b[39m, other)\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\arraylike.py:60\u001b[0m, in \u001b[0;36mOpsMixin.__ge__\u001b[1;34m(self, other)\u001b[0m\n\u001b[0;32m     58\u001b[0m \u001b[38;5;129m@unpack_zerodim_and_defer\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__ge__\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     59\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__ge__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[1;32m---> 60\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cmp_method(other, operator\u001b[38;5;241m.\u001b[39mge)\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\series.py:6119\u001b[0m, in \u001b[0;36mSeries._cmp_method\u001b[1;34m(self, other, op)\u001b[0m\n\u001b[0;32m   6116\u001b[0m lvalues \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_values\n\u001b[0;32m   6117\u001b[0m rvalues \u001b[38;5;241m=\u001b[39m extract_array(other, extract_numpy\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m, extract_range\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m)\n\u001b[1;32m-> 6119\u001b[0m res_values \u001b[38;5;241m=\u001b[39m ops\u001b[38;5;241m.\u001b[39mcomparison_op(lvalues, rvalues, op)\n\u001b[0;32m   6121\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_construct_result(res_values, name\u001b[38;5;241m=\u001b[39mres_name)\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\ops\\array_ops.py:330\u001b[0m, in \u001b[0;36mcomparison_op\u001b[1;34m(left, right, op)\u001b[0m\n\u001b[0;32m    321\u001b[0m         \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[0;32m    322\u001b[0m             \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mLengths must match to compare\u001b[39m\u001b[38;5;124m\"\u001b[39m, lvalues\u001b[38;5;241m.\u001b[39mshape, rvalues\u001b[38;5;241m.\u001b[39mshape\n\u001b[0;32m    323\u001b[0m         )\n\u001b[0;32m    325\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m should_extension_dispatch(lvalues, rvalues) \u001b[38;5;129;01mor\u001b[39;00m (\n\u001b[0;32m    326\u001b[0m     (\u001b[38;5;28misinstance\u001b[39m(rvalues, (Timedelta, BaseOffset, Timestamp)) \u001b[38;5;129;01mor\u001b[39;00m right \u001b[38;5;129;01mis\u001b[39;00m NaT)\n\u001b[0;32m    327\u001b[0m     \u001b[38;5;129;01mand\u001b[39;00m lvalues\u001b[38;5;241m.\u001b[39mdtype \u001b[38;5;241m!=\u001b[39m \u001b[38;5;28mobject\u001b[39m\n\u001b[0;32m    328\u001b[0m ):\n\u001b[0;32m    329\u001b[0m     \u001b[38;5;66;03m# Call the method on lvalues\u001b[39;00m\n\u001b[1;32m--> 330\u001b[0m     res_values \u001b[38;5;241m=\u001b[39m op(lvalues, rvalues)\n\u001b[0;32m    332\u001b[0m \u001b[38;5;28;01melif\u001b[39;00m is_scalar(rvalues) \u001b[38;5;129;01mand\u001b[39;00m isna(rvalues):  \u001b[38;5;66;03m# TODO: but not pd.NA?\u001b[39;00m\n\u001b[0;32m    333\u001b[0m     \u001b[38;5;66;03m# numpy does not like comparisons vs None\u001b[39;00m\n\u001b[0;32m    334\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m op \u001b[38;5;129;01mis\u001b[39;00m operator\u001b[38;5;241m.\u001b[39mne:\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\ops\\common.py:76\u001b[0m, in \u001b[0;36m_unpack_zerodim_and_defer.<locals>.new_method\u001b[1;34m(self, other)\u001b[0m\n\u001b[0;32m     72\u001b[0m             \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mNotImplemented\u001b[39m\n\u001b[0;32m     74\u001b[0m other \u001b[38;5;241m=\u001b[39m item_from_zerodim(other)\n\u001b[1;32m---> 76\u001b[0m \u001b[38;5;28;01mreturn\u001b[39;00m method(\u001b[38;5;28mself\u001b[39m, other)\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\arraylike.py:60\u001b[0m, in \u001b[0;36mOpsMixin.__ge__\u001b[1;34m(self, other)\u001b[0m\n\u001b[0;32m     58\u001b[0m \u001b[38;5;129m@unpack_zerodim_and_defer\u001b[39m(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m__ge__\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m     59\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m__ge__\u001b[39m(\u001b[38;5;28mself\u001b[39m, other):\n\u001b[1;32m---> 60\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_cmp_method(other, operator\u001b[38;5;241m.\u001b[39mge)\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\pandas\\core\\arrays\\datetimelike.py:1026\u001b[0m, in \u001b[0;36mDatetimeLikeArrayMixin._cmp_method\u001b[1;34m(self, other, op)\u001b[0m\n\u001b[0;32m   1024\u001b[0m o_mask \u001b[38;5;241m=\u001b[39m isna(other)\n\u001b[0;32m   1025\u001b[0m mask \u001b[38;5;241m=\u001b[39m \u001b[38;5;28mself\u001b[39m\u001b[38;5;241m.\u001b[39m_isnan \u001b[38;5;241m|\u001b[39m o_mask\n\u001b[1;32m-> 1026\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m mask\u001b[38;5;241m.\u001b[39many():\n\u001b[0;32m   1027\u001b[0m     nat_result \u001b[38;5;241m=\u001b[39m op \u001b[38;5;129;01mis\u001b[39;00m operator\u001b[38;5;241m.\u001b[39mne\n\u001b[0;32m   1028\u001b[0m     np\u001b[38;5;241m.\u001b[39mputmask(result, mask, nat_result)\n", "File \u001b[1;32md:\\anaconda\\Lib\\site-packages\\numpy\\core\\_methods.py:55\u001b[0m, in \u001b[0;36m_any\u001b[1;34m(a, axis, dtype, out, keepdims, where)\u001b[0m\n\u001b[0;32m     51\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_prod\u001b[39m(a, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, dtype\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, out\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, keepdims\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m,\n\u001b[0;32m     52\u001b[0m           initial\u001b[38;5;241m=\u001b[39m_NoValue, where\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m     53\u001b[0m     \u001b[38;5;28;01mreturn\u001b[39;00m umr_prod(a, axis, dtype, out, keepdims, initial, where)\n\u001b[1;32m---> 55\u001b[0m \u001b[38;5;28;01mdef\u001b[39;00m \u001b[38;5;21m_any\u001b[39m(a, axis\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, dtype\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, out\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m, keepdims\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mFalse\u001b[39;00m, \u001b[38;5;241m*\u001b[39m, where\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m):\n\u001b[0;32m     56\u001b[0m     \u001b[38;5;66;03m# Parsing keyword arguments is currently fairly slow, so avoid it for now\u001b[39;00m\n\u001b[0;32m     57\u001b[0m     \u001b[38;5;28;01mif\u001b[39;00m where \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;28;01mTrue\u001b[39;00m:\n\u001b[0;32m     58\u001b[0m         \u001b[38;5;28;01mreturn\u001b[39;00m umr_any(a, axis, dtype, out, keepdims)\n", "\u001b[1;31mKeyboardInterrupt\u001b[0m: "]}], "source": ["print(\"=== 数据预处理 ===\")\n", "\n", "# 计算股票收益率\n", "def calculate_returns(df_price, periods=[1, 5, 10, 20]):\n", "    \"\"\"计算不同周期的收益率\"\"\"\n", "    df_ret = df_price.copy()\n", "    \n", "    for period in periods:\n", "        df_ret[f'return_{period}d'] = df_ret.groupby('ts_code')['adj_close'].pct_change(period).shift(-period)\n", "    \n", "    return df_ret\n", "\n", "# 计算收益率\n", "df_returns = calculate_returns(df_daily)\n", "print(f\"收益率计算完成\")\n", "\n", "# 合并因子数据和收益率数据\n", "def merge_factor_returns(df_factors, df_returns):\n", "    \"\"\"合并因子数据和收益率数据\"\"\"\n", "    merged_data = []\n", "    \n", "    for _, factor_row in df_factors.iterrows():\n", "        stock_code = factor_row['stock_code']\n", "        pred_date = factor_row['prediction_date']\n", "        \n", "        # 找到预测日期当天或之后最近的交易日\n", "        stock_returns = df_returns[\n", "            (df_returns['ts_code'] == stock_code) & \n", "            (df_returns['trade_date'] >= pred_date)\n", "        ].sort_values('trade_date')\n", "        \n", "        if len(stock_returns) > 0:\n", "            # 取最近的交易日数据\n", "            nearest_return = stock_returns.iloc[0]\n", "            \n", "            # 合并数据\n", "            merged_row = factor_row.copy()\n", "            merged_row['actual_trade_date'] = nearest_return['trade_date']\n", "            merged_row['return_1d'] = nearest_return['return_1d']\n", "            merged_row['return_5d'] = nearest_return['return_5d']\n", "            merged_row['return_10d'] = nearest_return['return_10d']\n", "            merged_row['return_20d'] = nearest_return['return_20d']\n", "            \n", "            merged_data.append(merged_row)\n", "    \n", "    return pd.DataFrame(merged_data)\n", "\n", "# 合并数据\n", "df_analysis = merge_factor_returns(df_factors, df_returns)\n", "print(f\"数据合并完成: {len(df_analysis)} 条记录\")\n", "\n", "# 移除收益率缺失的记录\n", "df_analysis = df_analysis.dropna(subset=['return_1d', 'return_5d', 'return_10d', 'return_20d'])\n", "print(f\"有效分析数据: {len(df_analysis)} 条记录\")\n", "\n", "if len(df_analysis) == 0:\n", "    print(\"警告：没有有效的分析数据！\")\n", "    raise ValueError(\"数据合并失败，请检查日期匹配逻辑\")\n", "\n", "print(f\"分析数据预览:\")\n", "print(df_analysis[['stock_code', 'prediction_date', 'actual_trade_date', 'EP_FY1', 'EP_ROLL', 'return_5d', 'return_20d']].head())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. 因子分组回测"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 因子分组回测 ===\")\n", "\n", "def factor_group_backtest(df, factor_col, return_col, n_groups=5):\n", "    \"\"\"因子分组回测\"\"\"\n", "    df_clean = df[[factor_col, return_col]].dropna()\n", "    \n", "    if len(df_clean) == 0:\n", "        return None\n", "    \n", "    # 按因子值分组\n", "    df_clean['factor_group'] = pd.qcut(df_clean[factor_col], n_groups, labels=False, duplicates='drop')\n", "    \n", "    # 计算各组收益率\n", "    group_returns = df_clean.groupby('factor_group')[return_col].agg([\n", "        'mean', 'std', 'count'\n", "    ]).reset_index()\n", "    \n", "    group_returns.columns = ['group', 'mean_return', 'std_return', 'count']\n", "    group_returns['group'] = group_returns['group'] + 1  # 从1开始编号\n", "    \n", "    # 计算多空收益\n", "    if len(group_returns) >= 2:\n", "        long_short_return = group_returns.iloc[-1]['mean_return'] - group_returns.iloc[0]['mean_return']\n", "        long_short_std = np.sqrt(group_returns.iloc[-1]['std_return']**2 + group_returns.iloc[0]['std_return']**2)\n", "    else:\n", "        long_short_return = 0\n", "        long_short_std = 0\n", "    \n", "    return {\n", "        'group_returns': group_returns,\n", "        'long_short_return': long_short_return,\n", "        'long_short_std': long_short_std\n", "    }\n", "\n", "# 对EP_FY1和EP_ROLL进行分组回测\n", "factors_to_test = ['EP_FY1', 'EP_ROLL']\n", "return_periods = ['return_5d', 'return_20d']\n", "\n", "backtest_results = {}\n", "\n", "for factor in factors_to_test:\n", "    backtest_results[factor] = {}\n", "    print(f\"\\n--- {factor} 因子回测 ---\")\n", "    \n", "    for ret_period in return_periods:\n", "        result = factor_group_backtest(df_analysis, factor, ret_period)\n", "        if result:\n", "            backtest_results[factor][ret_period] = result\n", "            print(f\"{ret_period}: 多空收益 = {result['long_short_return']:.4f}\")\n", "            print(f\"分组收益率:\")\n", "            print(result['group_returns'][['group', 'mean_return', 'count']])\n", "        else:\n", "            print(f\"{ret_period}: 数据不足，无法分组\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. IC分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== IC分析 ===\")\n", "\n", "def calculate_ic_metrics(df, factor_col, return_col):\n", "    \"\"\"计算IC相关指标\"\"\"\n", "    df_clean = df[[factor_col, return_col]].dropna()\n", "    \n", "    if len(df_clean) < 10:\n", "        return None\n", "    \n", "    # 计算IC\n", "    ic = df_clean[factor_col].corr(df_clean[return_col], method='spearman')\n", "    \n", "    # 按时间分组计算IC序列（如果有足够的时间点）\n", "    if 'prediction_date' in df.columns:\n", "        df_clean_with_date = df[[factor_col, return_col, 'prediction_date']].dropna()\n", "        \n", "        # 按月分组计算IC\n", "        df_clean_with_date['year_month'] = df_clean_with_date['prediction_date'].dt.to_period('M')\n", "        \n", "        ic_series = []\n", "        for period, group in df_clean_with_date.groupby('year_month'):\n", "            if len(group) >= 5:  # 至少5个样本\n", "                period_ic = group[factor_col].corr(group[return_col], method='spearman')\n", "                if not np.isnan(period_ic):\n", "                    ic_series.append(period_ic)\n", "        \n", "        if len(ic_series) > 0:\n", "            ic_mean = np.mean(ic_series)\n", "            ic_std = np.std(ic_series)\n", "            ic_ir = ic_mean / ic_std if ic_std > 0 else 0\n", "            ic_win_rate = np.mean([x > 0 for x in ic_series])\n", "        else:\n", "            ic_mean = ic\n", "            ic_std = 0\n", "            ic_ir = 0\n", "            ic_win_rate = 1 if ic > 0 else 0\n", "    else:\n", "        ic_mean = ic\n", "        ic_std = 0\n", "        ic_ir = 0\n", "        ic_win_rate = 1 if ic > 0 else 0\n", "    \n", "    return {\n", "        'IC': ic,\n", "        'IC_mean': ic_mean,\n", "        'IC_std': ic_std,\n", "        'IC_IR': ic_ir,\n", "        'IC_win_rate': ic_win_rate,\n", "        'IC_series': ic_series\n", "    }\n", "\n", "# 计算IC指标\n", "ic_results = {}\n", "\n", "for factor in factors_to_test:\n", "    ic_results[factor] = {}\n", "    print(f\"\\n--- {factor} IC分析 ---\")\n", "    \n", "    for ret_period in return_periods:\n", "        ic_metrics = calculate_ic_metrics(df_analysis, factor, ret_period)\n", "        if ic_metrics:\n", "            ic_results[factor][ret_period] = ic_metrics\n", "            print(f\"{ret_period}:\")\n", "            print(f\"  IC = {ic_metrics['IC']:.4f}\")\n", "            print(f\"  IC_IR = {ic_metrics['IC_IR']:.4f}\")\n", "            print(f\"  IC胜率 = {ic_metrics['IC_win_rate']:.2%}\")\n", "        else:\n", "            print(f\"{ret_period}: 数据不足，无法计算IC\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. 生成因子评估表格"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 生成因子评估表格 ===\")\n", "\n", "def create_factor_summary_table(backtest_results, ic_results):\n", "    \"\"\"创建因子评估汇总表\"\"\"\n", "    summary_data = []\n", "    \n", "    for factor in factors_to_test:\n", "        for ret_period in return_periods:\n", "            row = {\n", "                '因子': factor,\n", "                '收益周期': ret_period.replace('return_', '').replace('d', '日')\n", "            }\n", "            \n", "            # 回测指标\n", "            if factor in backtest_results and ret_period in backtest_results[factor]:\n", "                bt_result = backtest_results[factor][ret_period]\n", "                \n", "                # 年化收益（假设252个交易日）\n", "                period_days = int(ret_period.replace('return_', '').replace('d', ''))\n", "                annualized_return = bt_result['long_short_return'] * (252 / period_days)\n", "                \n", "                # 多空IR\n", "                if bt_result['long_short_std'] > 0:\n", "                    long_short_ir = bt_result['long_short_return'] / bt_result['long_short_std']\n", "                else:\n", "                    long_short_ir = 0\n", "                \n", "                row['年化收益'] = f\"{annualized_return:.2%}\"\n", "                row['多空IR'] = f\"{long_short_ir:.4f}\"\n", "            else:\n", "                row['年化收益'] = 'N/A'\n", "                row['多空IR'] = 'N/A'\n", "            \n", "            # IC指标\n", "            if factor in ic_results and ret_period in ic_results[factor]:\n", "                ic_result = ic_results[factor][ret_period]\n", "                row['IC'] = f\"{ic_result['IC']:.4f}\"\n", "                row['IC-IR'] = f\"{ic_result['IC_IR']:.4f}\"\n", "                row['IC胜率'] = f\"{ic_result['IC_win_rate']:.2%}\"\n", "            else:\n", "                row['IC'] = 'N/A'\n", "                row['IC-IR'] = 'N/A'\n", "                row['IC胜率'] = 'N/A'\n", "            \n", "            summary_data.append(row)\n", "    \n", "    return pd.DataFrame(summary_data)\n", "\n", "# 生成汇总表\n", "factor_summary = create_factor_summary_table(backtest_results, ic_results)\n", "print(\"\\n=== 因子评估汇总表 ===\")\n", "print(factor_summary.to_string(index=False))\n", "\n", "# 保存表格\n", "factor_summary.to_csv('processed_data/factor_evaluation_summary.csv', index=False, encoding='utf-8-sig')\n", "print(\"\\n因子评估表已保存至: processed_data/factor_evaluation_summary.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. 可视化分析"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 可视化分析 ===\")\n", "\n", "# 1. 因子分组收益率可视化\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('因子分组回测结果', fontsize=16, fontweight='bold')\n", "\n", "plot_idx = 0\n", "for factor in factors_to_test:\n", "    for ret_period in return_periods:\n", "        if factor in backtest_results and ret_period in backtest_results[factor]:\n", "            ax = axes[plot_idx // 2, plot_idx % 2]\n", "            \n", "            group_data = backtest_results[factor][ret_period]['group_returns']\n", "            \n", "            bars = ax.bar(group_data['group'], group_data['mean_return'], \n", "                         color=['red' if x < 0 else 'green' for x in group_data['mean_return']],\n", "                         alpha=0.7)\n", "            \n", "            ax.set_title(f'{factor} - {ret_period.replace(\"return_\", \"\").replace(\"d\", \"日\")}收益')\n", "            ax.set_xlabel('分组')\n", "            ax.set_ylabel('平均收益率')\n", "            ax.grid(True, alpha=0.3)\n", "            \n", "            # 添加数值标签\n", "            for bar, value in zip(bars, group_data['mean_return']):\n", "                height = bar.get_height()\n", "                ax.text(bar.get_x() + bar.get_width()/2., height,\n", "                       f'{value:.3f}', ha='center', va='bottom' if height >= 0 else 'top')\n", "        \n", "        plot_idx += 1\n", "\n", "plt.tight_layout()\n", "plt.savefig('processed_data/factor_group_returns.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "# 2. 因子值与收益率散点图\n", "fig, axes = plt.subplots(2, 2, figsize=(15, 12))\n", "fig.suptitle('因子值与收益率关系', fontsize=16, fontweight='bold')\n", "\n", "plot_idx = 0\n", "for factor in factors_to_test:\n", "    for ret_period in return_periods:\n", "        ax = axes[plot_idx // 2, plot_idx % 2]\n", "        \n", "        # 获取有效数据\n", "        plot_data = df_analysis[[factor, ret_period]].dropna()\n", "        \n", "        if len(plot_data) > 0:\n", "            # 散点图\n", "            ax.scatter(plot_data[factor], plot_data[ret_period], alpha=0.5, s=20)\n", "            \n", "            # 添加趋势线\n", "            z = np.polyfit(plot_data[factor], plot_data[ret_period], 1)\n", "            p = np.poly1d(z)\n", "            ax.plot(plot_data[factor], p(plot_data[factor]), \"r--\", alpha=0.8)\n", "            \n", "            # 计算相关系数\n", "            corr = plot_data[factor].corr(plot_data[ret_period])\n", "            \n", "            ax.set_title(f'{factor} vs {ret_period.replace(\"return_\", \"\").replace(\"d\", \"日\")}收益\\n相关系数: {corr:.4f}')\n", "            ax.set_xlabel(factor)\n", "            ax.set_ylabel('收益率')\n", "            ax.grid(True, alpha=0.3)\n", "        \n", "        plot_idx += 1\n", "\n", "plt.tight_layout()\n", "plt.savefig('processed_data/factor_return_scatter.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n", "\n", "# 3. IC时间序列图（如果有足够的时间点）\n", "has_ic_series = False\n", "for factor in factors_to_test:\n", "    for ret_period in return_periods:\n", "        if (factor in ic_results and ret_period in ic_results[factor] and \n", "            len(ic_results[factor][ret_period]['IC_series']) > 1):\n", "            has_ic_series = True\n", "            break\n", "    if has_ic_series:\n", "        break\n", "\n", "if has_ic_series:\n", "    fig, axes = plt.subplots(2, 1, figsize=(15, 10))\n", "    fig.suptitle('IC时间序列', fontsize=16, fontweight='bold')\n", "    \n", "    for i, factor in enumerate(factors_to_test):\n", "        ax = axes[i]\n", "        \n", "        for ret_period in return_periods:\n", "            if (factor in ic_results and ret_period in ic_results[factor] and \n", "                len(ic_results[factor][ret_period]['IC_series']) > 1):\n", "                \n", "                ic_series = ic_results[factor][ret_period]['IC_series']\n", "                x_values = range(len(ic_series))\n", "                \n", "                ax.plot(x_values, ic_series, marker='o', \n", "                       label=f'{ret_period.replace(\"return_\", \"\").replace(\"d\", \"日\")}',\n", "                       alpha=0.7)\n", "        \n", "        ax.axhline(y=0, color='black', linestyle='--', alpha=0.5)\n", "        ax.set_title(f'{factor} IC时间序列')\n", "        ax.set_xlabel('时间期间')\n", "        ax.set_ylabel('IC值')\n", "        ax.legend()\n", "        ax.grid(True, alpha=0.3)\n", "    \n", "    plt.tight_layout()\n", "    plt.savefig('processed_data/ic_time_series.png', dpi=300, bbox_inches='tight')\n", "    plt.show()\n", "else:\n", "    print(\"IC时间序列数据不足，跳过时间序列图\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. 结果保存与总结"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(\"=== 结果保存与总结 ===\")\n", "\n", "# 保存详细的回测结果\n", "import json\n", "\n", "# 准备保存的数据\n", "results_to_save = {\n", "    'analysis_summary': {\n", "        'total_records': len(df_analysis),\n", "        'unique_stocks': df_analysis['stock_code'].nunique(),\n", "        'date_range': {\n", "            'start': df_analysis['prediction_date'].min().strftime('%Y-%m-%d'),\n", "            'end': df_analysis['prediction_date'].max().strftime('%Y-%m-%d')\n", "        },\n", "        'factors_tested': factors_to_test,\n", "        'return_periods': return_periods\n", "    },\n", "    'backtest_results': {},\n", "    'ic_results': {}\n", "}\n", "\n", "# 转换结果为可序列化格式\n", "for factor in factors_to_test:\n", "    if factor in backtest_results:\n", "        results_to_save['backtest_results'][factor] = {}\n", "        for ret_period in return_periods:\n", "            if ret_period in backtest_results[factor]:\n", "                bt_result = backtest_results[factor][ret_period]\n", "                results_to_save['backtest_results'][factor][ret_period] = {\n", "                    'long_short_return': float(bt_result['long_short_return']),\n", "                    'long_short_std': float(bt_result['long_short_std']),\n", "                    'group_returns': bt_result['group_returns'].to_dict('records')\n", "                }\n", "    \n", "    if factor in ic_results:\n", "        results_to_save['ic_results'][factor] = {}\n", "        for ret_period in return_periods:\n", "            if ret_period in ic_results[factor]:\n", "                ic_result = ic_results[factor][ret_period]\n", "                results_to_save['ic_results'][factor][ret_period] = {\n", "                    'IC': float(ic_result['IC']),\n", "                    'IC_mean': float(ic_result['IC_mean']),\n", "                    'IC_std': float(ic_result['IC_std']),\n", "                    'IC_IR': float(ic_result['IC_IR']),\n", "                    'IC_win_rate': float(ic_result['IC_win_rate']),\n", "                    'IC_series_length': len(ic_result['IC_series'])\n", "                }\n", "\n", "# 保存JSON结果\n", "with open('processed_data/factor_backtest_results.json', 'w', encoding='utf-8') as f:\n", "    json.dump(results_to_save, f, ensure_ascii=False, indent=2)\n", "\n", "# 保存分析数据\n", "df_analysis.to_csv('processed_data/factor_analysis_data.csv', index=False, encoding='utf-8-sig')\n", "\n", "print(\"\\n=== 回测分析完成 ===\")\n", "print(f\"分析数据量: {len(df_analysis):,} 条记录\")\n", "print(f\"涉及股票: {df_analysis['stock_code'].nunique():,} 只\")\n", "print(f\"时间范围: {df_analysis['prediction_date'].min().strftime('%Y-%m-%d')} 到 {df_analysis['prediction_date'].max().strftime('%Y-%m-%d')}\")\n", "print(f\"\\n测试因子: {', '.join(factors_to_test)}\")\n", "print(f\"收益周期: {', '.join([p.replace('return_', '').replace('d', '日') for p in return_periods])}\")\n", "\n", "print(\"\\n生成的文件:\")\n", "print(\"📊 因子评估汇总表: processed_data/factor_evaluation_summary.csv\")\n", "print(\"📈 分组收益图: processed_data/factor_group_returns.png\")\n", "print(\"📉 因子收益散点图: processed_data/factor_return_scatter.png\")\n", "if has_ic_series:\n", "    print(\"📊 IC时间序列图: processed_data/ic_time_series.png\")\n", "print(\"💾 详细回测结果: processed_data/factor_backtest_results.json\")\n", "print(\"💾 分析数据: processed_data/factor_analysis_data.csv\")\n", "\n", "print(\"\\n✅ EP_FY1和EP_ROLL因子回测分析完成！\")"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 5}