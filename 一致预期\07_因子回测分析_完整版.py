#!/usr/bin/env python3
"""
07 - 因子回测分析（完整版）

使用真实股票日频数据回测EP_FY和EP_ROLL因子的有效性
包含复权处理、因子分组回测、IC分析等完整功能

主要输出：
- 因子评估表格（年化收益、多空IR、IC、IC-IR、IC胜率）
- 分组收益可视化图表
- IC对比分析图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import json
from datetime import datetime

# 设置
warnings.filterwarnings('ignore')
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """高效数据加载"""
    print("=== 高效数据加载 ===")
    
    # 1. 加载因子数据
    df_factors = pd.read_feather('processed_data/derived_factors.feather')
    df_factors['prediction_date'] = pd.to_datetime(df_factors['prediction_date'])
    df_factors = df_factors.dropna(subset=['EP_FY', 'EP_ROLL'])
    print(f"因子数据: {len(df_factors)} 条记录")
    
    # 获取需要的股票列表和日期范围
    factor_stocks = df_factors['stock_code'].unique()
    min_date = df_factors['prediction_date'].min() - pd.Timedelta(days=30)
    max_date = df_factors['prediction_date'].max() + pd.Timedelta(days=30)
    
    print(f"需要的股票数量: {len(factor_stocks)}")
    print(f"日期范围: {min_date.strftime('%Y-%m-%d')} 到 {max_date.strftime('%Y-%m-%d')}")
    
    # 2. 加载日频数据（只加载需要的股票和日期范围）
    print("\n加载日频数据...")
    df_daily = pd.read_hdf(r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data\daily0925.h5', 'data')
    if df_daily.index.names:
        df_daily = df_daily.reset_index()
    
    df_daily['trade_date'] = pd.to_datetime(df_daily['trade_date'])
    
    # 过滤数据
    df_daily = df_daily[
        (df_daily['ts_code'].isin(factor_stocks)) &
        (df_daily['trade_date'] >= min_date) &
        (df_daily['trade_date'] <= max_date)
    ].copy()
    
    print(f"过滤后日频数据: {len(df_daily)} 条记录")
    
    # 3. 加载复权因子
    try:
        print("\n加载复权因子...")
        df_adjfactor = pd.read_hdf(r'C:\Users\<USER>\Desktop\金元顺安\一致预期\data\adjfactor.hd5', 'data')
        if df_adjfactor.index.names:
            df_adjfactor = df_adjfactor.reset_index()
        
        df_adjfactor['trade_date'] = pd.to_datetime(df_adjfactor['trade_date'])
        
        # 过滤复权因子数据
        df_adjfactor = df_adjfactor[
            (df_adjfactor['ts_code'].isin(factor_stocks)) &
            (df_adjfactor['trade_date'] >= min_date) &
            (df_adjfactor['trade_date'] <= max_date)
        ]
        
        # 合并复权因子
        df_daily = df_daily.merge(
            df_adjfactor[['ts_code', 'trade_date', 'adj_factor']], 
            on=['ts_code', 'trade_date'], 
            how='left'
        )
        
        # 计算复权收盘价
        df_daily['adj_close'] = df_daily['close'] * df_daily['adj_factor'].fillna(1.0)
        print(f"复权因子覆盖率: {df_daily['adj_factor'].notna().sum() / len(df_daily) * 100:.1f}%")
        
    except Exception as e:
        print(f"复权因子加载失败: {e}")
        df_daily['adj_close'] = df_daily['close']
        print("使用原始收盘价")
    
    print("\n数据加载完成")
    return df_factors, df_daily

def calculate_returns(df_daily):
    """高效收益率计算"""
    print("=== 高效收益率计算 ===")
    
    def calculate_returns_fast(df):
        """高效计算收益率"""
        df = df.sort_values(['ts_code', 'trade_date'])
        
        # 计算不同周期的收益率
        for period in [5, 20]:  # 只计算需要的周期
            df[f'return_{period}d'] = df.groupby('ts_code')['adj_close'].pct_change(period).shift(-period)
        
        return df
    
    df_returns = calculate_returns_fast(df_daily)
    print(f"收益率计算完成")
    
    # 移除收益率为空的记录
    df_returns = df_returns.dropna(subset=['return_5d', 'return_20d'])
    print(f"有效收益率数据: {len(df_returns)} 条记录")
    
    return df_returns

def merge_factor_returns_stable(df_factors, df_returns):
    """稳定的因子-收益率合并方法"""
    print("=== 稳定数据合并 ===")
    
    # 1. 准备数据
    factors = df_factors.copy()
    returns = df_returns.copy()
    
    # 2. 统一列名
    factors = factors.rename(columns={'stock_code': 'ts_code'})
    
    # 3. 确保时间列为datetime类型
    factors['prediction_date'] = pd.to_datetime(factors['prediction_date'])
    returns['trade_date'] = pd.to_datetime(returns['trade_date'])
    
    # 4. 使用常规merge + 时间过滤的方法
    print("执行数据合并...")
    merged = factors.merge(
        returns[['ts_code', 'trade_date', 'return_5d', 'return_20d']],
        on='ts_code',
        how='inner'
    )
    
    print(f"合并后数据量: {len(merged):,} 条")
    
    # 5. 过滤：只保留trade_date >= prediction_date的记录
    print("过滤时间条件...")
    merged = merged[merged['trade_date'] >= merged['prediction_date']]
    print(f"时间过滤后: {len(merged):,} 条")
    
    # 6. 对每个股票-预测日期组合，取最近的交易日
    print("选择最近交易日...")
    merged = merged.sort_values(['ts_code', 'prediction_date', 'trade_date'])
    result = merged.groupby(['ts_code', 'prediction_date']).first().reset_index()
    
    print(f"最终结果: {len(result):,} 条")
    
    return result

def factor_backtest_analysis(df, factor_col, return_col, n_groups=5):
    """因子回测分析"""
    df_clean = df[[factor_col, return_col, 'prediction_date']].dropna()
    
    if len(df_clean) < 50:
        return None
    
    # 按因子值分组
    df_clean['factor_group'] = pd.qcut(df_clean[factor_col], n_groups, labels=False, duplicates='drop')
    
    # 计算各组收益率
    group_stats = df_clean.groupby('factor_group')[return_col].agg([
        'mean', 'std', 'count'
    ]).reset_index()
    
    group_stats.columns = ['group', 'mean_return', 'std_return', 'count']
    group_stats['group'] = group_stats['group'] + 1
    
    # 计算多空组合
    if len(group_stats) >= 2:
        long_return = group_stats.iloc[-1]['mean_return']
        short_return = group_stats.iloc[0]['mean_return']
        long_short_return = long_return - short_return
        
        long_std = group_stats.iloc[-1]['std_return']
        short_std = group_stats.iloc[0]['std_return']
        long_short_std = np.sqrt(long_std**2 + short_std**2)
    else:
        long_short_return = 0
        long_short_std = 0
    
    # 计算IC
    ic = df_clean[factor_col].corr(df_clean[return_col], method='spearman')
    
    # 按月计算IC序列
    df_clean['year_month'] = df_clean['prediction_date'].dt.to_period('M')
    
    ic_series = []
    for period, group in df_clean.groupby('year_month'):
        if len(group) >= 10:
            period_ic = group[factor_col].corr(group[return_col], method='spearman')
            if not np.isnan(period_ic):
                ic_series.append(period_ic)
    
    if len(ic_series) > 1:
        ic_mean = np.mean(ic_series)
        ic_std = np.std(ic_series)
        ic_ir = ic_mean / ic_std if ic_std > 0 else 0
        ic_win_rate = np.mean([x > 0 for x in ic_series])
    else:
        ic_mean = ic
        ic_std = 0
        ic_ir = 0
        ic_win_rate = 1 if ic > 0 else 0
    
    return {
        'group_stats': group_stats,
        'long_short_return': long_short_return,
        'long_short_std': long_short_std,
        'ic': ic,
        'ic_mean': ic_mean,
        'ic_std': ic_std,
        'ic_ir': ic_ir,
        'ic_win_rate': ic_win_rate,
        'ic_series': ic_series
    }

def create_factor_summary_table(results, factors_to_test, return_periods):
    """生成因子评估表格"""
    print("=== 生成关键指标表格 ===")

    summary_data = []

    for factor in factors_to_test:
        for ret_period in return_periods:
            if factor in results and ret_period in results[factor]:
                result = results[factor][ret_period]

                # 计算年化收益
                period_days = int(ret_period.replace('return_', '').replace('d', ''))
                annualized_return = result['long_short_return'] * (252 / period_days)

                # 计算多空IR
                long_short_ir = result['long_short_return'] / result['long_short_std'] if result['long_short_std'] > 0 else 0

                summary_data.append({
                    '因子': factor,
                    '收益周期': ret_period.replace('return_', '').replace('d', '日'),
                    '年化收益': f"{annualized_return:.2%}",
                    '多空IR': f"{long_short_ir:.4f}",
                    'IC': f"{result['ic']:.4f}",
                    'IC-IR': f"{result['ic_ir']:.4f}",
                    'IC胜率': f"{result['ic_win_rate']:.2%}"
                })

    factor_summary = pd.DataFrame(summary_data)

    print("\n=== 因子评估汇总表 ===")
    print(factor_summary.to_string(index=False))

    # 保存表格
    factor_summary.to_csv('processed_data/factor_evaluation_summary.csv', index=False, encoding='utf-8-sig')
    print("\n✅ 因子评估表已保存")

    return factor_summary

def create_visualizations(results, factors_to_test, return_periods):
    """创建关键可视化图表"""
    print("=== 关键可视化展示 ===")

    # 1. 因子分组收益率图（核心展示）
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('EP_FY和EP_ROLL因子分组回测结果', fontsize=18, fontweight='bold')

    plot_idx = 0
    for factor in factors_to_test:
        for ret_period in return_periods:
            if factor in results and ret_period in results[factor]:
                ax = axes[plot_idx // 2, plot_idx % 2]

                group_data = results[factor][ret_period]['group_stats']

                # 绘制柱状图
                colors = ['red' if x < 0 else 'green' for x in group_data['mean_return']]
                bars = ax.bar(group_data['group'], group_data['mean_return'],
                             color=colors, alpha=0.7, edgecolor='black')

                # 添加数值标签
                for bar, value in zip(bars, group_data['mean_return']):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{value:.2%}', ha='center', va='bottom' if height >= 0 else 'top',
                           fontweight='bold')

                # 添加IC信息
                ic_value = results[factor][ret_period]['ic']
                ax.text(0.02, 0.98, f'IC = {ic_value:.4f}', transform=ax.transAxes,
                       fontsize=12, fontweight='bold', va='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

                ax.set_title(f'{factor} - {ret_period.replace("return_", "").replace("d", "日")}收益',
                            fontsize=14, fontweight='bold')
                ax.set_xlabel('分组（1=最低，5=最高）', fontsize=12)
                ax.set_ylabel('平均收益率', fontsize=12)
                ax.grid(True, alpha=0.3)
                ax.axhline(y=0, color='black', linestyle='-', alpha=0.8)

            plot_idx += 1

    plt.tight_layout()
    plt.savefig('processed_data/factor_group_returns_key.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 因子IC对比图
    fig, ax = plt.subplots(1, 1, figsize=(12, 8))

    ic_data = []
    labels = []

    for factor in factors_to_test:
        for ret_period in return_periods:
            if factor in results and ret_period in results[factor]:
                ic_data.append(results[factor][ret_period]['ic'])
                labels.append(f"{factor}\\n{ret_period.replace('return_', '').replace('d', '日')}")

    colors = ['skyblue' if x > 0 else 'lightcoral' for x in ic_data]
    bars = ax.bar(labels, ic_data, color=colors, alpha=0.8, edgecolor='black')

    # 添加数值标签
    for bar, value in zip(bars, ic_data):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height,
               f'{value:.4f}', ha='center', va='bottom' if height >= 0 else 'top',
               fontweight='bold', fontsize=12)

    ax.set_title('EP_FY和EP_ROLL因子IC对比', fontsize=16, fontweight='bold')
    ax.set_ylabel('IC值', fontsize=12)
    ax.grid(True, alpha=0.3)
    ax.axhline(y=0, color='black', linestyle='-', alpha=0.8)

    plt.tight_layout()
    plt.savefig('processed_data/factor_ic_comparison.png', dpi=300, bbox_inches='tight')
    plt.show()

    print("\\n✅ 关键可视化图表已生成")

def main():
    """主函数"""
    print("开始EP_FY和EP_ROLL因子回测分析")
    print("=" * 50)

    try:
        # 1. 数据加载
        df_factors, df_daily = load_data()

        # 2. 收益率计算
        df_returns = calculate_returns(df_daily)

        # 3. 数据合并
        df_analysis = merge_factor_returns_stable(df_factors, df_returns)

        # 重命名列
        df_analysis = df_analysis.rename(columns={
            'ts_code': 'stock_code',
            'trade_date': 'actual_trade_date'
        })

        # 移除收益率缺失的记录
        df_analysis = df_analysis.dropna(subset=['return_5d', 'return_20d'])

        print(f"\\n✅ 数据合并完成: {len(df_analysis):,} 条有效记录")
        print(f"数据覆盖率: {len(df_analysis) / len(df_factors) * 100:.1f}%")

        if len(df_analysis) == 0:
            raise ValueError("没有有效的分析数据，请检查数据匹配逻辑")

        # 4. 因子回测分析
        print("\\n=== 因子回测分析 ===")

        factors_to_test = ['EP_FY', 'EP_ROLL']
        return_periods = ['return_5d', 'return_20d']

        results = {}

        for factor in factors_to_test:
            results[factor] = {}
            print(f"\\n--- {factor} 分析 ---")

            for ret_period in return_periods:
                result = factor_backtest_analysis(df_analysis, factor, ret_period)
                if result:
                    results[factor][ret_period] = result
                    print(f"{ret_period}: IC={result['ic']:.4f}, 多空收益={result['long_short_return']:.4f}")
                else:
                    print(f"{ret_period}: 数据不足")

        # 5. 生成评估表格
        factor_summary = create_factor_summary_table(results, factors_to_test, return_periods)

        # 6. 创建可视化
        create_visualizations(results, factors_to_test, return_periods)

        # 7. 保存结果
        print("\\n=== 保存分析结果 ===")

        # 保存详细结果
        results_to_save = {
            'analysis_summary': {
                'total_records': len(df_analysis),
                'unique_stocks': df_analysis['stock_code'].nunique(),
                'date_range': {
                    'start': df_analysis['prediction_date'].min().strftime('%Y-%m-%d'),
                    'end': df_analysis['prediction_date'].max().strftime('%Y-%m-%d')
                },
                'factors_tested': factors_to_test,
                'return_periods': return_periods
            }
        }

        with open('processed_data/factor_backtest_results.json', 'w', encoding='utf-8') as f:
            json.dump(results_to_save, f, ensure_ascii=False, indent=2)

        # 保存分析数据
        df_analysis.to_csv('processed_data/factor_analysis_data.csv', index=False, encoding='utf-8-sig')

        # 8. 总结
        print("\\n=== 分析完成总结 ===")
        print(f"总数据量: {len(df_analysis):,}")
        print(f"涉及股票: {df_analysis['stock_code'].nunique():,} 只")
        print(f"时间范围: {df_analysis['prediction_date'].min().strftime('%Y-%m-%d')} 到 {df_analysis['prediction_date'].max().strftime('%Y-%m-%d')}")
        print(f"分析因子: {', '.join(factors_to_test)}")

        print("\\n生成的文件:")
        print("📊 因子评估表: processed_data/factor_evaluation_summary.csv")
        print("📈 分组收益图: processed_data/factor_group_returns_key.png")
        print("📉 IC对比图: processed_data/factor_ic_comparison.png")
        print("💾 详细结果: processed_data/factor_backtest_results.json")
        print("💾 分析数据: processed_data/factor_analysis_data.csv")

        print("\\n✅ EP_FY和EP_ROLL因子回测分析完成！")

        return df_analysis, results, factor_summary

    except Exception as e:
        print(f"\\n❌ 分析过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None, None, None

if __name__ == "__main__":
    df_analysis, results, factor_summary = main()
