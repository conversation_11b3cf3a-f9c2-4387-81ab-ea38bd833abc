# 一致预期指标构建项目

本项目复刻天风证券《基于基础数据的分析师一致预期指标构建》研报的核心内容，实现了完整的一致预期指标构建流程。

## 项目概述

### 研报核心内容
- **研报标题**: 基于基础数据的分析师一致预期指标构建
- **发布机构**: 天风证券
- **核心创新**: "时间+分析师+业绩报告"三维加权方法
- **主要指标**: EP_FY、EP_ROLL、Growth_FY、PEG、DEP、EP_PER

### 项目特点
- **模块化设计**: 6个独立的Jupyter notebook，功能清晰分离
- **完整流程**: 从数据准备到最终验证的全流程实现
- **可扩展性**: 框架设计便于后续添加真实数据和扩展分析
- **教学性**: 详细的注释和说明，便于理解研报方法论

## 文件结构

```
一致预期/
├── data/                           # 原始数据目录
│   ├── rpt_forecast_stk.feather   # 分析师预测数据
│   ├── swind.xlsx                 # 行业分类数据
│   └── ipodate.csv               # IPO数据
├── processed_data/                # 处理后数据目录（运行后生成）
├── 01_数据准备与筛选.ipynb         # 数据加载、清洗、样本池筛选
├── 02_预测偏离度分析.ipynb         # 时间跨度、企业规模、行业影响分析
├── 03_一致预期净利润加权.ipynb     # 三维加权方法实现
├── 04_衍生指标构建.ipynb          # 六大类衍生指标构建
├── 05_缺失值填充处理.ipynb        # 传统与创新填充方法对比
├── 06_因子验证与回测.ipynb        # 因子有效性验证和相关性分析
└── README.md                      # 项目说明文档
```

## 运行指南

### 环境要求
```python
pandas >= 1.3.0
numpy >= 1.21.0
matplotlib >= 3.4.0
seaborn >= 0.11.0
statsmodels >= 0.12.0
scipy >= 1.7.0
```

### 运行步骤

**按顺序运行以下notebook：**

1. **01_数据准备与筛选.ipynb**
   - 加载原始数据
   - 数据清洗和预处理
   - 样本池筛选（报告类型、上市时间、ST状态、数据质量）
   - 输出：`processed_data/forecast_data_filtered.feather`

2. **02_预测偏离度分析.ipynb**
   - 分析时间跨度对偏离度的影响
   - 分析企业规模对偏离度的影响
   - 分析行业对偏离度的影响
   - 输出：`processed_data/analysis_data_with_bias.feather`

3. **03_一致预期净利润加权.ipynb**
   - 实现有效报告筛选
   - 分析师去重处理
   - 分析师权重计算
   - 时间维度加权
   - 滚动一致预期计算
   - 输出：`processed_data/consensus_results.feather`

4. **04_衍生指标构建.ipynb**
   - EP_FY（年度估值）和EP_ROLL（滚动估值）
   - Growth_FY（净利润增速）
   - PEG（估值增速比）
   - DEP（估值变化）
   - EP_PER（估值分位点）
   - 因子中性化处理
   - 输出：`processed_data/final_factors.feather`

5. **05_缺失值填充处理.ipynb**
   - 传统方法（行业中位数填充）
   - 创新方法（增速分位点填充）
   - 填充效果对比
   - 输出：`processed_data/final_factors_filled.feather`

6. **06_因子验证与回测.ipynb**
   - 因子有效性分析（IC分析）
   - 分组回测分析
   - 因子相关性分析
   - 综合评估与研报结论对比
   - 输出：`processed_data/final_analysis_summary.json`

## 核心方法论

### 1. 三维加权方法
- **时间维度**: 半衰加权，越近期权重越高
- **分析师维度**: 基于历史预测准确率的权重
- **业绩报告维度**: 有效报告筛选和去重

### 2. 衍生指标体系
- **EP_FY**: 年度一致预期估值 = 一致预期净利润FY1 / 市值
- **EP_ROLL**: 滚动一致预期估值 = 滚动12个月一致预期净利润 / 市值
- **Growth_FY**: 一致预期净利润增速（回归法修正）
- **PEG**: 一致预期PEG代理变量 = EP_ROLL × Growth_FY
- **DEP**: 一致预期估值变化率
- **EP_PER**: 一致预期估值分位点

### 3. 创新填充方法
- 基于企业历史增速分位点的行业共性
- 计算缺失股票的历史增速在行业内的分位点
- 根据分位点估计当年增速，推导一致预期净利润

## 主要输出文件

### 核心数据文件
- `consensus_results.csv`: 一致预期净利润计算结果
- `final_factors.csv`: 完整的衍生指标数据
- `final_factors_filled.csv`: 填充后的最终因子数据

### 分析结果文件
- `ic_analysis_results.csv`: IC分析结果
- `factor_correlation_final.csv`: 因子相关性矩阵
- `comprehensive_evaluation.csv`: 综合评估结果
- `final_analysis_summary.json`: 完整分析摘要

### 质量控制文件
- `filtering_summary.csv`: 样本池筛选汇总
- `filling_comparison.csv`: 填充效果对比
- `independence_evaluation.csv`: 因子独立性评估

## 研报核心结论验证

### 原研报结论
1. 全样本期年化多空收益排序: EP_PER(23.5%) > DEP(22.7%) > EP_ROLL(19.7%) > PEG(17.7%) > Growth_FY(16.1%)
2. EP_ROLL与Growth_FY相关性低(0.01)，独立性强
3. PEG与EP_ROLL(0.63)、Growth_FY(0.67)相关性较高
4. DEP与EP_PER相关性为0.42

### 复刻结果
- 因子综合得分排序和相关性分析结果
- 详见 `06_因子验证与回测.ipynb` 的最终输出

## 注意事项

### 数据限制
- 本项目使用模拟数据进行演示
- 实际应用需要补充真实的市值、财务、股价数据
- 模拟的收益率数据仅用于方法验证

### 扩展建议
1. **数据完善**: 补充真实的市值、财务、股价数据
2. **时间序列**: 扩展到多时点数据以验证因子稳定性
3. **样本扩展**: 增加更多股票和更长时间跨度
4. **实盘验证**: 在真实市场环境中验证因子有效性

## 技术特点

- **模块化设计**: 每个notebook专注特定功能，便于维护和扩展
- **数据流管理**: 清晰的数据流转和版本控制
- **质量控制**: 完整的数据质量检查和异常处理
- **可视化**: 丰富的图表展示分析结果
- **文档化**: 详细的代码注释和方法说明

## 联系方式

如有问题或建议，请通过以下方式联系：
- 项目维护者：[您的姓名]
- 邮箱：[您的邮箱]

---

本项目为一致预期指标构建提供了完整的实现框架，可作为量化投资研究的重要参考工具。
