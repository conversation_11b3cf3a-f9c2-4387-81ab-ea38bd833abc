import h5py
import numpy as np

# 查看复权因子数据结构
with h5py.File('data/adjfactor.hd5', 'r') as f:
    print('文件中的键:', list(f.keys()))
    
    data_group = f['data']
    print('data组中的键:', list(data_group.keys()))
    
    # 查看各个数据集
    for key in data_group.keys():
        dataset = data_group[key]
        print(f'{key}: 形状={dataset.shape}, 类型={dataset.dtype}')
        if len(dataset.shape) == 1 and dataset.shape[0] < 20:
            print(f'  数据: {dataset[:]}')
        elif len(dataset.shape) == 2:
            print(f'  前5行: {dataset[:5]}')
