import pandas as pd
import numpy as np

# 检查预测数据的时间范围
print('=== 检查预测数据时间范围 ===')
df_forecast = pd.read_feather('data/rpt_forecast_stk.feather')
df_forecast['create_date'] = pd.to_datetime(df_forecast['create_date'])

print(f'数据总量: {len(df_forecast):,} 条')
print(f'时间范围: {df_forecast["create_date"].min()} 到 {df_forecast["create_date"].max()}')
print(f'预测年份范围: {df_forecast["forecast_year"].min()} 到 {df_forecast["forecast_year"].max()}')
print(f'股票数量: {df_forecast["stock_code"].nunique():,}')

# 按年份统计
print(f'\n=== 按年份统计 ===')
year_stats = df_forecast.groupby(df_forecast['create_date'].dt.year).agg({
    'stock_code': 'nunique',
    'create_date': 'count'
}).rename(columns={'stock_code': '股票数', 'create_date': '记录数'})
print(year_stats)

# 检查市值数据时间范围
print(f'\n=== 检查市值数据时间范围 ===')
df_market = pd.read_hdf('data/ind.h5', 'data')
if df_market.index.names:
    df_market = df_market.reset_index()
df_market['trade_date'] = pd.to_datetime(df_market['trade_date'])

print(f'市值数据总量: {len(df_market):,} 条')
print(f'时间范围: {df_market["trade_date"].min()} 到 {df_market["trade_date"].max()}')
print(f'股票数量: {df_market["ts_code"].nunique():,}')

# 检查日频数据时间范围
print(f'\n=== 检查日频数据时间范围 ===')
df_daily = pd.read_hdf('data/daily0925.h5', 'data')
if df_daily.index.names:
    df_daily = df_daily.reset_index()
df_daily['trade_date'] = pd.to_datetime(df_daily['trade_date'])

print(f'日频数据总量: {len(df_daily):,} 条')
print(f'时间范围: {df_daily["trade_date"].min()} 到 {df_daily["trade_date"].max()}')
print(f'股票数量: {df_daily["ts_code"].nunique():,}')
