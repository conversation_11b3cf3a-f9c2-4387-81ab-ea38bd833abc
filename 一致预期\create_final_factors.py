#!/usr/bin/env python3
"""
简单脚本：基于derived_factors.csv创建final_factors文件
避免pandas版本问题，使用基础CSV操作
"""

import csv
import os

def main():
    print("=== 基于CSV创建final_factors ===")
    
    # 读取derived_factors.csv
    input_file = 'processed_data/derived_factors.csv'
    if not os.path.exists(input_file):
        print(f"错误：{input_file} 不存在")
        return
    
    # 读取数据
    rows = []
    with open(input_file, 'r', encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        headers = reader.fieldnames
        print(f"原始列: {headers}")
        
        for row in reader:
            rows.append(row)
    
    print(f"读取了 {len(rows)} 行数据")
    
    # 创建final_factors的列映射
    # 05需要的列：stock_code, forecast_year, prediction_date, industry_l1,
    # consensus_profit_fy, consensus_profit_fy2, consensus_profit_roll,
    # EP_FY, EP_ROLL, Growth_FY, PEG_proxy, DEP, EP_PER, market_value, analyst_count
    
    final_headers = [
        'stock_code', 'forecast_year', 'prediction_date', 'industry_l1',
        'consensus_profit_fy', 'consensus_profit_fy2', 'consensus_profit_roll',
        'EP_FY', 'EP_ROLL', 'Growth_FY', 'PEG_proxy', 'DEP', 'EP_PER',
        'market_value', 'analyst_count'
    ]
    
    # 创建输出数据
    output_rows = []
    for row in rows:
        new_row = {}
        
        # 直接复制存在的列
        for col in final_headers:
            if col in row:
                new_row[col] = row[col]
            elif col == 'EP_FY' and 'EP_FY1' in row:
                # EP_FY别名
                new_row[col] = row['EP_FY1']
            else:
                # 缺失的列填空
                new_row[col] = ''
        
        output_rows.append(new_row)
    
    # 写入final_factors.csv
    output_file = 'processed_data/final_factors.csv'
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as f:
        writer = csv.DictWriter(f, fieldnames=final_headers)
        writer.writeheader()
        writer.writerows(output_rows)
    
    print(f"✅ 已创建 {output_file}")
    print(f"包含 {len(output_rows)} 行数据")
    print(f"列: {final_headers}")
    
    # 显示前2行
    print("\n前2行预览:")
    for i, row in enumerate(output_rows[:2]):
        print(f"行{i+1}: {dict(list(row.items())[:5])}...")  # 只显示前5列
    
    print(f"\n✅ final_factors.csv 已生成！")
    print("注意：由于环境限制，只生成了CSV格式")
    print("05可以修改为读取CSV文件，或者在Jupyter中运行pandas转换为feather")

if __name__ == "__main__":
    main()
