#!/usr/bin/env python3
"""
快速生成 final_factors.feather 文件，供05使用
基于已有的 derived_factors.feather 进行转换
"""

import pandas as pd
import numpy as np

def main():
    print("=== 生成 final_factors.feather ===")
    
    # 1. 加载已有的 derived_factors
    try:
        df = pd.read_feather('processed_data/derived_factors.feather')
        print(f"加载 derived_factors 成功，形状: {df.shape}")
        print(f"列名: {list(df.columns)}")
    except Exception as e:
        print(f"加载 derived_factors 失败: {e}")
        print("请先运行 04_衍生指标构建_FIXED.ipynb")
        return
    
    # 2. 添加05需要的字段
    # EP_FY 别名（05期望这个名字）
    if 'EP_FY1' in df.columns and 'EP_FY' not in df.columns:
        df['EP_FY'] = df['EP_FY1']
        print("添加 EP_FY 别名")
    
    # 添加占位列（05期望这些列存在，即使为NaN）
    placeholder_cols = ['Growth_FY', 'PEG_proxy', 'DEP', 'EP_PER']
    for col in placeholder_cols:
        if col not in df.columns:
            df[col] = np.nan
            print(f"添加占位列: {col}")
    
    # 3. 尝试合并行业信息
    if 'industry_l1' not in df.columns:
        try:
            df_industry = pd.read_feather('processed_data/industry_data_clean.feather')
            latest_industry = df_industry.groupby('ts_code').last().reset_index()
            df = df.merge(
                latest_industry[['ts_code', 'l1_name']], 
                left_on='stock_code',
                right_on='ts_code',
                how='left'
            )
            df = df.rename(columns={'l1_name': 'industry_l1'})
            if 'ts_code' in df.columns:
                df = df.drop('ts_code', axis=1)
            print("成功合并行业信息")
        except Exception as e:
            print(f"合并行业信息失败: {e}")
            df['industry_l1'] = np.nan
            print("使用空的行业信息")
    
    # 4. 确保有analyst_count列
    if 'analyst_count' not in df.columns:
        df['analyst_count'] = np.nan
        print("添加 analyst_count 占位列")
    
    # 5. 选择05需要的列（按05的期望顺序）
    final_columns = [
        'stock_code', 'forecast_year', 'prediction_date', 'industry_l1',
        'consensus_profit_fy', 'consensus_profit_fy2', 'consensus_profit_roll',
        'EP_FY', 'EP_ROLL', 'Growth_FY', 'PEG_proxy', 'DEP', 'EP_PER',
        'market_value', 'analyst_count'
    ]
    
    # 只选择存在的列
    available_columns = [col for col in final_columns if col in df.columns]
    missing_columns = [col for col in final_columns if col not in df.columns]
    
    if missing_columns:
        print(f"缺失列: {missing_columns}")
        for col in missing_columns:
            df[col] = np.nan
        available_columns = final_columns
    
    final_df = df[available_columns].copy()
    
    # 6. 保存 final_factors
    final_df.to_feather('processed_data/final_factors.feather')
    final_df.to_csv('processed_data/final_factors.csv', index=False, encoding='utf-8-sig')
    
    print(f"✅ 成功生成 final_factors.feather")
    print(f"形状: {final_df.shape}")
    print(f"列名: {list(final_df.columns)}")
    
    # 7. 数据质量检查
    print("\n=== 数据质量检查 ===")
    for col in ['EP_FY', 'EP_ROLL', 'PE_FY1', 'PE_ROLL']:
        if col in final_df.columns:
            valid_count = final_df[col].notna().sum()
            total_count = len(final_df)
            coverage = valid_count / total_count * 100
            print(f"{col}: {valid_count:,}/{total_count:,} ({coverage:.1f}%)")
    
    print(f"\n前2行预览:")
    print(final_df.head(2))
    
    print(f"\n✅ final_factors.feather 已生成，05现在可以运行了！")

if __name__ == "__main__":
    main()
