import pandas as pd
import numpy as np

def merge_factors_returns_for_backtest_optimized(final_factors, df_returns):
    """
    内存优化的合并因子数据和收益率数据用于回测
    
    参数:
    - final_factors: 因子数据
    - df_returns: 收益率数据
    
    返回:
    - 合并后的回测数据
    """
    print("=== 合并因子和收益率数据（内存优化版）===")
    
    if df_returns is None:
        print("收益率数据不可用，跳过合并")
        return None
    
    # 准备数据
    factors = final_factors.copy()
    returns = df_returns.copy()
    
    print(f"因子数据: {len(factors):,} 条")
    print(f"收益率数据: {len(returns):,} 条")
    
    # 统一列名
    factors = factors.rename(columns={'stock_code': 'ts_code', 'date': 'factor_date'})
    
    # 确保时间列为datetime类型
    factors['factor_date'] = pd.to_datetime(factors['factor_date'])
    returns['trade_date'] = pd.to_datetime(returns['trade_date'])
    
    # 内存优化：先过滤收益率数据，只保留因子涉及的股票和时间范围
    factor_stocks = set(factors['ts_code'].unique())
    min_factor_date = factors['factor_date'].min() - pd.Timedelta(days=5)
    max_factor_date = factors['factor_date'].max() + pd.Timedelta(days=60)
    
    print(f"过滤收益率数据...")
    returns_filtered = returns[
        (returns['ts_code'].isin(factor_stocks)) &
        (returns['trade_date'] >= min_factor_date) &
        (returns['trade_date'] <= max_factor_date)
    ].copy()
    
    print(f"过滤后收益率数据: {len(returns_filtered):,} 条")
    
    # 分批处理以避免内存溢出
    batch_size = 500  # 每批处理500只股票
    unique_stocks = list(factor_stocks)
    all_results = []
    
    print(f"开始分批处理，共 {len(unique_stocks)} 只股票，每批 {batch_size} 只")
    
    for i in range(0, len(unique_stocks), batch_size):
        batch_stocks = unique_stocks[i:i+batch_size]
        print(f"处理批次 {i//batch_size + 1}/{(len(unique_stocks)-1)//batch_size + 1}: {len(batch_stocks)} 只股票")
        
        # 当前批次的因子数据
        factors_batch = factors[factors['ts_code'].isin(batch_stocks)].copy()
        
        # 当前批次的收益率数据
        returns_batch = returns_filtered[returns_filtered['ts_code'].isin(batch_stocks)].copy()
        
        if len(factors_batch) == 0 or len(returns_batch) == 0:
            continue
        
        # 合并当前批次
        merged_batch = factors_batch.merge(
            returns_batch[['ts_code', 'trade_date', 'return_20d']],
            on='ts_code',
            how='inner'
        )
        
        if len(merged_batch) == 0:
            continue
        
        # 过滤：只保留trade_date >= factor_date的记录
        merged_batch = merged_batch[merged_batch['trade_date'] >= merged_batch['factor_date']]
        
        if len(merged_batch) == 0:
            continue
        
        # 对每个股票-因子日期组合，取最近的交易日
        merged_batch = merged_batch.sort_values(['ts_code', 'factor_date', 'trade_date'])
        result_batch = merged_batch.groupby(['ts_code', 'factor_date']).first().reset_index()
        
        all_results.append(result_batch)
        
        # 清理内存
        del merged_batch, result_batch
    
    if len(all_results) == 0:
        print("❌ 没有成功合并任何数据")
        return None
    
    # 合并所有批次结果
    print("合并所有批次结果...")
    result = pd.concat(all_results, ignore_index=True)
    
    # 重命名回原来的列名
    result = result.rename(columns={
        'ts_code': 'stock_code',
        'factor_date': 'date',
        'trade_date': 'actual_trade_date'
    })
    
    print(f"✅ 合并完成: {len(result):,} 条有效记录")
    print(f"时间范围: {result['date'].min()} 到 {result['date'].max()}")
    print(f"涉及股票: {result['stock_code'].nunique():,}")
    
    return result

# 使用示例（在notebook中运行）:
# df_backtest = merge_factors_returns_for_backtest_optimized(final_factors, df_returns)
