#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试市值数据加载
"""

import pandas as pd
import numpy as np

def test_market_value_data():
    """测试市值数据加载"""
    
    print("=== 测试市值数据加载 ===")
    
    try:
        # 尝试使用pandas直接读取HDF5文件
        try:
            df_market_data = pd.read_hdf('data/ind.h5', 'data')
            print(f"使用pandas成功加载市值数据，形状: {df_market_data.shape}")
        except Exception as hdf_error:
            print(f"使用pandas读取HDF5失败: {hdf_error}")
            print("尝试其他方法...")
            return False

        print(f"列名: {list(df_market_data.columns)}")
        print(f"索引: {df_market_data.index.names}")

        # 查看数据样本
        print(f"\n数据样本:")
        print(df_market_data.head())

        # 检查total_mv字段
        if 'total_mv' in df_market_data.columns:
            print(f"\n找到total_mv字段")
            print(f"total_mv统计:")
            print(df_market_data['total_mv'].describe())

            # 检查数据质量
            total_count = len(df_market_data)
            valid_count = df_market_data['total_mv'].notna().sum()
            positive_count = (df_market_data['total_mv'] > 0).sum()

            print(f"\n数据质量:")
            print(f"总记录数: {total_count:,}")
            print(f"非空记录数: {valid_count:,} ({valid_count/total_count*100:.2f}%)")
            print(f"正值记录数: {positive_count:,} ({positive_count/total_count*100:.2f}%)")

            # 查看时间范围
            if df_market_data.index.names and 'trade_date' in df_market_data.index.names:
                dates = df_market_data.index.get_level_values('trade_date')
                print(f"\n时间范围: {dates.min()} 到 {dates.max()}")
            elif 'trade_date' in df_market_data.columns:
                print(f"\n时间范围: {df_market_data['trade_date'].min()} 到 {df_market_data['trade_date'].max()}")

            # 查看股票数量
            if df_market_data.index.names and 'ts_code' in df_market_data.index.names:
                stocks = df_market_data.index.get_level_values('ts_code').unique()
                print(f"涉及股票数: {len(stocks):,}")
            elif 'ts_code' in df_market_data.columns:
                stocks = df_market_data['ts_code'].unique()
                print(f"涉及股票数: {len(stocks):,}")

            return True

        else:
            print(f"\n未找到total_mv字段")
            print(f"可用字段: {list(df_market_data.columns)}")
            return False

    except Exception as e:
        print(f"加载市值数据失败: {e}")
        return False

def test_consensus_data():
    """测试一致预期数据"""
    
    print("\n=== 测试一致预期数据 ===")
    
    try:
        df_consensus = pd.read_feather('processed_data/consensus_results.feather')
        print(f"一致预期数据加载成功，形状: {df_consensus.shape}")
        print(f"列名: {list(df_consensus.columns)}")
        
        # 查看数据样本
        print(f"\n数据样本:")
        print(df_consensus.head())
        
        # 检查关键字段
        key_fields = ['stock_code', 'prediction_date', 'consensus_profit_fy', 'consensus_profit_roll']
        for field in key_fields:
            if field in df_consensus.columns:
                valid_count = df_consensus[field].notna().sum()
                print(f"{field}: {valid_count:,} / {len(df_consensus):,} ({valid_count/len(df_consensus)*100:.2f}%)")
        
        return True
        
    except Exception as e:
        print(f"加载一致预期数据失败: {e}")
        return False

if __name__ == "__main__":
    market_ok = test_market_value_data()
    consensus_ok = test_consensus_data()
    
    if market_ok and consensus_ok:
        print("\n✅ 所有数据测试通过，可以运行04_衍生指标构建.ipynb")
    else:
        print("\n❌ 数据测试失败，请检查数据文件")
