# 复刻《基于基础数据的分析师一致预期指标构建》研报：一致预期数据处理详细步骤

要复刻天风证券这份研报的核心内容，需围绕 “数据准备→预测偏离度分析→一致预期净利润加权→衍生指标构建→缺失值填充→验证与回测” 六大核心环节展开，每个环节需严格贴合研报逻辑与数据特性。以下是分模块的详细操作步骤，结合研报方法论与实操细节说明：

## 一、前期准备：数据筛选与基础定义（对应研报 2-3 章）

研报核心数据依赖 “分析师预测数据” 与 “上市公司基础数据”，需先完成数据筛选、字段匹配与关键概念定义，为后续处理奠定基础。

### 步骤 1：确定数据来源与核心字段

研报使用**朝阳永续分析师预测数据库**，复刻时需确保数据包含以下核心字段（可参考你已有数据的字段映射）：



| 数据类型        | 核心字段（研报定义）                                                                                     | 你的数据字段映射建议                                                                                | 用途说明                           |
| ----------- | ---------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------- | ------------------------------ |
| 分析师预测数据     | 股票代码、分析师 ID、机构 ID、报告撰写日期（CREATE\_DATE）、报告录入日期（INTO\_DATE）、预测净利润（Profit\_Forcast）、预测年度（FY1/FY2） | S\_INFO\_WINDCODE、author\_name、organ\_id、create\_date、entrytime、forecast\_np、report\_year | 计算一致预期净利润的核心原始数据               |
| 上市公司基础数据    | 实际净利润（Profit\_Real）、市值（TOT\_MV）、年报公告日期（report\_date）、行业分类、ST 状态                                | 上市公司财报中的净利润、总市值、年报发布日期、中信一级行业、是否 ST 标识                                                    | 计算预测偏离度、市值分组、行业中性化、样本筛选        |
| 业绩预告 / 快报数据 | 业绩预告净利润上下限、预告发布日期                                                                              | 上市公司业绩预告公告中的净利润区间、公告日期                                                                    | 优先使用企业官方数据修正一致预期（研报 4.3 章核心逻辑） |

### 步骤 2：样本池筛选（剔除无效样本）

研报明确样本池需排除 “上市不满 60 交易日、ST/ST 摘帽不满 60 交易日” 的股票，操作步骤如下：



1.  **上市时间筛选**：通过股票 “上市日期” 字段，计算 “当前日期 - 上市日期”≥60 个交易日，剔除次新股；

2.  **ST 状态筛选**：通过 “是否 ST” 字段，剔除标记为 “ST” 或 “\*ST” 的股票，以及 ST 摘帽后未满 60 交易日的股票；

3.  **报告类型筛选**：研报剔除 “简评文章、港股报告”，仅保留 “个股报告、深度报告、调研报告、点评报告、新股研究”（参考研报表 1），需通过 “report\_type” 或 “title” 关键词筛选（如你的数据中 report\_type=22/24/25 对应点评 / 调研 / 年报点评，可保留）。

### 步骤 3：定义关键概念与计算公式

提前明确研报中核心指标的定义，避免后续计算偏差：



1.  **预测偏离度（bias）**：衡量分析师预测误差，公式为：

$bias = \frac{|Profit\_Forcast - Profit\_Real|}{|Profit\_Real|}$

其中，$Profit\_Forcast$为分析师预测净利润，$Profit\_Real$为上市公司年报实际净利润；



1.  **报告时间差（delta）**：衡量数据商录入延迟，公式为：

$delta = INTO\_DATE - CREATE\_DATE$

（单位：天，研报显示 2016-2017 年约 84.8% 的报告在 2 天内录入，可用于验证数据时效性）；



1.  **预测时间跨度（δ）**：衡量预测与年报公告的时间距离，公式为：

$\delta = M_{report} - M_t$

其中，$M_{report}$为年报公告日所在月份，$M_t$为报告撰写日所在月份（如 8 月撰写、次年 4 月公告，δ=4+12-8=8）。

## 二、核心环节 1：预测偏离度影响因素分析（对应研报 3 章）

研报通过分析 “时间跨度、企业规模、行业” 对偏离度的影响，为后续加权逻辑提供依据，需量化验证这三个因素的显著性。

### 步骤 1：时间跨度对偏离度的影响



1.  **分组**：按 “预测时间跨度 δ” 将数据分为 12 组（δ=0 至 δ=11，δ=0 表示当月公告、当月预测）；

2.  **计算偏离度**：对每组计算 “偏离度中位数”（研报用中位数避免极端值影响）；

3.  **验证单调性**：绘制 “时间跨度 - 偏离度中位数” 折线图（参考研报图 9），需验证 “δ 越大，偏离度越高” 的单调性（研报结论：时间跨度是最显著影响因素）。

### 步骤 2：企业规模对偏离度的影响



1.  **市值分组**：按 “预测日当月的股票市值” 将样本分为 10 组（1 组为最小市值，10 组为最大市值）；

2.  **计算偏离度**：每组计算 “偏离度中位数”；

3.  **验证相关性**：绘制 “市值分组 - 偏离度中位数” 图（参考研报图 10），需验证 “市值越大，偏离度越低”（研报结论：大企业盈利稳定，预测更准确）。

### 步骤 3：行业对偏离度的影响



1.  **行业分类**：按 “中信一级行业” 将样本分组（共 29 个行业，如银行、食品饮料、钢铁等）；

2.  **计算偏离度**：每个行业计算 “偏离度中位数”；

3.  **对比差异**：绘制 “行业 - 偏离度中位数” 柱状图（参考研报图 11），需验证 “周期性行业（钢铁、煤炭）偏离度高，稳定行业（银行、食品饮料）偏离度低”（研报核心结论）。

## 三、核心环节 2：一致预期净利润加权计算（对应研报 4 章）

这是研报的核心创新点 —— 通过 “时间 + 分析师 + 业绩报告” 三维加权，提升一致预期净利润的准确性，需严格按以下步骤操作：

### 步骤 1：筛选有效报告（排除过期 / 无效数据）

研报定义 “有效报告” 为 “预测日前 3 个月内撰写且录入的报告”，步骤如下：



1.  **确定预测日（t）**：以 “月度调仓” 为频率（研报回溯时用月度调仓），取每月最后一个交易日为预测日 t；

2.  **时间范围筛选**：对每个预测日 t，筛选 “报告撰写日期≥t-3 个月” 且 “报告录入日期≤t” 的报告（确保数据在调仓时可获取）；

3.  **特殊情况处理**：若 3 个月内无有效报告，扩展至 “t-6 个月” 内的报告；若 6 个月内仍无报告，标记为 “一致预期缺失”。

### 步骤 2：分析师自身预测修正（去重最新值）

同一分析师可能多次修正对同一股票的预测，需保留最新预测：



1.  **分组去重**：按 “股票代码 + 分析师 ID + 预测年度（FY1/FY2）” 分组；

2.  **保留最新值**：每组内按 “报告撰写日期” 降序排序，仅保留撰写日期最新的 1 条预测数据（参考研报表 2，如分析师 3386 对 000001 的预测，仅保留 20150813 的 248.42 亿元）。

### 步骤 3：优先使用业绩报告数据（修正一致预期）

研报认为 “企业业绩预告 / 快报可信度高于分析师预测”，需优先替换：



1.  **业绩报告筛选**：对每个股票 - 预测年度，筛选 “业绩预告 / 快报发布日期≤预测日 t” 且 “发布日期≥上一次财报日期” 的报告；

2.  **数据替换**：

*   若为业绩快报：直接用快报中的 “净利润” 作为该股票 - 预测年度的一致预期净利润；

*   若为业绩预告：取 “净利润上限 + 净利润下限” 的均值作为一致预期净利润；

1.  **覆盖范围验证**：研报提到业绩报告修正约占总样本的 15%，可通过统计替换后的数据占比验证合理性（参考研报图 15，替换后偏离度显著降低）。

### 步骤 4：时间维度加权（赋予新报告更高权重）

基于 “时间跨度越小，预测越准确”，按 “撰写月份” 半衰加权：



1.  **月份分组**：对有效报告（已去重、无业绩报告的样本），按 “报告撰写月份” 分为 3 组（t 月、t-1 月、t-2 月，t 为预测日所在月）；

2.  **组内均值计算**：每组内计算 “预测净利润的简单均值”（先消除同月份内不同分析师的个体差异）；

3.  **半衰加权**：赋予 t 月均值权重 4、t-1 月权重 2、t-2 月权重 1，计算时间加权后的净利润：

$Profit\_time = \frac{4ÃProfit_t + 2ÃProfit_{t-1} + 1ÃProfit_{t-2}}{4+2+1}$

（若用 6 个月数据，分组为 t-t-5 月，权重依次为 32、16、8、4、2、1，仍按半衰逻辑）。

### 步骤 5：分析师维度加权（赋予高准确率分析师更高权重）

基于 “分析师历史预测准确率” 调整权重，步骤如下：



1.  **计算分析师历史修正后偏离度**：

*   取 “分析师上一年（t-1 年）的所有预测数据”，对每个预测数据计算原始偏离度 bias；

*   对 bias 进行回归调整（剔除时间跨度、市值、行业的影响），回归公式为：

$Bias = \sum_{i=1}^{29}\alpha_iÃIndustry_i + \beta_1Ã\delta + \beta_2ÃSize + \varepsilon$

其中，$Industry_i$为行业哑变量（29 个行业），$\delta$为预测时间跨度，$Size$为股票市值的标准化值；



*   取回归残差$\varepsilon$作为 “修正后偏离度”，并计算每个分析师 t-1 年的 “平均修正后偏离度”（若分析师 t-1 年预测次数 < 5 次，权重设为 1）；

1.  **权重映射**：将分析师 “平均修正后偏离度” 线性插值到 \[1,5] 区间（偏离度越小，权重越大），公式为：

$weight_i = 5 - \frac{bias_i - Min(bias)}{Max(bias) - Min(bias)}Ã4$

（如 t-1 年修正后偏离度最小的分析师权重 = 5，最大的 = 1）；



1.  **分析师加权计算**：对步骤 4 中的 “月份分组均值”，按分析师权重重新计算组内均值：

$Profit\_analyst = \frac{\sum(weight_iÃProfit_i)}{\sum weight_i}$

（$Profit_i$为分析师 i 的预测净利润，$weight_i$为分析师 i 的权重）。

### 步骤 6：最终一致预期净利润合成

结合时间加权与分析师加权，最终公式为：

$Profit\_FY = \begin{cases} 
ä¸ç»©æ¥ååå©æ¶¦ & å­å¨ææä¸ç»©æ¥å \\
\frac{4ÃProfit\_analyst_t + 2ÃProfit\_analyst_{t-1} + 1ÃProfit\_analyst_{t-2}}{7} & æ ä¸ç»©æ¥å 
\end{cases}$

（Profit\_analyst\_t 为 t 月撰写报告的分析师加权均值，以此类推）。

### 步骤 7：滚动一致预期净利润计算（Profit\_ROLL，研报 5 章）

为解决 “年报未公布时 FY1 预测失效” 问题，构建滚动 12 个月的一致预期净利润：



1.  **确定年度区间**：设预测日 t 为 S1 年 m 月（m 为 1-12），则滚动区间为 “S1 年 m 月 - S2 年 m 月”；

2.  **分阶段计算**：

*   S1 年剩余月份净利润：$Profit_{S1\_rest} = Profit_{FY1} Ã (1 - \frac{m}{12})$（Profit\_FY1 为 S1 年一致预期净利润）；

*   S2 年对应月份净利润：$Profit_{S2\_part} = Profit_{FY2} Ã \frac{m}{12}$（Profit\_FY2 为 S2 年一致预期净利润）；

1.  **滚动净利润合成**：

$Profit\_ROLL = Profit_{S1\_rest} + Profit_{S2\_part}$

（参考研报图 23，若 S1 年年报未公布，Profit\_FY1 替换为 Profit\_FY2，Profit\_FY2 替换为 Profit\_FY3）。

## 四、核心环节 3：一致预期衍生指标构建（对应研报 6 章）

基于一致预期净利润（Profit\_FY/Profit\_ROLL），构建研报中的五大类衍生指标，需严格遵循公式与中性化处理。

### 步骤 1：一致预期估值 EP\_FY 与 EP\_ROLL



*   **EP\_FY（年度估值）**：$EP_{FY1} = \frac{Profit_{FY1}}{TOT\_MV}$（Profit\_FY1 为下一年一致预期净利润，TOT\_MV 为预测日市值）；

*   **EP\_ROLL（滚动估值）**：$EP_{ROLL} = \frac{Profit_{ROLL}}{TOT\_MV}$（Profit\_ROLL 为滚动 12 个月一致预期净利润）；

*   **中性化处理**：对 EP\_FY1 进行 “行业 + 市值 + EP\_TTM” 正交（剔除当前估值影响），回归公式为：

$EP_{FY1} = \sum_{i=1}^{29}\alpha_iÃIndustry_i + \beta_1ÃSize + \beta_2ÃEP_{TTM} + \varepsilon$

取残差$\varepsilon$作为提纯因子 EP\_PURE（参考研报表 5，提纯后稳定性提升）。

### 步骤 2：一致预期净利润增速 Growth\_FY

需修正 “微利股虚假高增速” 问题，用回归法计算两年复合增速：



1.  **基础数据准备**：取 “上一年实际净利润（Profit\_FY0）、当年一致预期（Profit\_FY1）、下一年一致预期（Profit\_FY2）”；

2.  **回归拟合增速**：

*   拟合线性方程$y = bÃx + (y_1 - b)$（约束过点 (1, Profit\_FY0)），最小化残差平方和后解得：

$b = 0.4Ã(Profit_{FY2} - Profit_{FY1}) + 0.6Ã(Profit_{FY1} - Profit_{FY0})$



*   计算复合增速：$Growth_{FY} = \frac{b}{\frac{1}{3}(|Profit_{FY0}| + |Profit_{FY1}| + |Profit_{FY2}|)}$；

1.  **修正处理**：对 Growth\_FY 进行 “行业 + 市值 + log (abs (Profit\_FY0))” 回归，取残差作为 Growth\_Mod（剔除历史净利润影响，参考研报表 7，修正后 IC 均值从 0.016 提升至 0.049）。

### 步骤 3：一致预期 PEG（估值增速比）

以 EP\_ROLL 与 Growth\_FY 的乘积为代理变量（避免 PE 为负的问题）：

$PEG = EP_{ROLL} Ã Growth_{FY}$

（研报逻辑：PEG 越小，估值相对于增速越合理；代理变量越大，对应 PEG 越小，选股时取代理变量高分组）。

### 步骤 4：一致预期估值变化 DEP

衡量 “预期盈利变化与股价变化的博弈”，公式为：

$DEP_t = \frac{EP_{FY1,t}}{EP_{FY1,t-1}} - 1$

（EP\_FY1,t 为 t 月一致预期估值，EP\_FY1,t-1 为 t-1 月估值）；



*   **提纯处理**：对 DEP 进行 “行业 + 市值 + 反转因子” 回归（剔除股价反转影响），取残差作为 DEP\_PURE（参考研报表 10，提纯后仍有 9.1% 年化多空收益）。

### 步骤 5：一致预期估值分位点 EP\_PER

衡量个股估值在自身历史序列中的位置，步骤如下：



1.  **历史数据筛选**：取 “股票过去 12 个月的 EP\_ROLL 数据”（每月 1 个值，共 12 个观测）；

2.  **分位点计算**：

*   若当前 EP\_ROLL < 历史最小值：$percentile = \frac{EP_{ROLL} - min(EP)}{|min(EP)|}$（<0）；

*   若当前 EP\_ROLL 在历史区间内：$percentile = \frac{EP_{ROLL} - EP_m}{EP_{m+1} - EP_m}Ã\frac{1}{13} + \frac{m}{13}$（m 为历史排序后的位置，1≤m≤11）；

*   若当前 EP\_ROLL > 历史最大值：$percentile = 1 + \frac{EP_{ROLL} - max(EP)}{|max(EP)|}$（>1）；


3.  **区间映射**：将 <0 的分位点线性映射到 \[0, 1/13]，>1 的分位点线性映射到 \[12/13, 1]，确保最终分位点落在 \[0,1] 区间内；

*   例如：若过去 12 个月 EP\_ROLL 最小值为 0.05、最大值为 0.15，当前 EP\_ROLL=0.2（> 最大值），则先计算初始 percentile=1+(0.2-0.15)/0.15≈1.33，再将其映射到 \[12/13,1]（约 0.923-1），最终分位点 = 0.923+(1.33-1)×(1-0.923)/(max\_initial\_percentile-1)（max\_initial\_percentile 为所有 > 1 样本的初始分位点最大值）；

4.  **中性化处理**：对最终分位点进行 “行业 + 市值” 中性化，得到 EP\_PER 因子（参考研报表 11，该因子全样本期 IC 均值达 0.067，年化多空收益 23.5%）。

## 五、核心环节 4：缺失值填充（对应研报 7 章）

研报指出 “传统行业中位数填充逻辑不足”，需基于 “增速分位点” 优先填充一致预期净利润，再推导衍生指标缺失值。

### 步骤 1：识别缺失值类型

先统计一致预期数据的缺失场景，主要包括：



*   **场景 1**：一致预期净利润（Profit\_FY1/Profit\_ROLL）缺失（因分析师覆盖不足，研报显示 2014 年最低覆盖度仍超 75%）；

*   **场景 2**：衍生指标（如 Growth\_FY、PEG）缺失（因净利润缺失或计算所需的其他数据缺失）；

    优先处理 “场景 1”，再通过填充后的净利润推导 “场景 2” 的指标值。

### 步骤 2：传统填充方法（行业中位数填充）

作为基准方法，用于与研报创新方法对比：



1.  **行业分组**：按 “中信一级行业” 分组（与前文一致）；

2.  **计算中位数**：对每组内 “非缺失的一致预期净利润（Profit\_FY1）” 计算中位数；

3.  **填充缺失值**：将同行业中位数赋值给该行业内净利润缺失的股票；

4.  **衍生指标计算**：用填充后的净利润重新计算 Growth\_FY、PEG 等衍生指标（参考研报表 12，该方法填充后 EP\_ROLL 年化收益从 19.7% 降至 17.7%）。

### 步骤 3：研报创新方法（增速分位点填充）

基于 “企业历史增速分位点的行业共性” 填充，逻辑更贴合盈利增长规律：



1.  **计算历史增速分位点**：

*   对 “一致预期净利润缺失的股票”，取其 “上一年实际净利润增速”（增速 =（上一年实际净利润 - 前两年实际净利润）/| 前两年实际净利润 |）；

*   按 “行业” 分组，计算该股票上一年增速在行业内的分位点（即 point\_last，如某股票上一年增速在行业内排前 30%，则 point\_last=0.3）；

1.  **估计当年增速分位点**：

*   对每个行业，计算 “所有净利润缺失股票的 point\_last 均值”（即 point\_forecast=Mean (point\_last)）；

*   将该均值作为缺失股票 “当年增速分位点” 的估计值；

1.  **推导一致预期净利润**：

*   按 “当年增速分位点” 在行业内匹配对应的 “增速区间”（如分位点 0.3 对应行业内 30% 分位的增速值，假设为 15%）；

*   计算填充后的净利润：Profit\_FY1\_填充 = 上一年实际净利润 ×(1 + 估计增速)；

1.  **衍生指标填充**：用填充后的 Profit\_FY1 计算 Growth\_FY、PEG 等指标（参考研报表 12，该方法填充后 EP\_ROLL 的 IC-IR 从 2.30 提升至 2.73，稳定性更优）。

### 步骤 4：填充效果验证

对比两种方法的填充效果，需验证以下指标（与研报逻辑一致）：



*   **覆盖度**：填充后数据覆盖度需从原始的 75%-80% 提升至 100%（行业中位数填充）或 89% 以上（增速分位点填充，研报 EP\_PER 填充后覆盖度 89.3%）；

*   **因子绩效**：通过 IC 均值、IC-IR、年化多空收益判断填充后因子的选股能力是否显著下降（研报结论：两种方法均未导致绩效大幅下滑，增速分位点填充的 IC 更优）。

## 六、核心环节 5：因子验证与回测（对应研报 8-9 章）

复刻研报的因子有效性验证，需从 “不同样本空间稳定性”“因子相关性” 两个维度展开，确保指标具备实际投资价值。

### 步骤 1：不同样本空间回测

验证因子在大盘股、中小盘股中的普适性，样本空间划分参考研报：



| 样本空间    | 成分股范围（研报定义）             | 回测步骤                                                                                                                 |
| ------- | ----------------------- | -------------------------------------------------------------------------------------------------------------------- |
| 沪深 300  | 沪深两市市值最大的 300 只股票       | 1. 每个样本空间内单独计算五大类衍生指标；2. 对指标进行 “行业 + 市值” 中性化（增速指标额外剔除净利润影响）；3. 按月调仓，构建 “因子值前 10%（多头）- 后 10%（空头）” 组合，计算年化收益、IC、IC-IR； |
| 中证 500  | 沪深 300 之后市值最大的 500 只股票  | 同上                                                                                                                   |
| 中证 1000 | 中证 500 之后市值最大的 1000 只股票 | 同上                                                                                                                   |
| 创业板综指   | 创业板所有上市公司               | 同上                                                                                                                   |



*   **关键验证结论**（需与研报一致）：

1.  EP\_ROLL 在沪深 300（IC=0.070）和中证 1000（IC=0.075）中表现更优；

2.  Growth\_FY、PEG 在中证 1000（年化收益 22.4%）和创业板（年化收益 21.0%）中表现更优（小票更注重成长）；

3.  DEP、EP\_PER 在所有样本空间 IC 均超 0.05，稳定性最强（参考研报表 13）。

### 步骤 2：因子相关性分析

验证指标间的独立性，确保能为多因子模型提供增量信息：



1.  **计算截面相关性**：

*   按 “月度” 计算所有衍生指标（EP\_ROLL、Growth\_FY、PEG、DEP、EP\_PER）的截面相关系数；

*   计算 2010-2018 年（研报回溯区间）的平均截面相关性；

1.  **关键结论验证**（需与研报一致，参考研报图 43-44）：

*   EP\_ROLL 与 Growth\_FY 相关性低（平均相关系数 0.01），独立性强；

*   PEG 与 EP\_ROLL（0.63）、Growth\_FY（0.67）相关性较高（因 PEG 由二者衍生）；

*   DEP 与 EP\_PER 相关性为 0.42，需注意在多因子模型中避免重复配置；

1.  **中性化后相关性**：对所有指标进行 “行业 + 市值” 中性化后，相关性略有上升（如 EP\_ROLL 与 Growth\_FY 升至 0.29），但仍处于低相关水平，可同时纳入模型。

## 七、最终输出：研报核心结论复刻

完成所有数据处理后，需整理与研报一致的核心结论，验证复刻效果：



1.  **一致预期净利润加权效果**：研报加权方法（时间 + 分析师 + 业绩报告）相较于简单平均，预测偏离度降低 20%-25%（尤其在预测时间跨度较小时更显著）；

2.  **衍生指标绩效排序**：全样本期年化多空收益从高到低为：EP\_PER（23.5%）>DEP（22.7%）>EP\_ROLL（19.7%）>PEG（17.7%）>Growth\_FY（16.1%）；

3.  **缺失值填充价值**：增速分位点填充虽在年化收益上略低于行业中位数填充，但 IC-IR 更高（如 EP\_ROLL 从 1.76 升至 2.16），因子稳定性更优；

4.  **实际应用建议**：中小盘策略优先配置 Growth\_FY、PEG；大盘策略优先配置 EP\_ROLL；全市场策略可同时纳入 DEP、EP\_PER，通过相关性控制避免信息冗余。

## 八、关键注意事项（避免复刻偏差）



1.  **数据时效性匹配**：研报中 “报告录入时间与撰写时间差（delta）” 需匹配（2016-2017 年 84.8% 报告 2 天内录入），若你的数据 delta 分布差异较大，需调整有效报告的时间筛选范围；

2.  **行业分类一致性**：全程使用 “中信一级行业”（共 29 个），避免因行业分类不同（如申万行业）导致偏离度分析、相关性计算偏差；

3.  **极端值处理**：计算偏离度、增速时，需用 “绝对值分母”（如研报 bias 公式），避免净利润为负时出现正负偏差抵消；

4.  **回溯区间对齐**：若你的数据时间跨度不足（如无 2010-2018 年数据），需在结论中注明 “回溯区间缩短对绩效的影响”，避免与研报结论直接冲突。