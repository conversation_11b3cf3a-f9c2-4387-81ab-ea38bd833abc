# 天风证券研报章节与代码文件对应表

## 📋 研报信息
- **标题**: 基于基础数据的分析师一致预期指标构建
- **发布机构**: 天风证券
- **发布日期**: 2018年4月10日
- **研报文件**: `20180410-天风证券-天风证券基于基础数据的分析师一致预期指标构建（2018-04-10）.pdf`

## 🔗 章节与代码对应关系

### 第一章：研究背景与意义
**研报章节**: 1. 引言
**对应代码**: 无直接对应
**说明**: 理论背景，无需代码实现

---

### 第二章：数据来源与预处理

#### 2.1 数据来源与筛选
**研报章节**: 2.1 数据来源
**对应代码**: `01_数据准备与筛选.py`
**主要内容**:
- 从Wind数据库获取分析师预测数据
- 筛选条件：时间范围、预测类型、指标类型
- 数据质量初步检查

**核心实现**:
```python
# 时间筛选：2019年6月至2020年6月
df_filtered = df[
    (df['est_dt'] >= '2019-06-01') & 
    (df['est_dt'] <= '2020-06-30')
]

# 预测类型筛选：年报预测（rpt_type=4）
df_filtered = df_filtered[df_filtered['rpt_type'] == 4]

# 指标筛选：净利润（indicator_code=321001000）
df_filtered = df_filtered[df_filtered['indicator_code'] == 321001000]
```

#### 2.2 数据质量控制
**研报章节**: 2.2 数据清洗
**对应代码**: `02_预测偏离度分析.py`
**主要内容**:
- 计算预测偏离度指标
- 识别异常预测值（Z-score > 3）
- 移除明显错误的预测

**核心实现**:
```python
# 计算Z-score
df['z_score'] = (df['predicted_eps'] - df['consensus_mean']) / df['consensus_std']

# 标记异常值
df['is_outlier'] = np.abs(df['z_score']) > 3.0
```

---

### 第三章：一致预期计算方法

#### 3.1 时间加权一致预期
**研报章节**: 3.1 一致预期计算
**对应代码**: `03_一致预期净利润加权.ipynb`
**主要内容**:
- 实现时间加权的一致预期算法
- 处理多分析师预测的聚合
- 生成月度一致预期序列

**核心实现**:
```python
# 时间加权公式
weight = np.exp(-lambda_param * days_diff)
consensus_eps = np.sum(weight * prediction) / np.sum(weight)
```

#### 3.2 衍生指标构建
**研报章节**: 3.2 衍生指标
**对应代码**: `04_衍生指标构建_FIXED.ipynb`
**主要内容**:
- 构建EP_FY1（基于FY1预测的市盈率倒数）
- 构建EP_ROLL（滚动12个月预测的市盈率倒数）
- 计算市值加权版本

**核心实现**:
```python
# EP_FY1计算
df['EP_FY1'] = df['consensus_eps_fy1'] / df['market_value']

# EP_ROLL计算
df['EP_ROLL'] = df['consensus_eps_roll'] / df['market_value']
```

#### 3.3 数据完整性处理
**研报章节**: 3.3 缺失值处理
**对应代码**: `05_缺失值填充处理_简化版.ipynb`
**主要内容**:
- 行业中位数填充缺失值
- 数据质量验证
- 填充效果评估

**核心实现**:
```python
# 行业中位数填充
df['EP_FY1_filled'] = df.groupby('industry')['EP_FY1'].transform(
    lambda x: x.fillna(x.median())
)
```

---

### 第四章：因子有效性验证

#### 4.1 初步验证
**研报章节**: 4.1 因子相关性分析
**对应代码**: `06_因子验证与回测.ipynb`
**主要内容**:
- 因子间相关性分析
- 基础统计特征验证
- 预测模型初步构建

**核心实现**:
```python
# 相关性矩阵
correlation_matrix = df[factor_columns].corr()

# 预测模型
from sklearn.ensemble import RandomForestRegressor
model = RandomForestRegressor()
model.fit(X_train, y_train)
```

#### 4.2 完整回测分析
**研报章节**: 4.2 因子回测
**对应代码**: `07_因子回测分析_完整版.py`
**主要内容**:
- 使用真实复权价格计算收益率
- 因子分组回测分析
- IC分析和多空组合构建
- 生成完整的因子评估报告

**核心实现**:
```python
# 复权价格计算
df['adj_close'] = df['close'] * df['adj_factor']

# 收益率计算
df['return_5d'] = df.groupby('stock_code')['adj_close'].pct_change(5).shift(-5)

# 因子分组
df['factor_group'] = pd.qcut(df['EP_FY1'], 5, labels=False)

# IC计算
ic = df['EP_FY1'].corr(df['return_5d'], method='spearman')
```

---

## 📊 研报核心指标与代码实现对应

### 表格1：因子基础统计
**研报位置**: 第3章表格
**代码实现**: `04_衍生指标构建_FIXED.ipynb`
**输出文件**: `processed_data/derived_factors.csv`

### 表格2：因子相关性矩阵
**研报位置**: 第4章图表
**代码实现**: `06_因子验证与回测.ipynb`
**输出文件**: `processed_data/factor_correlation_matrix.csv`

### 表格3：因子回测结果
**研报位置**: 第4章核心表格
**代码实现**: `07_因子回测分析_完整版.py`
**输出文件**: `processed_data/factor_evaluation_summary.csv`

**关键指标对应**:
- **年化收益**: `annualized_return`
- **多空IR**: `long_short_ir`
- **IC**: `ic`
- **IC-IR**: `ic_ir`
- **IC胜率**: `ic_win_rate`

### 图表1：因子分组收益
**研报位置**: 第4章核心图表
**代码实现**: `07_因子回测分析_完整版.py`
**输出文件**: `processed_data/factor_group_returns_key.png`

### 图表2：IC时间序列
**研报位置**: 第4章分析图表
**代码实现**: `07_因子回测分析_完整版.py`
**输出文件**: `processed_data/factor_ic_comparison.png`

---

## 🔍 研报与实现的主要差异

### 1. 数据时间范围
- **研报**: 2010-2017年
- **实现**: 2019年6月-2020年6月
- **原因**: 数据可获得性限制

### 2. 股票池范围
- **研报**: 全A股
- **实现**: 有预测数据的股票
- **原因**: 预测数据覆盖范围限制

### 3. 复权处理
- **研报**: 未明确说明
- **实现**: 使用真实复权因子
- **优势**: 更准确的收益率计算

### 4. 回测频率
- **研报**: 月度调仓
- **实现**: 基于预测日期的动态调仓
- **原因**: 数据结构差异

---

## ✅ 已完成的研报内容

### 核心算法实现
- ✅ 时间加权一致预期计算
- ✅ EP_FY1和EP_ROLL因子构建
- ✅ 因子分组回测分析
- ✅ IC分析和统计检验

### 数据处理流程
- ✅ 原始数据筛选和清洗
- ✅ 异常值检测和处理
- ✅ 缺失值填充策略
- ✅ 数据质量控制

### 分析结果输出
- ✅ 因子评估表格
- ✅ 分组收益可视化
- ✅ IC分析图表
- ✅ 详细的统计报告

---

## ⚠️ 需要真实收益率但未完成的内容

### 1. 长期回测验证
**研报要求**: 3-5年长期回测
**当前状态**: 仅1年数据
**缺失原因**: 历史数据时间范围限制
**影响**: 无法验证因子长期稳定性

### 2. 行业中性化分析
**研报要求**: 行业中性化因子表现
**当前状态**: 基础行业分析
**缺失原因**: 行业分类数据不完整
**影响**: 无法排除行业因素影响

### 3. 市场状态分析
**研报要求**: 不同市场环境下的因子表现
**当前状态**: 单一时期分析
**缺失原因**: 缺少市场状态标识数据
**影响**: 无法分析因子的市场适应性

### 4. 交易成本考虑
**研报要求**: 考虑交易成本的净收益分析
**当前状态**: 理论收益分析
**缺失原因**: 缺少流动性和交易成本数据
**影响**: 实际可操作性评估不足

### 5. 风险调整分析
**研报要求**: 多维度风险调整收益
**当前状态**: 基础风险指标
**缺失原因**: 缺少完整的风险因子数据
**影响**: 风险评估不够全面

---

## 📝 使用建议

### 1. 按顺序运行
严格按照文件编号顺序执行，确保数据依赖关系正确

### 2. 检查输出
每个步骤完成后检查输出文件，确保数据质量

### 3. 环境配置
确保Python环境和必要包的版本兼容性

### 4. 数据路径
根据实际情况调整数据文件路径

### 5. 参数调整
可根据需要调整关键参数（如时间权重λ、分组数量等）

---

**最后更新**: 2024年12月
**对应研报**: 天风证券-基于基础数据的分析师一致预期指标构建-20180410
