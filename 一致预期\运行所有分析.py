#!/usr/bin/env python3
"""
运行所有分析脚本

按正确顺序执行所有分析步骤，生成完整的研究报告
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def run_script(script_name, description):
    """运行单个脚本"""
    print(f"\n{'='*60}")
    print(f"开始执行: {description}")
    print(f"脚本文件: {script_name}")
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # 检查文件是否存在
        if not os.path.exists(script_name):
            print(f"❌ 错误: 文件 {script_name} 不存在")
            return False
        
        # 运行脚本
        if script_name.endswith('.py'):
            result = subprocess.run([sys.executable, script_name], 
                                  capture_output=True, text=True, encoding='utf-8')
        else:
            print(f"⚠️  跳过非Python文件: {script_name}")
            return True
        
        end_time = time.time()
        duration = end_time - start_time
        
        if result.returncode == 0:
            print(f"✅ 成功完成: {description}")
            print(f"执行时间: {duration:.2f} 秒")
            if result.stdout:
                print("输出:")
                print(result.stdout[-1000:])  # 只显示最后1000个字符
        else:
            print(f"❌ 执行失败: {description}")
            print(f"错误代码: {result.returncode}")
            if result.stderr:
                print("错误信息:")
                print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 执行异常: {e}")
        return False
    
    return True

def check_environment():
    """检查运行环境"""
    print("检查运行环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查必要的包
    required_packages = ['pandas', 'numpy', 'matplotlib', 'seaborn']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"❌ {package} 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n缺少必要包: {', '.join(missing_packages)}")
        print("请先安装缺少的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    # 检查数据文件
    data_files = [
        'data/rpt_forecast_stk.feather',
        'data/daily0925.h5',
        'data/adjfactor.hd5'
    ]
    
    missing_files = []
    for file_path in data_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path} 存在")
        else:
            print(f"❌ {file_path} 不存在")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺少数据文件: {missing_files}")
        print("请确保数据文件在正确位置")
        return False
    
    return True

def create_output_directory():
    """创建输出目录"""
    if not os.path.exists('processed_data'):
        os.makedirs('processed_data')
        print("✅ 创建输出目录: processed_data")
    else:
        print("✅ 输出目录已存在: processed_data")

def main():
    """主函数"""
    print("基于基础数据的分析师一致预期指标构建")
    print("=" * 60)
    print(f"开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查环境
    if not check_environment():
        print("\n❌ 环境检查失败，请解决上述问题后重试")
        return
    
    # 创建输出目录
    create_output_directory()
    
    # 定义执行步骤
    steps = [
        ("01_数据准备与筛选.py", "数据准备与筛选"),
        ("02_预测偏离度分析.py", "预测偏离度分析"),
        # 注意：03-06是Jupyter notebook文件，需要手动运行
        # ("07_因子回测分析_完整版.py", "因子回测分析"),  # 暂时跳过，因为有环境问题
    ]
    
    # 执行步骤
    total_start_time = time.time()
    success_count = 0
    
    for script_name, description in steps:
        if run_script(script_name, description):
            success_count += 1
        else:
            print(f"\n❌ 步骤失败: {description}")
            print("是否继续执行后续步骤？(y/n): ", end="")
            choice = input().lower()
            if choice != 'y':
                break
    
    total_end_time = time.time()
    total_duration = total_end_time - total_start_time
    
    # 总结
    print(f"\n{'='*60}")
    print("执行总结")
    print(f"{'='*60}")
    print(f"总执行时间: {total_duration:.2f} 秒")
    print(f"成功步骤: {success_count}/{len(steps)}")
    print(f"完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success_count == len(steps):
        print("\n🎉 所有步骤执行成功！")
        print("\n📋 后续手动步骤:")
        print("1. 运行 03_一致预期净利润加权.ipynb")
        print("2. 运行 04_衍生指标构建_FIXED.ipynb")
        print("3. 运行 05_缺失值填充处理_简化版.ipynb")
        print("4. 运行 06_因子验证与回测.ipynb")
        print("5. 运行 07_因子回测分析_完整版.py (需要解决环境问题)")
    else:
        print(f"\n⚠️  部分步骤执行失败，请检查错误信息")
    
    print(f"\n📁 输出文件位置: processed_data/")
    print(f"📖 详细说明请参考: 项目总结与步骤说明.md")

if __name__ == "__main__":
    main()
