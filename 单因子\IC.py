import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import statsmodels.api as sm
from scipy import stats
plt.rcParams.update({
    "font.size": 20,
    "mathtext.fontset": "cm",
    "font.sans-serif": ["SimHei"],
    "axes.unicode_minus": False,
    "figure.dpi": 200
})

# 数据加载与预处理
data = pd.read_hdf(r"C:\Users\<USER>\Desktop\金元顺安\单因子\data\merged_data2.h5", parse_dates=['trade_date'])
data = data.sort_values(by=['trade_date', 'ts_code']).reset_index(drop=True)


# 因子IC计算
def calculate_ic(data, factor_col):
    ic_list = []
    dates = data['trade_date'].unique()

    for date in dates:
        df = data[data['trade_date'] == date].copy()
        if len(df) < 30:
            continue  # 过滤样本量不足的截面

        # 计算 Spearman 秩相关系数（IC）
        ic, p_value = stats.spearmanr(df[factor_col], df['return_t1'])
        ic_list.append({
            'trade_date': date,
            'ic': ic,
            'p_value': p_value
        })

    return pd.DataFrame(ic_list)


# 计算 PB 和 total_mv 的 IC 序列
pb_ic = calculate_ic(data, 'pb')
mv_ic = calculate_ic(data, 'total_mv')


# 3. IC 评价指标计算
def evaluate_ic(ic_df, factor_name):
    ic_values = ic_df['ic'].dropna()

    ic_mean = ic_values.mean()
    ic_std = ic_values.std()
    ir_ratio = ic_mean / ic_std if ic_std != 0 else 0
    ic_positive_ratio = (ic_values > 0).mean()
    ic_abs_gt02_ratio = (np.abs(ic_values) > 0.02).mean()

    return {
        '因子': factor_name,
        'IC序列均值': ic_mean,
        'IC序列标准差': ic_std,
        'IR比率': ir_ratio,
        'IC>0占比': ic_positive_ratio,
        '|IC|>0.02占比': ic_abs_gt02_ratio
    }


pb_eval_ic = evaluate_ic(pb_ic, 'PB')
mv_eval_ic = evaluate_ic(mv_ic, 'total_mv')
ic_eval_table = pd.DataFrame([pb_eval_ic, mv_eval_ic])


# IC 累积曲线
def plot_ic_cumulative(ic_df, factor_name, ax):
    """
    绘制 IC 累积曲线：
    - 按日期累加 IC 值（类似累计收益率逻辑）
    """
    ic_df = ic_df.sort_values('trade_date').reset_index(drop=True)
    # 计算累积 IC
    ic_df['cum_ic'] = ic_df['ic'].cumsum()
    ax.plot(ic_df['trade_date'], ic_df['cum_ic'], label=factor_name, linewidth=2)
    return ax


plt.figure(figsize=(12, 6))
ax = plt.gca()
ax = plot_ic_cumulative(pb_ic, 'PB', ax)
ax = plot_ic_cumulative(mv_ic, 'total_mv', ax)

plt.title('因子IC累积曲线', fontweight='bold')
plt.xlabel('日期')
plt.ylabel('累积IC')
plt.axhline(y=0, color='gray', linestyle='--', alpha=0.5)
plt.legend()
plt.grid(alpha=0.3)
plt.tight_layout()
plt.show()

print("因子IC分析结果：")
print(ic_eval_table)