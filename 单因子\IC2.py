import pandas as pd
import numpy as np
from scipy.stats import spearmanr
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import h5py

# 设置中文显示
plt.rcParams["font.family"] = ["SimHei", "WenQuanYi Micro Hei", "Heiti TC"]
plt.rcParams['axes.unicode_minus'] = False


# 读取H5文件中的所有字段名（用于确认日期字段）
def get_all_fields(file_path):
    with h5py.File(file_path, 'r') as f:
        data = f['data']  # 数据集键名
        return list(data.keys())  # 返回所有字段名称


# 读取日期列表（使用正确的日期字段名）
def get_dates(file_path, date_field):
    with h5py.File(file_path, 'r') as f:
        data = f['data']
        dates = pd.unique(pd.to_datetime(data[date_field][:]))  # 使用实际日期字段
    return dates


# 按日期分批计算IC值（遵循报告逻辑：因子与下期收益的秩相关）
def calculate_ic(file_path, factor_name, date_field):
    dates = get_dates(file_path, date_field)
    ic_values = []
    valid_dates = []

    with h5py.File(file_path, 'r') as f:
        data = f['data']
        all_dates = pd.to_datetime(data[date_field][:])  # 全量日期数据

        for date in dates:
            # 筛选单日期数据（报告中的截面期逻辑）
            mask = (all_dates == date)
            factor_values = data[factor_name][mask]  # 因子暴露度
            returns = data['return_t1'][mask]  # 下期收益率（T+1期）

            # 过滤无效值（参考报告样本量要求）
            valid_mask = ~np.isnan(factor_values) & ~np.isnan(returns)
            if np.sum(valid_mask) >= 30:  # 确保样本量足够
                # 计算Spearman相关系数（报告定义的IC值）
                ic, _ = spearmanr(factor_values[valid_mask], returns[valid_mask])
                ic_values.append(ic)
                valid_dates.append(date)

    return pd.DataFrame({'trade_date': valid_dates, f'{factor_name}_ic': ic_values})


# 计算报告要求的IC统计指标
def analyze_ic(ic_df, factor_name):
    ic_col = f'{factor_name}_ic'
    ic_mean = ic_df[ic_col].mean()  # IC序列均值
    ic_std = ic_df[ic_col].std()  # IC序列标准差
    ir_ratio = abs(ic_mean / ic_std) if ic_std != 0 else 0  # IR比率
    ic_pos_ratio = (ic_df[ic_col] > 0).mean()  # IC>0占比
    ic_abs_ratio = (abs(ic_df[ic_col]) > 0.02).mean()  # |IC|>0.02占比

    print(f"\n{factor_name} IC分析结果（参照报告标准）：")
    print(f"IC序列均值：{ic_mean:.4f}")
    print(f"IC序列标准差：{ic_std:.4f}")
    print(f"IR比率：{ir_ratio:.4f}")
    print(f"IC>0占比：{ic_pos_ratio:.4f}")
    print(f"|IC|>0.02占比：{ic_abs_ratio:.4f}")
    return ic_df


# 绘制IC累积曲线（复刻报告图表117-118）
def plot_cumulative_ic(ic_df, factor_name):
    ic_df = ic_df.sort_values('trade_date')
    ic_df['cumulative_ic'] = ic_df[f'{factor_name}_ic'].cumsum()  # 累积IC

    plt.figure(figsize=(10, 6))
    plt.plot(ic_df['trade_date'], ic_df['cumulative_ic'], label=factor_name)
    plt.title(f'{factor_name} IC值累积曲线')
    plt.xlabel('日期')
    plt.ylabel('累积IC值')
    plt.grid(linestyle='--', alpha=0.7)
    plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    plt.gcf().autofmt_xdate()
    plt.legend()
    plt.show()


# 主函数
def main():
    file_path = r"C:\Users\<USER>\Desktop\金元顺安\单因子\data\merged_data2.h5"

    # 步骤1：先获取数据中的所有字段，确认实际日期字段名称
    fields = get_all_fields(file_path)
    print(f"数据中所有字段：{fields}")  # 查看输出，找到日期字段（如'date'或'trade_dt'）

    # 步骤2：手动指定实际日期字段（根据上一步输出修改，例如'date'）
    date_field = '请替换为实际日期字段名'  # 例如：若字段为'date'则改为'date'

    # 步骤3：分析因子（可替换为报告中的动量因子）
    factors = ['pb', 'total_mv']
    for factor in factors:
        ic_df = calculate_ic(file_path, factor, date_field)
        ic_df = analyze_ic(ic_df, factor)
        plot_cumulative_ic(ic_df, factor)


if __name__ == "__main__":
    main()