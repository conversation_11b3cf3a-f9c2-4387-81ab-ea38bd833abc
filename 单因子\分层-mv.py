import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import statsmodels.api as sm
from scipy import stats
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')
plt.rcParams.update({
    "font.size": 20,
    "mathtext.fontset": "cm",
    "font.sans-serif": ["SimHei"],
    "axes.unicode_minus": False,
    "figure.dpi": 200
})


# ----------------------
# 1. 数据加载与基础处理
# ----------------------
def load_data(file_path):
    """加载HDF数据并进行基础清洗"""
    data = pd.read_hdf(file_path, parse_dates=['trade_date'])
    # 按日期和股票代码排序
    data = data.sort_values(by=['trade_date', 'ts_code']).reset_index(drop=True)
    # 剔除关键列缺失值
    data = data.dropna(subset=['trade_date', 'ts_code', 'pb', 'total_mv', 'return_t1'])
    print(f"数据加载完成，共 {len(data)} 条记录，时间范围：{data['trade_date'].min()} 至 {data['trade_date'].max()}")
    return data


# 加载数据（替换为你的文件路径）
data = load_data(r"C:\Users\<USER>\Desktop\金元顺安\单因子\data\merged_data3.h5")


# ----------------------
# 2. 因子预处理（原始因子去极值，保留原始分布特征）
# ----------------------
def winsorize_by_date(group, factor_col, n=5):
    """按日期截面去极值（中位数±5倍MAD法）"""
    med = group[factor_col].median()
    mad = (group[factor_col] - med).abs().median()
    lower = med - n * mad
    upper = med + n * mad
    return group[factor_col].clip(lower, upper)


# 对pb和total_mv按日期去极值（避免极端值干扰分层）
data['pb_winsor'] = data.groupby('trade_date', group_keys=False).apply(
    lambda x: winsorize_by_date(x, 'pb')
)
data['total_mv_winsor'] = data.groupby('trade_date', group_keys=False).apply(
    lambda x: winsorize_by_date(x, 'total_mv')
)

# 可选：对市值做对数转换（改善右偏分布）
data['log_total_mv_winsor'] = np.log(data['total_mv_winsor'])  # 对数市值因子


# ----------------------
# 3. 分层回测核心函数
# ----------------------
def quantile_backtest(data, factor_col, n_quantiles=8):
    """
    对指定因子进行分层回测
    参数：
    - data: 包含因子和下期收益的DataFrame
    - factor_col: 因子列名（需预处理后的因子）
    - n_quantiles: 分层数量
    返回：
    - 各层收益时间序列（index=日期，columns=分层）
    """
    results = []
    dates = data['trade_date'].unique()  # 所有交易日

    for date in dates:
        # 筛选当日数据，剔除因子或收益缺失的样本
        df = data[data['trade_date'] == date][['ts_code', factor_col, 'return_t1']].dropna()
        if len(df) < n_quantiles * 2:  # 确保每层至少有足够样本
            continue

        # 按因子值升序分层（1=最低因子值，n_quantiles=最高因子值）
        # 处理因子值重复导致的分层失败问题（duplicates='drop'）
        df['quantile'] = pd.qcut(df[factor_col], n_quantiles, labels=False, duplicates='drop') + 1

        # 计算每层平均收益
        for q in range(1, n_quantiles + 1):
            quantile_return = df[df['quantile'] == q]['return_t1'].mean()
            results.append({
                'trade_date': date,
                'quantile': q,
                'return': quantile_return
            })

    # 转换为时间序列格式（日期×分层）
    result_df = pd.DataFrame(results).pivot_table(
        index='trade_date', columns='quantile', values='return'
    )
    return result_df


# ----------------------
# 4. 执行回测（PB、原始市值、对数市值因子）
# ----------------------
# PB因子回测（使用去极值后的pb_winsor）
pb_quantile = quantile_backtest(data, 'pb_winsor', n_quantiles=8)
# 原始市值因子回测（去极值后的total_mv_winsor）
mv_quantile = quantile_backtest(data, 'total_mv_winsor', n_quantiles=8)
# 对数市值因子回测（可选）
log_mv_quantile = quantile_backtest(data, 'log_total_mv_winsor', n_quantiles=8)


# ----------------------
# 5. 组合指标计算函数
# ----------------------
def calculate_metrics(return_series, risk_free_rate=0.02):
    """计算组合年化收益、夏普比率、最大回撤等核心指标"""
    # 年化收益率（假设收益为月度数据）
    annual_return = (1 + return_series.mean()) ** 12 - 1

    # 夏普比率（月度标准差年化）
    monthly_std = return_series.std()
    sharpe = (return_series.mean() - risk_free_rate / 12) / monthly_std * np.sqrt(12)

    # 最大回撤
    cum_return = (1 + return_series).cumprod()
    rolling_max = cum_return.cummax()
    drawdown = (cum_return - rolling_max) / rolling_max
    max_drawdown = drawdown.min()

    # 月胜率
    win_rate = (return_series > 0).mean()

    return {
        '年化收益率': annual_return,
        '夏普比率': sharpe,
        '最大回撤': max_drawdown,
        '月胜率': win_rate
    }


def get_strategy_summary(quantile_df, factor_name):
    """汇总TOP组合、BOTTOM组合和多空组合指标"""
    top_return = quantile_df[quantile_df.columns[-1]]  # 最后一列是最高因子值组合
    bottom_return = quantile_df[quantile_df.columns[0]]  # 第一列是最低因子值组合
    long_short_return = top_return - bottom_return  # 多空组合（TOP - BOTTOM）

    # 计算各组合指标
    top_metrics = calculate_metrics(top_return)
    bottom_metrics = calculate_metrics(bottom_return)
    ls_metrics = calculate_metrics(long_short_return, risk_free_rate=0)  # 多空无风险利率

    # 单调性得分（Spearman秩相关：分层与收益的相关性）
    quantile_returns = [quantile_df[q].mean() for q in quantile_df.columns]
    monotonicity, _ = stats.spearmanr(range(1, len(quantile_returns) + 1), quantile_returns)

    return {
        '因子名称': factor_name,
        'TOP组合年化收益率': top_metrics['年化收益率'],
        'TOP组合夏普比率': top_metrics['夏普比率'],
        'TOP组合最大回撤': top_metrics['最大回撤'],
        'TOP组合月胜率': top_metrics['月胜率'],
        'BOTTOM组合年化收益率': bottom_metrics['年化收益率'],
        '多空组合年化收益率': ls_metrics['年化收益率'],
        '多空组合夏普比率': ls_metrics['夏普比率'],
        '多空组合最大回撤': ls_metrics['最大回撤'],
        '单调性得分': monotonicity
    }


# ----------------------
# 6. 生成回测结果与可视化
# ----------------------
# 计算各因子策略指标
pb_summary = get_strategy_summary(pb_quantile, 'PB（去极值）')
mv_summary = get_strategy_summary(mv_quantile, '市值（去极值）')
log_mv_summary = get_strategy_summary(log_mv_quantile, '对数市值（去极值）')

# 汇总为对比表格
summary_table = pd.DataFrame([pb_summary, mv_summary, log_mv_summary]).round(4)
print("\n分层回测指标汇总：")
print(summary_table)


# 可视化分层累计收益曲线
def plot_cumulative_returns(quantile_df, factor_name):
    """绘制各分层累计收益曲线"""
    cum_return = (1 + quantile_df).cumprod() - 1  # 累计收益率（从0开始）

    plt.figure(figsize=(12, 6))
    # 绘制TOP和BOTTOM组合
    plt.plot(cum_return.index, cum_return.iloc[:, -1], label=f'{factor_name}_TOP（第{cum_return.shape[1]}层）',
             color='red')
    plt.plot(cum_return.index, cum_return.iloc[:, 0], label=f'{factor_name}_BOTTOM（第1层）', color='blue')
    # 绘制多空组合
    plt.plot(cum_return.index, (cum_return.iloc[:, -1] - cum_return.iloc[:, 0]),
             label=f'{factor_name}_多空组合', color='green')

    plt.title(f'{factor_name}分层累计收益曲线', fontsize=14)
    plt.xlabel('日期', fontsize=12)
    plt.ylabel('累计收益率', fontsize=12)
    plt.legend()
    plt.grid(alpha=0.3)
    plt.tight_layout()
    plt.show()


# 绘制各因子收益曲线
plot_cumulative_returns(pb_quantile, 'PB（去极值）')
plot_cumulative_returns(mv_quantile, '市值（去极值）')
plot_cumulative_returns(log_mv_quantile, '对数市值（去极值）')