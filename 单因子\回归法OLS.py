import pandas as pd
import numpy as np
import h5py
from datetime import datetime, timedelta
import time
from scipy.stats import rankdata, ttest_ind
import statsmodels.api as sm
from scipy import stats
from statsmodels.regression.rolling import RollingOLS
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import warnings
warnings.filterwarnings('ignore')
plt.rcParams.update({
    "font.size": 20,
    "mathtext.fontset": "cm",
    "font.sans-serif": ["SimHei"],
    "axes.unicode_minus": False,
    "figure.dpi": 200
})
data = pd.read_hdf(r"C:\Users\<USER>\Desktop\金元顺安\单因子\data\merged_data2.h5", 'data')


def ols_factor_test(data, factor_col):
    # 提取所有截面日期
    dates = data['trade_date'].unique()
    result_list = []

    for date in dates:
        # 筛选单截面期数据
        df = data[data['trade_date'] == date].copy()
        # 过滤样本量不足的截面
        if len(df) < 30:
            continue
        # 剔除因子或收益率缺失的样本
        df = df.dropna(subset=[factor_col, 'return_t1'])

        # 构建OLS回归模型
        y = df['return_t1']  # 因变量：下期收益率
        X = sm.add_constant(df[factor_col])  # 自变量：因子值+常数项
        model = sm.OLS(y, X)

        try:
            result = model.fit()
        except:
            continue  # 跳过拟合失败的截面期

        result_list.append({
            'trade_date': date,
            'factor_return': result.params[factor_col],  # 因子收益率（回归系数）
            't_value': result.tvalues[factor_col]  # t值（显著性指标）
        })

    return pd.DataFrame(result_list)


# 分别测试PB和市值因子
pb_ols_result = ols_factor_test(data, factor_col='pb')
mv_ols_result = ols_factor_test(data, factor_col='total_mv')


def evaluate_factor(result_df, factor_name):
    """计算文档规定的因子评价指标"""
    t_values = result_df['t_value']
    factor_returns = result_df['factor_return']

    # a) t值序列绝对值平均值（因子显著性）
    abs_t_mean = np.abs(t_values).mean()
    # b) t值绝对值>2的占比（显著性稳定性，文档核心指标）
    abs_t_gt2_ratio = np.mean(np.abs(t_values) > 2)
    # c) 因子收益率均值及均值t检验（收益方向与显著性）
    return_mean = factor_returns.mean()
    return_t_test = stats.ttest_1samp(factor_returns, 0)[0]  # 零假设：均值为0
    # d) t均值绝对值 / t标准差（稳健性指标）
    t_mean_abs = np.abs(t_values.mean())
    t_std = t_values.std()
    t_ratio = t_mean_abs / t_std if t_std != 0 else 0

    return {
        '因子': factor_name,
        '|t|均值': round(abs_t_mean, 4),
        '|t|>2占比': round(abs_t_gt2_ratio, 4),
        't均值': round(t_values.mean(), 4),
        't均值/t标准差': round(t_ratio, 4),
        '因子收益率均值': round(return_mean, 6),
        '因子收益率序列t检验': round(return_t_test, 4)
    }


# 生成评价表格
pb_eval = evaluate_factor(pb_ols_result, factor_name='PB')
mv_eval = evaluate_factor(mv_ols_result, factor_name='total_mv')
eval_table = pd.DataFrame([pb_eval, mv_eval])

# 因子收益率时序图
fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), sharex=True)
# PB因子
ax1.plot(pb_ols_result['trade_date'], pb_ols_result['factor_return'], label='PB因子收益率', color='blue')
ax1.axhline(y=0, color='r', linestyle='--', alpha=0.3)
ax1.set_title('PB因子收益率时序')
ax1.legend()
# 市值因子
ax2.plot(mv_ols_result['trade_date'], mv_ols_result['factor_return'], label='市值因子收益率', color='green')
ax2.axhline(y=0, color='r', linestyle='--', alpha=0.3)
ax2.set_title('total_mv因子收益率时序')
ax2.legend()
plt.tight_layout()
plt.show()

# t值分布直方图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
# PB因子t值
sns.histplot(pb_ols_result['t_value'], ax=ax1, kde=True, color='blue')
ax1.axvline(x=2, color='r', linestyle='--', alpha=0.5)
ax1.axvline(x=-2, color='r', linestyle='--', alpha=0.5)
ax1.set_title('PB因子t值分布')
# 市值因子t值
sns.histplot(mv_ols_result['t_value'], ax=ax2, kde=True, color='green')
ax2.axvline(x=2, color='r', linestyle='--', alpha=0.5)
ax2.axvline(x=-2, color='r', linestyle='--', alpha=0.5)
ax2.set_title('total_mv因子t值分布')
plt.tight_layout()
plt.show()

# 评价指标对比图
fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
# |t|均值对比
sns.barplot(x='因子', y='|t|均值', data=eval_table, ax=ax1)
ax1.set_title('因子|t|均值对比（越高越显著）')
# |t|>2占比对比
sns.barplot(x='因子', y='|t|>2占比', data=eval_table, ax=ax2)
ax2.set_title('因子|t|>2占比对比（越高越稳定）')
plt.tight_layout()
plt.show()


print("\nOLS单因子测试评价结果：")
print(eval_table)

