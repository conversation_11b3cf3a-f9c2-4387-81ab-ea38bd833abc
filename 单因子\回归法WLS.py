import pandas as pd
import numpy as np
import h5py
from scipy.stats import rankdata, ttest_ind
import statsmodels.api as sm
from statsmodels.regression.rolling import RollingOLS
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import warnings
warnings.filterwarnings('ignore')
plt.rcParams.update({
    "font.size": 20,
    "mathtext.fontset": "cm",
    "font.sans-serif": ["SimHei"],
    "axes.unicode_minus": False,
    "figure.dpi": 200
})

merged_data = pd.read_hdf(r"C:\Users\<USER>\Desktop\金元顺安\单因子\data\merged_data2.h5", 'data')


def wls_factor_test(data, factor_col, weight_col='total_mv'):
    dates = data['trade_date'].unique()
    result_list = []

    for date in dates:
        df = data[data['trade_date'] == date].copy()
        if len(df) < 30:
            continue

        # 因变量：下期收益率（return_t1）
        y = df['return_t1']
        # 自变量：因子（添加常数项）
        X = sm.add_constant(df[factor_col])
        # 权重：流通市值的平方根（参考Barra手册）
        weights = np.sqrt(df[weight_col])

        # WLS回归
        model = sm.WLS(y, X, weights=weights)
        result = model.fit()

        factor_coef = result.params[factor_col]  # 因子收益率（回归系数）
        factor_t = result.tvalues[factor_col]  # t值
        result_list.append({
            'trade_date': date,
            'factor_return': factor_coef,
            't_value': factor_t
        })

    return pd.DataFrame(result_list)


pb_result = wls_factor_test(merged_data, factor_col='pb')
mv_result = wls_factor_test(merged_data, factor_col='total_mv')