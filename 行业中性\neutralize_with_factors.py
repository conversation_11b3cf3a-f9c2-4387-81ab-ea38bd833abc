import pandas as pd
import numpy as np
import h5py
from statsmodels.api import OLS

# 读取数据
def read_data():
    # 读取因子数据，设置日期和股票代码为索引
    with h5py.File('data/factor.h5', 'r') as f:
        df_factors = pd.DataFrame()
        for key in f.keys():
            df_factors[key] = f[key][:]
        # 假设 h5 文件中存储了索引信息，若没有则需手动处理
        # 这里暂时假设读取后的数据已经正确设置了 trade_date 和 ts_code 为索引
        # 实际使用时可能需要根据 h5 文件结构调整
        df_factors = pd.read_hdf('data/factor.h5')

    # 读取股票数据
    df_stock = pd.read_hdf('data/merged_data818.h5')

    # 读取行业数据
    df_industry = pd.read_excel('data/swind.xlsx')
    return df_stock, df_factors, df_industry

# 合并数据
def merge_data(df_stock, df_factors):
    # 将因子数据与股票数据合并，按日期和股票代码对齐
    df_stock['trade_date'] = pd.to_datetime(df_stock['trade_date'])
    df_stock = df_stock.set_index(['trade_date', 'ts_code'])
    merged_df = df_stock.join(df_factors, how='inner')
    return merged_df.reset_index()

# 行业中性化处理
def neutralize_by_industry(merged_df, df_industry):
    # 合并行业数据，只保留行业一级代码
    full_df = pd.merge(merged_df, df_industry[['ts_code', 'l1_code']], on='ts_code', how='left')
    
    # 需要中性化的因子列
    factor_columns = ['pb', 'total_mv']
    industry_column = 'l1_code'
    
    # 创建行业虚拟变量
    dummies = pd.get_dummies(full_df[industry_column])
    
    neutralized_df = full_df.copy()
    for factor in factor_columns:
        # 运行回归并获取残差
        model = OLS(full_df[factor], dummies)
        results = model.fit()
        neutralized_df[f'{factor}_neutralized'] = results.resid
    
    return neutralized_df

if __name__ == '__main__':
    df_stock, df_factors, df_industry = read_data()
    merged_df = merge_data(df_stock, df_factors)
    neutralized_df = neutralize_by_industry(merged_df, df_industry)
    
    # 保存中性化后的因子
    neutralized_df[['trade_date', 'ts_code', 'pb_neutralized', 'total_mv_neutralized']].to_hdf('data/neutralized_factors.h5', key='neutralized_factors', mode='w')
    print('行业中性化处理完成，结果已保存到 data/neutralized_factors.h5')